package com.newt.anime.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Send
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.newt.anime.data.models.AvailableEmojis

@Composable
fun ChatOverlay(
    onSendMessage: (String) -> Unit,
    onSendEmoji: (String) -> Unit,
    chatMessages: List<com.newt.anime.data.models.ChatMessage> = emptyList(),
    showControls: Boolean = true,
    modifier: Modifier = Modifier
) {
    var messageText by remember { mutableStateOf("") }
    var isChatExpanded by remember { mutableStateOf(false) }
    var isEmojiExpanded by remember { mutableStateOf(false) }
    val keyboardController = LocalSoftwareKeyboardController.current

    Box(modifier = modifier) {

        // نافذة الدردشة في اليسار
        AnimatedVisibility(
            visible = isChatExpanded,
            modifier = Modifier
                .align(Alignment.CenterStart)
                .padding(16.dp),
            enter = slideInHorizontally(initialOffsetX = { -it }) + fadeIn(),
            exit = slideOutHorizontally(targetOffsetX = { -it }) + fadeOut()
        ) {
            ChatWindow(
                messageText = messageText,
                onMessageChange = { messageText = it },
                onSendMessage = {
                    if (messageText.isNotBlank()) {
                        onSendMessage(messageText)
                        messageText = ""
                        keyboardController?.hide()
                        isChatExpanded = false // إغلاق النافذة تلقائياً
                    }
                },
                onClose = { isChatExpanded = false },
                chatMessages = chatMessages
            )
        }

        // نافذة الإيموجي
        AnimatedVisibility(
            visible = isEmojiExpanded,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(16.dp),
            enter = slideInVertically(initialOffsetY = { -it }) + fadeIn(),
            exit = slideOutVertically(targetOffsetY = { -it }) + fadeOut()
        ) {
            EmojiPanel(
                onEmojiClick = { emoji ->
                    onSendEmoji(emoji)
                    isEmojiExpanded = false
                },
                onClose = { isEmojiExpanded = false }
            )
        }

        // أزرار الدردشة والإيموجي في اليسار - تظهر فقط مع أزرار التحكم
        if (!isChatExpanded && !isEmojiExpanded && showControls) {
            Column(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // زر الدردشة
                FloatingActionButton(
                    onClick = { isChatExpanded = true },
                    modifier = Modifier.size(56.dp),
                    containerColor = MaterialTheme.colorScheme.primary
                ) {
                    Text(
                        text = "💬",
                        fontSize = 24.sp
                    )
                }

                // زر الإيموجي
                FloatingActionButton(
                    onClick = { isEmojiExpanded = true },
                    modifier = Modifier.size(56.dp),
                    containerColor = MaterialTheme.colorScheme.secondary
                ) {
                    Text(
                        text = "😂",
                        fontSize = 24.sp
                    )
                }
            }
        }
    }
}

@Composable
private fun EmojiButton(
    emoji: String,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(40.dp)
            .background(
                Color.White.copy(alpha = 0.2f),
                CircleShape
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = emoji,
            fontSize = 20.sp
        )
    }
}

@Composable
private fun EmojiPanel(
    onEmojiClick: (String) -> Unit,
    onClose: () -> Unit
) {
    Card(
        modifier = Modifier.wrapContentSize(),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.8f)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // عنوان مع زر الإغلاق
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "😂 الإيموجي",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "✕",
                    color = Color.White,
                    fontSize = 16.sp,
                    modifier = Modifier
                        .clickable { onClose() }
                        .padding(4.dp)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // شبكة الإيموجي
            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                modifier = Modifier.wrapContentSize(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(AvailableEmojis.ALL) { emoji ->
                    Box(
                        modifier = Modifier
                            .size(60.dp)
                            .background(
                                Color.White.copy(alpha = 0.1f),
                                RoundedCornerShape(12.dp)
                            )
                            .clickable { onEmojiClick(emoji) },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = emoji,
                            fontSize = 28.sp
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ChatWindow(
    messageText: String,
    onMessageChange: (String) -> Unit,
    onSendMessage: () -> Unit,
    onClose: () -> Unit,
    chatMessages: List<com.newt.anime.data.models.ChatMessage> = emptyList()
) {
    Card(
        modifier = Modifier
            .width(280.dp)
            .height(200.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.8f)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp)
        ) {
            // عنوان الدردشة مع زر الإغلاق
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "💬 الدردشة",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "✕",
                    color = Color.White,
                    fontSize = 16.sp,
                    modifier = Modifier
                        .clickable { onClose() }
                        .padding(4.dp)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // قائمة الرسائل
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .background(
                        Color.White.copy(alpha = 0.1f),
                        RoundedCornerShape(8.dp)
                    )
                    .padding(8.dp),
                reverseLayout = true
            ) {
                if (chatMessages.isEmpty()) {
                    item {
                        Box(
                            modifier = Modifier.fillMaxWidth(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "لا توجد رسائل...",
                                color = Color.White.copy(alpha = 0.7f),
                                fontSize = 12.sp
                            )
                        }
                    }
                } else {
                    items(chatMessages.takeLast(20).reversed()) { message ->
                        SimpleChatMessageItem(message = message)
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // حقل إدخال الرسالة
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedTextField(
                    value = messageText,
                    onValueChange = onMessageChange,
                    modifier = Modifier.weight(1f),
                    placeholder = {
                        Text(
                            text = "اكتب رسالة...",
                            fontSize = 12.sp
                        )
                    },
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
                    keyboardActions = KeyboardActions(onSend = { onSendMessage() }),
                    singleLine = true,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = Color.White,
                        unfocusedTextColor = Color.White,
                        focusedBorderColor = Color.White,
                        unfocusedBorderColor = Color.White.copy(alpha = 0.5f)
                    )
                )

                Spacer(modifier = Modifier.width(8.dp))

                IconButton(
                    onClick = onSendMessage,
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            MaterialTheme.colorScheme.primary,
                            CircleShape
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.Send,
                        contentDescription = "إرسال",
                        tint = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun SimpleChatMessageItem(
    message: com.newt.anime.data.models.ChatMessage
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = message.userName,
                    color = getUserColor(message.userId, ""),
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = formatTime(message.timestamp),
                    color = Color.White.copy(alpha = 0.7f),
                    fontSize = 9.sp
                )
            }
            Spacer(modifier = Modifier.height(2.dp))
            Text(
                text = message.message,
                color = Color.White,
                fontSize = 12.sp
            )
        }
    }
}

private fun formatTime(timestamp: Long): String {
    val now = System.currentTimeMillis()
    val diff = now - timestamp
    return when {
        diff < 60000 -> "الآن"
        diff < 3600000 -> "${diff / 60000}د"
        else -> "${diff / 3600000}س"
    }
}

// تحديد لون المستخدم
private fun getUserColor(userId: String, currentUserId: String): Color {
    return when {
        userId == currentUserId -> Color(0xFFFFD700) // ذهبي للمالك
        else -> {
            // ألوان مختلفة للمستخدمين الآخرين
            val colors = listOf(
                Color(0xFF00BCD4), // سماوي
                Color(0xFF4CAF50), // أخضر
                Color(0xFFFF9800), // برتقالي
                Color(0xFF9C27B0), // بنفسجي
                Color(0xFFE91E63), // وردي
                Color(0xFF2196F3), // أزرق
                Color(0xFFFF5722), // أحمر برتقالي
                Color(0xFF607D8B)  // رمادي مزرق
            )
            val index = userId.hashCode().let { if (it < 0) -it else it } % colors.size
            colors[index]
        }
    }
}
