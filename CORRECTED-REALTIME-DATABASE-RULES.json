{
  "rules": {
    // قواعد المستخدمين
    "users": {
      "$uid": {
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid"
      }
    },
    
    // قواعد المجموعات - مبسطة ومحسنة
    "groups": {
      // السماح بالقراءة والكتابة للمستخدمين المسجلين
      ".read": "auth != null",
      ".write": "auth != null",
      
      // فهرسة للبحث السريع
      ".indexOn": ["code", "ownerId", "name"],
      
      // قواعد كل مجموعة
      "$groupId": {
        ".read": "auth != null",
        ".write": "auth != null",
        
        // معلومات المجموعة الأساسية
        "name": {
          ".read": "auth != null",
          ".write": "auth != null"
        },
        "code": {
          ".read": "auth != null",
          ".write": "auth != null"
        },
        "ownerId": {
          ".read": "auth != null",
          ".write": "auth != null"
        },
        "ownerName": {
          ".read": "auth != null",
          ".write": "auth != null"
        },
        "createdAt": {
          ".read": "auth != null",
          ".write": "auth != null"
        },
        
        // الأعضاء
        "members": {
          ".read": "auth != null",
          ".write": "auth != null",
          "$memberId": {
            ".read": "auth != null",
            ".write": "auth != null"
          }
        },
        
        // الفيديو الحالي - مهم جداً للتزامن
        "currentVideo": {
          ".read": "auth != null",
          ".write": "auth != null",
          
          // تفاصيل الفيديو
          "videoUrl": {
            ".read": "auth != null",
            ".write": "auth != null"
          },
          "title": {
            ".read": "auth != null",
            ".write": "auth != null"
          },
          "isPlaying": {
            ".read": "auth != null",
            ".write": "auth != null"
          },
          "currentPosition": {
            ".read": "auth != null",
            ".write": "auth != null"
          },
          "lastUpdated": {
            ".read": "auth != null",
            ".write": "auth != null"
          }
        }
      }
    }
  }
}
