# دليل توقيع APK للنشر

## نظرة عامة
لنشر التطبيق على Google Play Store أو أي متجر تطبيقات، يجب توقيع APK بمفتاح خاص. هذا الدليل يوضح كيفية إنشاء مفتاح التوقيع وتوقيع APK.

## الخطوة 1: إنشاء مفتاح التوقيع (Keystore)

### باستخدام Android Studio
1. افتح Android Studio
2. اذهب إلى `Build` > `Generate Signed Bundle / APK`
3. اختر `APK`
4. انقر على `Create new...` لإنشاء keystore جديد
5. املأ المعلومات المطلوبة:
   - **Keystore path**: مسار حفظ الملف (مثل: `anime-app-keystore.jks`)
   - **Password**: كلمة مرور قوية للـ keystore
   - **Key alias**: اسم المفتاح (مثل: `anime-app-key`)
   - **Key password**: كلمة مرور للمفتاح
   - **Validity**: مدة صلاحية المفتاح (25 سنة موصى بها)
   - **Certificate**: معلومات الشهادة (الاسم، المنظمة، إلخ)

### باستخدام سطر الأوامر
```bash
keytool -genkey -v -keystore anime-app-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias anime-app-key
```

## الخطوة 2: تكوين Gradle للتوقيع

### إنشاء ملف gradle.properties (محلي)
أنشئ ملف `gradle.properties` في مجلد المشروع الرئيسي:
```properties
MYAPP_UPLOAD_STORE_FILE=anime-app-keystore.jks
MYAPP_UPLOAD_KEY_ALIAS=anime-app-key
MYAPP_UPLOAD_STORE_PASSWORD=your_keystore_password
MYAPP_UPLOAD_KEY_PASSWORD=your_key_password
```

### تعديل app/build.gradle.kts
أضف إعدادات التوقيع:
```kotlin
android {
    signingConfigs {
        create("release") {
            storeFile = file(project.findProperty("MYAPP_UPLOAD_STORE_FILE") as String)
            storePassword = project.findProperty("MYAPP_UPLOAD_STORE_PASSWORD") as String
            keyAlias = project.findProperty("MYAPP_UPLOAD_KEY_ALIAS") as String
            keyPassword = project.findProperty("MYAPP_UPLOAD_KEY_PASSWORD") as String
        }
    }
    
    buildTypes {
        release {
            isMinifyEnabled = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }
    }
}
```

## الخطوة 3: بناء APK موقع

### باستخدام Gradle
```bash
./gradlew assembleRelease
```

### باستخدام Android Studio
1. اذهب إلى `Build` > `Generate Signed Bundle / APK`
2. اختر `APK`
3. اختر الـ keystore المُنشأ
4. أدخل كلمات المرور
5. اختر `release` build variant
6. انقر على `Finish`

## الخطوة 4: التحقق من التوقيع

```bash
# التحقق من التوقيع
jarsigner -verify -verbose -certs your-app-release.apk

# عرض معلومات الشهادة
keytool -printcert -jarfile your-app-release.apk
```

## الخطوة 5: تحسين APK (اختياري)

### استخدام zipalign
```bash
zipalign -v 4 your-app-release-unaligned.apk your-app-release.apk
```

### التحقق من zipalign
```bash
zipalign -c -v 4 your-app-release.apk
```

## نصائح مهمة للأمان

### حماية الـ Keystore
1. **احتفظ بنسخة احتياطية**: احفظ الـ keystore في مكان آمن
2. **لا تشارك كلمات المرور**: لا تضع كلمات المرور في Git
3. **استخدم متغيرات البيئة**: للحماية الإضافية
4. **احفظ معلومات الشهادة**: ستحتاجها لتحديثات التطبيق

### ملف .gitignore
تأكد من إضافة هذه الملفات لـ .gitignore:
```
*.jks
*.keystore
gradle.properties
local.properties
```

## استكشاف الأخطاء

### خطأ "keystore was tampered with"
- تحقق من كلمة مرور الـ keystore
- تأكد من سلامة ملف الـ keystore

### خطأ "key was created with errors"
- تحقق من كلمة مرور المفتاح
- تأكد من صحة alias المفتاح

### خطأ "zipalign not found"
- تأكد من تثبيت Android SDK build-tools
- أضف مسار build-tools للـ PATH

## سكريبت التوقيع التلقائي

```bash
#!/bin/bash
# sign-apk.sh

APK_PATH="app/build/outputs/apk/release/app-release-unsigned.apk"
SIGNED_APK_PATH="anime-app-release-signed.apk"
KEYSTORE_PATH="anime-app-keystore.jks"

echo "توقيع APK..."
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore $KEYSTORE_PATH $APK_PATH anime-app-key

echo "تحسين APK..."
zipalign -v 4 $APK_PATH $SIGNED_APK_PATH

echo "التحقق من التوقيع..."
jarsigner -verify -verbose -certs $SIGNED_APK_PATH

echo "APK موقع وجاهز للنشر: $SIGNED_APK_PATH"
```

## الخطوات التالية
1. إنشاء حساب Google Play Console
2. رفع APK الموقع
3. إعداد معلومات التطبيق
4. اختبار التطبيق على أجهزة مختلفة
5. نشر التطبيق
