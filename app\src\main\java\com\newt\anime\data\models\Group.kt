package com.newt.anime.data.models

data class Group(
    val id: String = "",
    val name: String = "",
    val code: String = "",
    val ownerId: String = "",
    val ownerName: String = "",
    val members: Map<String, Member> = emptyMap(),
    val currentVideo: VideoSession? = null,
    val presence: Map<String, Long> = emptyMap(), // المتصلين النشطين الحاليين
    val maxMembers: Int = 4, // الحد الأقصى للأعضاء (افتراضي 4 للمجاني)
    val createdAt: Long = System.currentTimeMillis()
) {
    // حساب عدد المتصلين النشطين الحاليين فقط
    fun getActiveViewersCount(): Int {
        val currentTime = System.currentTimeMillis()
        val fiveMinutesAgo = currentTime - (5 * 60 * 1000) // 5 دقائق

        return presence.values.count { timestamp ->
            timestamp > fiveMinutesAgo // متصل خلال آخر 5 دقائق
        }
    }

    // التحقق من إمكانية انضمام عضو جديد
    fun canAddNewMember(): Boolean {
        return members.size < maxMembers
    }

    // الحصول على عدد الأماكن المتبقية
    fun getRemainingSlots(): Int {
        return maxOf(0, maxMembers - members.size)
    }

    // التحقق من امتلاء المجموعة
    fun isFull(): Boolean {
        return members.size >= maxMembers
    }

    // الحصول على نص وصفي لحالة المجموعة
    fun getMembershipStatus(): String {
        return when {
            maxMembers == Int.MAX_VALUE -> "أعضاء غير محدودين"
            isFull() -> "مكتملة (${members.size}/${maxMembers})"
            else -> "${members.size}/${maxMembers} أعضاء"
        }
    }

    // الحصول على قائمة المتصلين النشطين مع أسمائهم
    fun getActiveViewers(): List<Member> {
        val currentTime = System.currentTimeMillis()
        val fiveMinutesAgo = currentTime - (5 * 60 * 1000) // 5 دقائق

        return presence.filter { (_, timestamp) ->
            timestamp > fiveMinutesAgo
        }.mapNotNull { (userId, _) ->
            members[userId] // الحصول على بيانات العضو
        }
    }
}

data class Member(
    val uid: String = "",
    val name: String = "",
    val joinedAt: Long = System.currentTimeMillis()
)

data class VideoSession(
    val videoUrl: String = "",
    val title: String = "",
    val isPlaying: Boolean = false,
    val currentPosition: Long = 0,
    val lastUpdated: Long = System.currentTimeMillis(),
    val syncCommand: String = "", // "play", "pause", "seek"
    val syncTimestamp: Long = System.currentTimeMillis(), // طابع زمني للتزامن الفوري
    val ownerAction: Boolean = false, // هل هذا إجراء من المالك؟
    val hasStarted: Boolean = false // هل بدأ المالك التشغيل مرة واحدة على الأقل؟
)
