{"logs": [{"outputFile": "com.newt.anime.app-mergeReleaseResources-62:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\85c261ec03eee0a4101b5d8231a63563\\transformed\\material3-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,292,407,524,627,725,840,977,1094,1249,1334,1434,1526,1627,1747,1869,1974,2118,2253,2390,2562,2694,2820,2945,3073,3166,3266,3394,3536,3635,3737,3846,3986,4127,4237,4339,4417,4512,4609,4717,4803,4889,4995,5075,5160,5268,5370,5474,5572,5660,5766,5872,5974,6096,6176,6283", "endColumns": "117,118,114,116,102,97,114,136,116,154,84,99,91,100,119,121,104,143,134,136,171,131,125,124,127,92,99,127,141,98,101,108,139,140,109,101,77,94,96,107,85,85,105,79,84,107,101,103,97,87,105,105,101,121,79,106,99", "endOffsets": "168,287,402,519,622,720,835,972,1089,1244,1329,1429,1521,1622,1742,1864,1969,2113,2248,2385,2557,2689,2815,2940,3068,3161,3261,3389,3531,3630,3732,3841,3981,4122,4232,4334,4412,4507,4604,4712,4798,4884,4990,5070,5155,5263,5365,5469,5567,5655,5761,5867,5969,6091,6171,6278,6378"}, "to": {"startLines": "108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9005,9123,9242,9357,9474,9577,9675,9790,9927,10044,10199,10284,10384,10476,10577,10697,10819,10924,11068,11203,11340,11512,11644,11770,11895,12023,12116,12216,12344,12486,12585,12687,12796,12936,13077,13187,13289,13367,13462,13559,13667,13753,13839,13945,14025,14110,14218,14320,14424,14522,14610,14716,14822,14924,15046,15126,15233", "endColumns": "117,118,114,116,102,97,114,136,116,154,84,99,91,100,119,121,104,143,134,136,171,131,125,124,127,92,99,127,141,98,101,108,139,140,109,101,77,94,96,107,85,85,105,79,84,107,101,103,97,87,105,105,101,121,79,106,99", "endOffsets": "9118,9237,9352,9469,9572,9670,9785,9922,10039,10194,10279,10379,10471,10572,10692,10814,10919,11063,11198,11335,11507,11639,11765,11890,12018,12111,12211,12339,12481,12580,12682,12791,12931,13072,13182,13284,13362,13457,13554,13662,13748,13834,13940,14020,14105,14213,14315,14419,14517,14605,14711,14817,14919,15041,15121,15228,15328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f341d0dad5e087546904e2a03fd632f3\\transformed\\play-services-base-18.1.0\\res\\values-zu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,472,603,703,867,992,1112,1218,1374,1480,1641,1768,1922,2075,2132,2197", "endColumns": "106,171,130,99,163,124,119,105,155,105,160,126,153,152,56,64,80", "endOffsets": "299,471,602,702,866,991,1111,1217,1373,1479,1640,1767,1921,2074,2131,2196,2277"}, "to": {"startLines": "30,31,32,33,34,35,36,37,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1920,2031,2207,2342,2446,2614,2743,2867,3109,3269,3379,3544,3675,3833,3990,4051,4120", "endColumns": "110,175,134,103,167,128,123,109,159,109,164,130,157,156,60,68,84", "endOffsets": "2026,2202,2337,2441,2609,2738,2862,2972,3264,3374,3539,3670,3828,3985,4046,4115,4200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4366d762d4e78881da860b75d1c4c59f\\transformed\\play-services-basement-18.4.0\\res\\values-zu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "38", "startColumns": "4", "startOffsets": "2977", "endColumns": "131", "endOffsets": "3104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dc398bc18186089f1a6c4c36afb914b8\\transformed\\foundation-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,146", "endColumns": "90,91", "endOffsets": "141,233"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "16367,16458", "endColumns": "90,91", "endOffsets": "16453,16545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6436519ada9dddee292e4f473475ac25\\transformed\\exoplayer-ui-2.19.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,522,726,823,914,998,1092,1187,1259,1330,1429,1529,1596,1660,1726,1806,1924,2048,2166,2241,2333,2407,2480,2574,2662,2725,2794,2847,2905,2957,3018,3078,3140,3205,3273,3343,3402,3470,3537,3605,3659,3727,3814,3901", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,96,90,83,93,94,71,70,98,99,66,63,65,79,117,123,117,74,91,73,72,93,87,62,68,52,57,51,60,59,61,64,67,69,58,67,66,67,53,67,86,86,54", "endOffsets": "288,517,721,818,909,993,1087,1182,1254,1325,1424,1524,1591,1655,1721,1801,1919,2043,2161,2236,2328,2402,2475,2569,2657,2720,2789,2842,2900,2952,3013,3073,3135,3200,3268,3338,3397,3465,3532,3600,3654,3722,3809,3896,3951"}, "to": {"startLines": "2,11,15,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,572,4617,4714,4805,4889,4983,5078,5150,5221,5320,5420,5487,5551,5617,5697,5815,5939,6057,6132,6224,6298,6371,6465,6553,6616,7340,7393,7451,7503,7564,7624,7686,7751,7819,7889,7948,8016,8083,8151,8205,8273,8360,8447", "endLines": "10,14,18,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "endColumns": "17,12,12,96,90,83,93,94,71,70,98,99,66,63,65,79,117,123,117,74,91,73,72,93,87,62,68,52,57,51,60,59,61,64,67,69,58,67,66,67,53,67,86,86,54", "endOffsets": "338,567,771,4709,4800,4884,4978,5073,5145,5216,5315,5415,5482,5546,5612,5692,5810,5934,6052,6127,6219,6293,6366,6460,6548,6611,6680,7388,7446,7498,7559,7619,7681,7746,7814,7884,7943,8011,8078,8146,8200,8268,8355,8442,8497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf7927868eeddad3b4319378a3201862\\transformed\\browser-1.4.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,275,387", "endColumns": "111,107,111,111", "endOffsets": "162,270,382,494"}, "to": {"startLines": "48,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "4205,8502,8610,8722", "endColumns": "111,107,111,111", "endOffsets": "4312,8605,8717,8829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5f82ddb128f9d8fc1f894d4feaff6b09\\transformed\\exoplayer-core-2.19.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,183,245,314,391,471,560,641", "endColumns": "68,58,61,68,76,79,88,80,68", "endOffsets": "119,178,240,309,386,466,555,636,705"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6685,6754,6813,6875,6944,7021,7101,7190,7271", "endColumns": "68,58,61,68,76,79,88,80,68", "endOffsets": "6749,6808,6870,6939,7016,7096,7185,7266,7335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8f761d99cc674c08e5b049b2a7bbf06\\transformed\\core-1.16.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "21,22,23,24,25,26,27,173", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1013,1111,1215,1314,1417,1523,1630,15993", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "1106,1210,1309,1412,1518,1625,1738,16089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95a85adbd9a0258061c7c940036f2eec\\transformed\\ui-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,282,387,492,582,664,753,846,929,1017,1105,1181,1262,1338,1413,1492,1562", "endColumns": "94,81,104,104,89,81,88,92,82,87,87,75,80,75,74,78,69,123", "endOffsets": "195,277,382,487,577,659,748,841,924,1012,1100,1176,1257,1333,1408,1487,1557,1681"}, "to": {"startLines": "28,29,49,50,51,106,107,165,166,167,168,169,170,171,172,174,175,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1743,1838,4317,4422,4527,8834,8916,15333,15426,15509,15597,15685,15761,15842,15918,16094,16173,16243", "endColumns": "94,81,104,104,89,81,88,92,82,87,87,75,80,75,74,78,69,123", "endOffsets": "1833,1915,4417,4522,4612,8911,9000,15421,15504,15592,15680,15756,15837,15913,15988,16168,16238,16362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9c9112f3b5828659e41d3d6d877dc27\\transformed\\credentials-1.2.0-rc01\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,122", "endOffsets": "164,287"}, "to": {"startLines": "19,20", "startColumns": "4,4", "startOffsets": "776,890", "endColumns": "113,122", "endOffsets": "885,1008"}}]}]}