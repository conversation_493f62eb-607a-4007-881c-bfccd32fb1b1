{
  "rules": {
    // ==========================================
    // قواعد الإنتاج المحسنة للأداء
    // Production Optimized Rules
    // ==========================================
    
    // المستخدمين - Users
    "users": {
      "$uid": {
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid",
        ".validate": "newData.hasChildren(['uid', 'email', 'displayName'])",
        
        "uid": {
          ".validate": "newData.val() === auth.uid"
        },
        "email": {
          ".validate": "newData.isString() && newData.val().contains('@')"
        },
        "displayName": {
          ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 50"
        },
        "createdAt": {
          ".validate": "newData.isNumber()"
        }
      }
    },
    
    // المجموعات - Groups
    "groups": {
      ".read": "auth != null",
      ".write": "auth != null",
      ".indexOn": ["code", "ownerId", "createdAt"],
      
      "$groupId": {
        ".read": "auth != null",
        ".write": "auth != null",
        
        // معلومات أساسية
        "id": { ".validate": "newData.val() === $groupId" },
        "name": { ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 50" },
        "code": { ".validate": "newData.isString() && newData.val().length === 6" },
        "ownerId": { ".validate": "newData.isString()" },
        "ownerName": { ".validate": "newData.isString() && newData.val().length <= 50" },
        "createdAt": { ".validate": "newData.isNumber()" },
        
        // الأعضاء - محسن للأداء
        "members": {
          ".read": "auth != null",
          ".write": "auth != null",
          "$memberId": {
            ".validate": "newData.hasChildren(['uid', 'name'])",
            "uid": { ".validate": "newData.val() === $memberId" },
            "name": { ".validate": "newData.isString() && newData.val().length <= 50" },
            "joinedAt": { ".validate": "newData.isNumber()" }
          }
        },
        
        // الفيديو الحالي - محسن للتزامن
        "currentVideo": {
          ".read": "auth != null",
          ".write": "auth != null",
          ".indexOn": ["syncTimestamp"],
          
          "videoUrl": { ".validate": "newData.isString()" },
          "title": { ".validate": "newData.isString() && newData.val().length <= 200" },
          "isPlaying": { ".validate": "newData.isBoolean()" },
          "currentPosition": { ".validate": "newData.isNumber() && newData.val() >= 0" },
          "lastUpdated": { ".validate": "newData.isNumber()" },
          "syncCommand": { 
            ".validate": "newData.isString() && (newData.val() === 'play' || newData.val() === 'pause' || newData.val() === 'seek' || newData.val() === '')" 
          },
          "syncTimestamp": { ".validate": "newData.isNumber()" },
          "ownerAction": { ".validate": "newData.isBoolean()" },
          "hasStarted": { ".validate": "newData.isBoolean()" }
        },
        
        // حالة الاتصال - محسن للذاكرة
        "presence": {
          ".read": "auth != null",
          ".write": "auth != null",
          "$userId": {
            ".write": "$userId === auth.uid",
            ".validate": "newData.isNumber() || !newData.exists()"
          }
        },
        
        // الدردشة - محدود بـ 100 رسالة
        "chat": {
          ".read": "auth != null",
          ".write": "auth != null",
          ".indexOn": ["timestamp"],
          
          "$messageId": {
            ".validate": "newData.hasChildren(['userId', 'message', 'timestamp']) && newData.child('userId').val() === auth.uid",
            
            "id": { ".validate": "newData.val() === $messageId" },
            "userId": { ".validate": "newData.val() === auth.uid" },
            "userName": { ".validate": "newData.isString() && newData.val().length <= 50" },
            "message": { ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 500" },
            "timestamp": { ".validate": "newData.isNumber()" },
            "type": { ".validate": "newData.val() === 'TEXT' || newData.val() === 'EMOJI'" }
          }
        },
        
        // ردود الفعل - محدود بـ 50 تفاعل
        "reactions": {
          ".read": "auth != null",
          ".write": "auth != null",
          ".indexOn": ["timestamp"],
          
          "$reactionId": {
            ".validate": "newData.hasChildren(['userId', 'emoji', 'timestamp']) && newData.child('userId').val() === auth.uid",
            
            "id": { ".validate": "newData.val() === $reactionId" },
            "userId": { ".validate": "newData.val() === auth.uid" },
            "userName": { ".validate": "newData.isString() && newData.val().length <= 50" },
            "emoji": { 
              ".validate": "newData.isString() && (newData.val() === '❤️' || newData.val() === '😂' || newData.val() === '😢' || newData.val() === '😡' || newData.val() === '😱' || newData.val() === '🛑')" 
            },
            "timestamp": { ".validate": "newData.isNumber()" }
          }
        },
        
        // إعدادات المجموعة - مالك فقط
        "settings": {
          ".read": "auth != null",
          ".write": "root.child('groups').child($groupId).child('ownerId').val() === auth.uid",
          
          "allowChat": { ".validate": "newData.isBoolean()" },
          "allowReactions": { ".validate": "newData.isBoolean()" },
          "maxMembers": { ".validate": "newData.isNumber() && newData.val() >= 2 && newData.val() <= 50" },
          "isPrivate": { ".validate": "newData.isBoolean()" }
        }
      }
    },
    
    // إحصائيات عامة - قراءة فقط
    "stats": {
      ".read": "auth != null",
      ".write": false
    }
  }
}
