@echo off
echo ========================================
echo    Firebase Rules Deployment Tool
echo ========================================
echo.

echo اختر نوع القواعد للنشر:
echo [1] قواعد شاملة ومفصلة (FIREBASE-REALTIME-DATABASE-RULES.json)
echo [2] قواعد مبسطة للتطوير (FIREBASE-RULES-SIMPLE.json)
echo [3] قواعد محسنة للإنتاج (FIREBASE-RULES-PRODUCTION.json)
echo [4] إلغاء
echo.

set /p choice="أدخل اختيارك (1-4): "

if "%choice%"=="1" (
    set rules_file=FIREBASE-REALTIME-DATABASE-RULES.json
    set rules_name=الشاملة والمفصلة
) else if "%choice%"=="2" (
    set rules_file=FIREBASE-RULES-SIMPLE.json
    set rules_name=المبسطة للتطوير
) else if "%choice%"=="3" (
    set rules_file=FIREBASE-RULES-PRODUCTION.json
    set rules_name=المحسنة للإنتاج
) else if "%choice%"=="4" (
    echo تم الإلغاء.
    pause
    exit /b 0
) else (
    echo اختيار غير صحيح!
    pause
    exit /b 1
)

echo.
echo تم اختيار القواعد %rules_name%
echo الملف: %rules_file%
echo.

if not exist "%rules_file%" (
    echo خطأ: الملف %rules_file% غير موجود!
    pause
    exit /b 1
)

echo هل تريد المتابعة؟ (y/n)
set /p confirm=
if /i not "%confirm%"=="y" (
    echo تم الإلغاء.
    pause
    exit /b 0
)

echo.
echo [1/3] التحقق من Firebase CLI...
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Firebase CLI غير مثبت!
    echo يرجى تثبيته باستخدام: npm install -g firebase-tools
    pause
    exit /b 1
)

echo [2/3] التحقق من تسجيل الدخول...
firebase projects:list >nul 2>&1
if %errorlevel% neq 0 (
    echo يرجى تسجيل الدخول أولاً...
    firebase login
    if %errorlevel% neq 0 (
        echo فشل في تسجيل الدخول!
        pause
        exit /b 1
    )
)

echo [3/3] نشر القواعد...
echo.

:: إنشاء نسخة احتياطية
echo إنشاء نسخة احتياطية...
firebase database:get / > backup-rules-%date:~-4,4%%date:~-10,2%%date:~-7,2%.json 2>nul

:: نسخ الملف المختار إلى database.rules.json
copy "%rules_file%" database.rules.json >nul

:: نشر القواعد
firebase deploy --only database
if %errorlevel% neq 0 (
    echo فشل في نشر القواعد!
    pause
    exit /b 1
)

echo.
echo ========================================
echo       تم نشر القواعد بنجاح! ✅
echo ========================================
echo.
echo القواعد المنشورة: %rules_name%
echo الملف: %rules_file%
echo.
echo ملاحظات:
echo - تم إنشاء نسخة احتياطية
echo - يمكنك مراجعة القواعد في Firebase Console
echo - اختبر التطبيق للتأكد من عمل القواعد
echo.
pause
