package com.newt.anime.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.newt.anime.data.models.VideoSession
import kotlinx.coroutines.delay

@Composable
fun NewVideoPlayer(
    videoSession: VideoSession,
    isOwner: <PERSON><PERSON><PERSON>,
    onPlayPause: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var isLoading by remember { mutableStateOf(true) }
    var hasError by remember { mutableStateOf(false) }
    var showControls by remember { mutableStateOf(false) }
    var isPlaying by remember { mutableStateOf(videoSession.isPlaying) }
    var currentPosition by remember { mutableStateOf(0L) }
    var duration by remember { mutableStateOf(0L) }
    var videoView by remember { mutableStateOf<android.widget.VideoView?>(null) }

    // إخفاء التحكم تلقائياً بعد 3 ثواني
    LaunchedEffect(showControls) {
        if (showControls) {
            delay(3000)
            showControls = false
        }
    }

    // تحديث حالة التشغيل
    LaunchedEffect(videoSession.isPlaying) {
        isPlaying = videoSession.isPlaying
    }

    // تحديث بسيط للحالة
    LaunchedEffect(videoSession.isPlaying) {
        isPlaying = videoSession.isPlaying
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        if (videoSession.videoUrl.isNotEmpty()) {
            // مشغل الفيديو مع تحكم YouTube
            AndroidView(
                factory = { ctx ->
                    android.widget.VideoView(ctx).apply {
                        videoView = this
                        setVideoPath(videoSession.videoUrl)

                        setOnPreparedListener { mediaPlayer ->
                            android.util.Log.d("VideoPlayer", "Video prepared successfully")
                            isLoading = false
                            hasError = false
                            duration = mediaPlayer.duration.toLong()

                            // إعدادات التشغيل المستمر
                            mediaPlayer.isLooping = true
                            mediaPlayer.setVideoScalingMode(android.media.MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING)

                            // منع التوقف التلقائي
                            mediaPlayer.setWakeMode(context, android.os.PowerManager.PARTIAL_WAKE_LOCK)

                            // تشغيل تلقائي إذا مطلوب
                            if (videoSession.isPlaying) {
                                android.util.Log.d("VideoPlayer", "Auto-starting video...")
                                start()
                                isPlaying = true
                            }

                            android.util.Log.d("VideoPlayer", "Video ready - duration: $duration, isPlaying: $isPlaying")
                        }

                        setOnErrorListener { _, what, extra ->
                            isLoading = false
                            hasError = true
                            android.util.Log.e("NewVideoPlayer", "Error: what=$what, extra=$extra")
                            true
                        }

                        setOnCompletionListener {
                            // إعادة تشغيل تلقائي (Loop)
                            if (videoSession.isPlaying) {
                                start()
                                isPlaying = true
                            }
                        }

                        // معالج للحفاظ على التشغيل
                        setOnInfoListener { _, what, _ ->
                            when (what) {
                                android.media.MediaPlayer.MEDIA_INFO_BUFFERING_START -> {
                                    // بدء التخزين المؤقت - لا نوقف التشغيل
                                    false
                                }
                                android.media.MediaPlayer.MEDIA_INFO_BUFFERING_END -> {
                                    // انتهاء التخزين المؤقت - استمرار التشغيل
                                    if (videoSession.isPlaying && !isPlaying) {
                                        start()
                                        isPlaying = true
                                    }
                                    false
                                }
                                else -> false
                            }
                        }

                        // إظهار التحكم عند النقر (للمالك فقط)
                        setOnClickListener {
                            if (isOwner) {
                                showControls = !showControls
                                android.util.Log.d("VideoPlayer", "Controls toggled: $showControls")
                            }
                        }

                        // منع التحكم للمشاهدين
                        if (!isOwner) {
                            isClickable = false
                            isFocusable = false
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxSize()
                    .clickable(enabled = isOwner) {
                        showControls = !showControls
                    },
                update = { view ->
                    videoView = view
                    currentPosition = view.currentPosition.toLong()

                    // تحديث بسيط للحالة
                    if (view.isPlaying != isPlaying) {
                        isPlaying = view.isPlaying
                    }

                    // تزامن للمشاهدين فقط
                    if (!isOwner && videoSession.isPlaying != view.isPlaying) {
                        if (videoSession.isPlaying) {
                            view.start()
                        } else {
                            view.pause()
                        }
                    }
                }
            )
        }
        
        // مؤشر التحميل
        if (isLoading && videoSession.videoUrl.isNotEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.8f)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(
                        color = Color.Red,
                        strokeWidth = 4.dp,
                        modifier = Modifier.size(60.dp)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "جاري تحميل الفيديو...",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        
        // رسالة خطأ
        if (hasError) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.8f)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "❌",
                        fontSize = 48.sp,
                        color = Color.Red
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "خطأ في تحميل الفيديو",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "تحقق من رابط الفيديو أو الاتصال",
                        color = Color.White.copy(alpha = 0.7f),
                        fontSize = 14.sp
                    )
                }
            }
        }
        
        // مؤشر للمشاهدين
        if (!isOwner && videoSession.videoUrl.isNotEmpty()) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp)
                    .background(
                        Color.Black.copy(alpha = 0.7f),
                        RoundedCornerShape(20.dp)
                    )
                    .padding(horizontal = 12.dp, vertical = 6.dp)
            ) {
                Text(
                    text = "👁️ وضع المشاهدة",
                    color = Color.White,
                    fontSize = 12.sp
                )
            }
        }
        
        // تحكم YouTube للمالك
        if (isOwner && videoSession.videoUrl.isNotEmpty() && !isLoading && !hasError && showControls) {
            // تحكم YouTube في الأسفل
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .background(
                        androidx.compose.ui.graphics.Brush.verticalGradient(
                            colors = listOf(
                                Color.Transparent,
                                Color.Black.copy(alpha = 0.8f)
                            )
                        )
                    )
                    .padding(16.dp)
            ) {
                Column {
                    // شريط التقدم
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = formatTime(currentPosition),
                            color = Color.White,
                            fontSize = 12.sp,
                            modifier = Modifier.padding(end = 8.dp)
                        )

                        LinearProgressIndicator(
                            progress = if (duration > 0) currentPosition.toFloat() / duration.toFloat() else 0f,
                            modifier = Modifier
                                .weight(1f)
                                .height(4.dp),
                            color = Color.Red,
                            trackColor = Color.White.copy(alpha = 0.3f)
                        )

                        Text(
                            text = formatTime(duration),
                            color = Color.White,
                            fontSize = 12.sp,
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // أزرار التحكم البسيطة - 3 أزرار فقط مثل YouTube
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // تراجع 10 ثواني
                        SimpleControlButton(
                            icon = Icons.Default.KeyboardArrowLeft,
                            onClick = {
                                try {
                                    videoView?.let { view ->
                                        val newPosition = maxOf(0, view.currentPosition - 10000)
                                        view.seekTo(newPosition)
                                        android.util.Log.d("VideoPlayer", "Seek backward 10s to: $newPosition")
                                    }
                                } catch (e: Exception) {
                                    android.util.Log.e("VideoPlayer", "Error in seek backward: ${e.message}")
                                }
                            }
                        )

                        // تشغيل/إيقاف - الزر الوحيد في التطبيق
                        SimpleControlButton(
                            icon = if (isPlaying) Icons.Default.Close else Icons.Default.PlayArrow,
                            onClick = {
                                try {
                                    videoView?.let { view ->
                                        android.util.Log.d("VideoPlayer", "SINGLE Play/Pause button clicked. Current state: $isPlaying")

                                        if (isPlaying) {
                                            view.pause()
                                            isPlaying = false
                                            onPlayPause(false)
                                            android.util.Log.d("VideoPlayer", "Video paused successfully")
                                        } else {
                                            view.start()
                                            isPlaying = true
                                            onPlayPause(true)
                                            android.util.Log.d("VideoPlayer", "Video started successfully")
                                        }
                                    }
                                } catch (e: Exception) {
                                    android.util.Log.e("VideoPlayer", "Error in SINGLE play/pause button: ${e.message}")
                                    e.printStackTrace()
                                }
                            },
                            isMain = true
                        )

                        // تقديم 10 ثواني
                        SimpleControlButton(
                            icon = Icons.Default.KeyboardArrowRight,
                            onClick = {
                                try {
                                    videoView?.let { view ->
                                        val newPosition = minOf(view.duration, view.currentPosition + 10000)
                                        view.seekTo(newPosition)
                                        android.util.Log.d("VideoPlayer", "Seek forward 10s to: $newPosition")
                                    }
                                } catch (e: Exception) {
                                    android.util.Log.e("VideoPlayer", "Error in seek forward: ${e.message}")
                                }
                            }
                        )
                    }
                }
            }
        }

        // لا يوجد زر مركزي - فقط النقر على الفيديو لإظهار التحكم
        
        // رسالة عدم وجود فيديو
        if (videoSession.videoUrl.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "📺",
                        fontSize = 64.sp,
                        color = Color.White.copy(alpha = 0.5f)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "لا يوجد فيديو حالياً",
                        color = Color.White.copy(alpha = 0.7f),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                    if (isOwner) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "اضغط 'رفع فيديو' لبدء المشاهدة",
                            color = Color.White.copy(alpha = 0.5f),
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun SimpleControlButton(
    icon: ImageVector,
    onClick: () -> Unit,
    isMain: Boolean = false,
    modifier: Modifier = Modifier
) {
    val buttonSize = if (isMain) 64.dp else 48.dp
    val iconSize = if (isMain) 36.dp else 24.dp

    Box(
        modifier = modifier
            .size(buttonSize)
            .background(
                Color.Black.copy(alpha = 0.7f),
                CircleShape
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = Color.White,
            modifier = Modifier.size(iconSize)
        )
    }
}

private fun formatTime(timeMs: Long): String {
    val totalSeconds = timeMs / 1000
    val minutes = totalSeconds / 60
    val seconds = totalSeconds % 60
    return String.format("%d:%02d", minutes, seconds)
}
