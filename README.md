# تطبيق مشاهدة الفيديوهات الجماعية - Anime

تطبيق Android يتيح للمستخدمين إنشاء مجموعات لمشاهدة الفيديوهات بشكل متزامن من Google Drive.

## الميزات الرئيسية

### 🔐 نظام المصادقة
- تسجيل الدخول بالإيميل وكلمة المرور
- إنشاء حساب جديد
- تسجيل الخروج

### 👥 إدارة المجموعات
- إنشاء مجموعات جديدة مع كود دعوة فريد
- الانضمام للمجموعات باستخدام الكود
- عرض قائمة المجموعات التي ينتمي إليها المستخدم
- عرض أعضاء المجموعة

### 🎬 مشاهدة الفيديوهات المتزامنة
- إضافة روابط فيديوهات Google Drive
- التحكم في التشغيل/الإيقاف (للمالك فقط)
- مشاهدة متزامنة لجميع أعضاء المجموعة
- عرض حالة التشغيل في الوقت الفعلي

## التقنيات المستخدمة

### 🏗️ البنية التقنية
- **Kotlin** - لغة البرمجة الأساسية
- **Jetpack Compose** - لبناء واجهات المستخدم
- **Material 3** - نظام التصميم
- **MVVM Architecture** - معمارية التطبيق

### 🔥 Firebase Services
- **Firebase Authentication** - نظام المصادقة
- **Firebase Realtime Database** - قاعدة البيانات المتزامنة
- **Firebase Storage** - تخزين الملفات

### 📱 مكتبات Android
- **Navigation Compose** - التنقل بين الشاشات
- **ViewModel & LiveData** - إدارة حالة التطبيق
- **WebView** - عرض الفيديوهات
- **Coroutines** - البرمجة غير المتزامنة

## هيكل المشروع

```
app/src/main/java/com/newt/anime/
├── data/
│   ├── models/          # نماذج البيانات
│   │   ├── User.kt
│   │   └── Group.kt
│   └── repository/      # طبقة الوصول للبيانات
│       ├── AuthRepository.kt
│       └── GroupRepository.kt
├── ui/
│   ├── components/      # المكونات المشتركة
│   │   ├── VideoPlayer.kt
│   │   └── GroupDialogs.kt
│   ├── screens/         # شاشات التطبيق
│   │   ├── LoginScreen.kt
│   │   ├── SignUpScreen.kt
│   │   ├── HomeScreen.kt
│   │   └── GroupScreen.kt
│   ├── viewmodel/       # ViewModels
│   │   ├── AuthViewModel.kt
│   │   └── GroupViewModel.kt
│   └── theme/           # تصميم التطبيق
├── navigation/          # نظام التنقل
│   └── AnimeNavigation.kt
└── MainActivity.kt      # النشاط الرئيسي
```

## كيفية الاستخدام

### 1. تسجيل الدخول/إنشاء حساب
- افتح التطبيق
- أدخل بياناتك للدخول أو أنشئ حساب جديد

### 2. إنشاء مجموعة
- اضغط على "إنشاء مجموعة"
- أدخل اسم المجموعة
- احصل على كود المجموعة وشاركه مع الأصدقاء

### 3. الانضمام لمجموعة
- اضغط على "انضمام لمجموعة"
- أدخل كود المجموعة المستلم من المالك

### 4. مشاهدة الفيديوهات
- ادخل للمجموعة
- المالك يضيف رابط فيديو Google Drive
- المالك يتحكم في التشغيل/الإيقاف
- جميع الأعضاء يشاهدون بنفس التوقيت

## إعداد المشروع

### المتطلبات
- Android Studio Arctic Fox أو أحدث
- Android SDK 24 أو أحدث
- حساب Firebase

### خطوات الإعداد
1. استنسخ المشروع
2. افتح في Android Studio
3. أضف ملف `google-services.json` من Firebase Console
4. قم ببناء المشروع

### إعداد Firebase
1. أنشئ مشروع جديد في Firebase Console
2. فعّل Authentication (Email/Password)
3. فعّل Realtime Database
4. فعّل Storage
5. أضف التطبيق وحمّل ملف التكوين

## الحالة الحالية

✅ **مكتمل:**
- نظام المصادقة الكامل
- إنشاء والانضمام للمجموعات
- واجهة المستخدم الأساسية
- مشغل الفيديو الأساسي
- التزامن في الوقت الفعلي

🔄 **قيد التطوير:**
- تحسين مشغل الفيديو
- إضافة المزيد من تنسيقات الفيديو
- تحسين التصميم
- إضافة الإشعارات

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
