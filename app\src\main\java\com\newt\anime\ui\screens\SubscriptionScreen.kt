package com.newt.anime.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.newt.anime.data.models.SubscriptionTier
import com.newt.anime.ui.viewmodel.SubscriptionViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SubscriptionScreen(
    onNavigateBack: () -> Unit,
    subscriptionViewModel: SubscriptionViewModel = viewModel()
) {
    val uiState by subscriptionViewModel.uiState.collectAsState()
    val userSubscription by subscriptionViewModel.userSubscription.collectAsState()

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top Bar
        TopAppBar(
            title = { Text("💎 الاشتراكات") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "رجوع")
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // معلومات الاشتراك الحالي
            item {
                CurrentSubscriptionCard(
                    subscription = userSubscription
                )
            }

            // عنوان الخطط
            item {
                Text(
                    text = "🎯 اختر خطتك",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            // بطاقات الاشتراكات
            items(SubscriptionTier.values().toList()) { tier ->
                SubscriptionCard(
                    tier = tier,
                    isCurrentTier = userSubscription?.tier == tier && (userSubscription?.isValid() == true),
                    onSubscribe = {
                        // تفعيل الاشتراك مباشرة بدون حوار تأكيد
                        subscriptionViewModel.simulatePayment(tier)
                    },
                    isLoading = uiState.isLoading
                )
            }

            // معلومات إضافية
            item {
                InfoCard()
            }
        }
    }

    // تم إزالة حوار التأكيد - الاشتراك يفعل مباشرة

    // عرض الرسائل
    LaunchedEffect(uiState.message) {
        uiState.message?.let {
            // يمكن إضافة Snackbar هنا
            subscriptionViewModel.clearMessages()
        }
    }

    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // يمكن إضافة Snackbar للأخطاء هنا
            subscriptionViewModel.clearMessages()
        }
    }
}

@Composable
fun CurrentSubscriptionCard(
    subscription: com.newt.anime.data.models.UserSubscription?
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "📊 اشتراكك الحالي",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            val currentTier = subscription?.tier ?: SubscriptionTier.FREE
            val isValid = subscription?.isValid() ?: true

            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Star,
                    contentDescription = null,
                    tint = when (currentTier) {
                        SubscriptionTier.FREE -> Color.Gray
                        SubscriptionTier.PREMIUM -> Color(0xFFFFD700)
                        SubscriptionTier.UNLIMITED -> Color(0xFFFF6B35)
                    },
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = currentTier.nameAr,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // عرض الرصيد المتبقي
            subscription?.let { sub ->
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = "📊 رصيد الرفع",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = sub.getRemainingQuotaText(),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = if (sub.isExpired()) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurface
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // شريط التقدم
                        LinearProgressIndicator(
                            progress = sub.getUsagePercentage() / 100f,
                            modifier = Modifier.fillMaxWidth(),
                            color = when {
                                sub.getUsagePercentage() > 90 -> MaterialTheme.colorScheme.error
                                sub.getUsagePercentage() > 70 -> Color(0xFFFF9800)
                                else -> MaterialTheme.colorScheme.primary
                            }
                        )

                        Text(
                            text = "${sub.getUsagePercentage()}% مستخدم",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))
            }

            Text(
                text = "الحد الأقصى: ${if (currentTier.maxMembers == Int.MAX_VALUE) "غير محدود" else "${currentTier.maxMembers} أعضاء"}",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
fun SubscriptionCard(
    tier: SubscriptionTier,
    isCurrentTier: Boolean,
    onSubscribe: () -> Unit,
    isLoading: Boolean
) {
    val cardColors = when (tier) {
        SubscriptionTier.FREE -> CardDefaults.cardColors()
        SubscriptionTier.PREMIUM -> CardDefaults.cardColors(
            containerColor = Color(0xFFFFD700).copy(alpha = 0.1f)
        )
        SubscriptionTier.UNLIMITED -> CardDefaults.cardColors(
            containerColor = Color(0xFFFF6B35).copy(alpha = 0.1f)
        )
    }

    val borderColor = when (tier) {
        SubscriptionTier.FREE -> Color.Gray
        SubscriptionTier.PREMIUM -> Color(0xFFFFD700)
        SubscriptionTier.UNLIMITED -> Color(0xFFFF6B35)
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .then(
                if (isCurrentTier) {
                    Modifier.border(2.dp, Color.Green, RoundedCornerShape(12.dp))
                } else {
                    Modifier.border(1.dp, borderColor, RoundedCornerShape(12.dp))
                }
            ),
        colors = cardColors
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        Icons.Default.Star,
                        contentDescription = null,
                        tint = borderColor,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = tier.nameAr,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                if (isCurrentTier) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            Icons.Default.Check,
                            contentDescription = null,
                            tint = Color.Green,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "نشط",
                            fontSize = 12.sp,
                            color = Color.Green,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // السعر
            Text(
                text = tier.getPrice(),
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = borderColor
            )
            
            if (tier != SubscriptionTier.FREE) {
                Text(
                    text = "رصيد ${tier.uploadLimitGB.toInt()}GB",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // الميزات
            tier.features.forEach { feature ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(vertical = 2.dp)
                ) {
                    Icon(
                        Icons.Default.Check,
                        contentDescription = null,
                        tint = Color.Green,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = feature,
                        fontSize = 14.sp
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // زر الاشتراك
            if (isCurrentTier) {
                OutlinedButton(
                    onClick = { },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = false
                ) {
                    Text("✅ مفعل حالياً")
                }
            } else {
                Button(
                    onClick = onSubscribe,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isLoading,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = borderColor
                    )
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color.White
                        )
                    } else {
                        Text(
                            text = if (tier == SubscriptionTier.FREE) "تفعيل" else "اشترك الآن",
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun InfoCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "ℹ️ معلومات مهمة",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "• يمكنك إلغاء الاشتراك في أي وقت\n" +
                        "• سيتم تجديد الاشتراك تلقائياً\n" +
                        "• جميع الأسعار بالدولار الأمريكي\n" +
                        "• دعم فني متاح 24/7 للمشتركين",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                lineHeight = 16.sp
            )
        }
    }
}

@Composable
fun SubscriptionConfirmDialog(
    tier: SubscriptionTier,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "تأكيد الاشتراك",
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text("هل تريد الاشتراك في خطة ${tier.nameAr}؟")
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "السعر: ${tier.getPrice()}${if (tier != SubscriptionTier.FREE) " شهرياً" else ""}",
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "الميزات الرئيسية:",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Bold
                )
                tier.features.take(3).forEach { feature ->
                    Text(
                        text = "• $feature",
                        fontSize = 11.sp,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        },
        confirmButton = {
            Button(onClick = onConfirm) {
                Text("تأكيد")
            }
        },
        dismissButton = {
            OutlinedButton(onClick = onDismiss) {
                Text("إلغاء")
            }
        }
    )
}
