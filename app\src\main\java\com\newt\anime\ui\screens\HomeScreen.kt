package com.newt.anime.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ExitToApp
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.newt.anime.data.models.Group
import com.newt.anime.ui.components.CreateGroupDialog
import com.newt.anime.ui.components.JoinGroupDialog
import com.newt.anime.ui.components.GroupCodeDialog
import com.newt.anime.ui.viewmodel.AuthViewModel
import com.newt.anime.ui.viewmodel.GroupViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    onNavigateToGroup: (Group) -> Unit,
    authViewModel: AuthViewModel = viewModel(),
    groupViewModel: GroupViewModel = viewModel()
) {
    var showCreateGroupDialog by remember { mutableStateOf(false) }
    var showJoinGroupDialog by remember { mutableStateOf(false) }
    
    val groupUiState by groupViewModel.uiState.collectAsState()
    val userGroups by groupViewModel.userGroups.collectAsState()
    
    // Show created group code dialog
    LaunchedEffect(groupUiState.createdGroupCode) {
        if (groupUiState.createdGroupCode != null) {
            // Code will be shown in the dialog
        }
    }

    // تحديث القائمة عند العودة للشاشة الرئيسية
    LaunchedEffect(Unit) {
        groupViewModel.refreshGroups()
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Top bar
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "مجموعات المشاهدة",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold
            )
            
            IconButton(onClick = { authViewModel.signOut() }) {
                Icon(Icons.Default.ExitToApp, contentDescription = "تسجيل الخروج")
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Action buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Button(
                onClick = { showCreateGroupDialog = true },
                modifier = Modifier.weight(1f)
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("إنشاء مجموعة")
            }
            
            OutlinedButton(
                onClick = { showJoinGroupDialog = true },
                modifier = Modifier.weight(1f)
            ) {
                Icon(Icons.Default.Person, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("انضمام لمجموعة")
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Error display
        groupUiState.error?.let { error ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Text(
                    text = error,
                    modifier = Modifier.padding(16.dp),
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
        
        // Groups list
        Text(
            text = "مجموعاتي",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        if (userGroups.isEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "لا توجد مجموعات بعد. قم بإنشاء مجموعة جديدة أو انضم لمجموعة موجودة.",
                    modifier = Modifier.padding(16.dp),
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(userGroups) { group ->
                    GroupCard(
                        group = group,
                        onClick = { onNavigateToGroup(group) }
                    )
                }
            }
        }
    }
    
    // Create Group Dialog
    if (showCreateGroupDialog) {
        CreateGroupDialog(
            onDismiss = { showCreateGroupDialog = false },
            onCreateGroup = { groupName ->
                groupViewModel.createGroup(groupName)
                showCreateGroupDialog = false
            },
            isLoading = groupUiState.isLoading
        )
    }
    
    // Join Group Dialog
    if (showJoinGroupDialog) {
        JoinGroupDialog(
            onDismiss = { showJoinGroupDialog = false },
            onJoinGroup = { code ->
                groupViewModel.joinGroup(code)
                showJoinGroupDialog = false
            },
            isLoading = groupUiState.isLoading
        )
    }
    
    // Show created group code
    groupUiState.createdGroupCode?.let { code ->
        GroupCodeDialog(
            code = code,
            onDismiss = { groupViewModel.clearCreatedGroupCode() }
        )
    }
}

@Composable
fun GroupCard(
    group: Group,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = group.name,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = "المالك: ${group.ownerName}",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "الأعضاء: ${group.members.size}",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "الكود: ${group.code}",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}
