package com.newt.anime.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ExitToApp
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.newt.anime.data.models.Group
import com.newt.anime.ui.components.CreateGroupDialog
import com.newt.anime.ui.components.JoinGroupDialog
import com.newt.anime.ui.components.GroupCodeDialog
import com.newt.anime.ui.components.GroupCreationDialog
import com.newt.anime.ui.components.UpgradeRequiredDialog
import com.newt.anime.data.models.SubscriptionTier
import com.newt.anime.ui.viewmodel.AuthViewModel
import com.newt.anime.ui.viewmodel.GroupViewModel
import com.newt.anime.ui.viewmodel.SubscriptionViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    onNavigateToGroup: (Group) -> Unit,
    onNavigateToSubscription: () -> Unit,
    authViewModel: AuthViewModel = viewModel(),
    groupViewModel: GroupViewModel = viewModel(),
    subscriptionViewModel: SubscriptionViewModel = viewModel()
) {
    var showCreateGroupDialog by remember { mutableStateOf(false) }
    var showJoinGroupDialog by remember { mutableStateOf(false) }
    var showUpgradeDialog by remember { mutableStateOf<SubscriptionTier?>(null) }
    
    val groupUiState by groupViewModel.uiState.collectAsState()
    val userGroups by groupViewModel.userGroups.collectAsState()
    val userSubscription by subscriptionViewModel.userSubscription.collectAsState()
    
    // Show created group code dialog
    LaunchedEffect(groupUiState.createdGroupCode) {
        if (groupUiState.createdGroupCode != null) {
            // Code will be shown in the dialog
        }
    }

    // تحديث القائمة عند العودة للشاشة الرئيسية
    LaunchedEffect(Unit) {
        groupViewModel.refreshGroups()
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Top bar
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "مجموعات المشاهدة",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold
            )

            Row {
                IconButton(onClick = onNavigateToSubscription) {
                    Icon(Icons.Default.Star, contentDescription = "الاشتراكات", tint = Color(0xFFFFD700))
                }
                IconButton(onClick = { authViewModel.signOut() }) {
                    Icon(Icons.Default.ExitToApp, contentDescription = "تسجيل الخروج")
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))

        // عرض رصيد الرفع
        userSubscription?.let { subscription ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = when (subscription.tier) {
                        SubscriptionTier.FREE -> MaterialTheme.colorScheme.surfaceVariant
                        SubscriptionTier.PREMIUM -> Color(0xFFFFD700).copy(alpha = 0.1f)
                        SubscriptionTier.UNLIMITED -> Color(0xFFFF6B35).copy(alpha = 0.1f)
                    }
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = "📊 ${subscription.tier.nameAr}",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = when (subscription.tier) {
                                SubscriptionTier.FREE -> Color.Gray
                                SubscriptionTier.PREMIUM -> Color(0xFFFFD700)
                                SubscriptionTier.UNLIMITED -> Color(0xFFFF6B35)
                            }
                        )
                        Text(
                            text = subscription.getRemainingQuotaText(),
                            fontSize = 14.sp,
                            color = if (subscription.isExpired()) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurface
                        )
                    }

                    // شريط التقدم دائري
                    Box(
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            progress = subscription.getUsagePercentage() / 100f,
                            modifier = Modifier.size(48.dp),
                            strokeWidth = 4.dp,
                            color = when {
                                subscription.getUsagePercentage() > 90 -> MaterialTheme.colorScheme.error
                                subscription.getUsagePercentage() > 70 -> Color(0xFFFF9800)
                                else -> MaterialTheme.colorScheme.primary
                            }
                        )
                        Text(
                            text = "${subscription.getUsagePercentage()}%",
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Action buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Button(
                onClick = { showCreateGroupDialog = true },
                modifier = Modifier.weight(1f)
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("إنشاء مجموعة")
            }
            
            OutlinedButton(
                onClick = { showJoinGroupDialog = true },
                modifier = Modifier.weight(1f)
            ) {
                Icon(Icons.Default.Person, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("انضمام لمجموعة")
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Error display
        groupUiState.error?.let { error ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Text(
                    text = error,
                    modifier = Modifier.padding(16.dp),
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
        
        // Groups list
        Text(
            text = "مجموعاتي",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        if (userGroups.isEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "لا توجد مجموعات بعد. قم بإنشاء مجموعة جديدة أو انضم لمجموعة موجودة.",
                    modifier = Modifier.padding(16.dp),
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(userGroups) { group ->
                    GroupCard(
                        group = group,
                        onClick = { onNavigateToGroup(group) }
                    )
                }
            }
        }
    }

    // حوار إنشاء المجموعة الجديد مع اختيار عدد الأعضاء
    if (showCreateGroupDialog) {
        GroupCreationDialog(
            onDismiss = { showCreateGroupDialog = false },
            onCreateGroup = { groupName, maxMembers ->
                groupViewModel.createGroup(groupName, maxMembers)
                showCreateGroupDialog = false
            },
            onUpgradeRequired = { tier ->
                showCreateGroupDialog = false
                showUpgradeDialog = tier
            },
            subscriptionViewModel = subscriptionViewModel
        )
    }

    // حوار الانضمام للمجموعة
    if (showJoinGroupDialog) {
        JoinGroupDialog(
            onDismiss = { showJoinGroupDialog = false },
            onJoinGroup = { code ->
                groupViewModel.joinGroup(code)
                showJoinGroupDialog = false
            },
            isLoading = groupUiState.isLoading
        )
    }

    // حوار عرض كود المجموعة المُنشأة
    groupUiState.createdGroupCode?.let { code ->
        GroupCodeDialog(
            code = code,
            onDismiss = {
                groupViewModel.clearCreatedGroupCode()
            }
        )
    }

    // حوار ترقية الاشتراك
    showUpgradeDialog?.let { tier ->
        UpgradeRequiredDialog(
            requiredTier = tier,
            onUpgrade = {
                showUpgradeDialog = null
                onNavigateToSubscription()
            },
            onDismiss = { showUpgradeDialog = null }
        )
    }
}

@Composable
fun GroupCard(
    group: Group,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = group.name,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = "المالك: ${group.ownerName}",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = group.getMembershipStatus(),
                fontSize = 14.sp,
                color = if (group.isFull()) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "الكود: ${group.code}",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}
