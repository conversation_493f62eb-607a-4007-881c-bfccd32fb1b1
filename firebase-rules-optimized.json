{"rules": {"users": {"$uid": {".read": "$uid === auth.uid", ".write": "$uid === auth.uid"}}, "groups": {".read": "auth != null", ".write": "auth != null", ".indexOn": ["code", "ownerId", "name"], "$groupId": {".read": "auth != null", ".write": "auth != null", "members": {".read": "auth != null", ".write": "auth != null"}, "currentVideo": {".read": "auth != null", ".write": "auth != null", ".indexOn": ["syncTimestamp", "lastUpdated", "ownerAction"], "videoUrl": {".validate": "newData.isString()"}, "title": {".validate": "newData.isString()"}, "isPlaying": {".validate": "newData.isBoolean()"}, "currentPosition": {".validate": "newData.isNumber() && newData.val() >= 0"}, "lastUpdated": {".validate": "newData.isNumber()"}, "syncCommand": {".validate": "newData.isString() && (newData.val() === 'play' || newData.val() === 'pause' || newData.val() === 'seek' || newData.val() === '')"}, "syncTimestamp": {".validate": "newData.isNumber()"}, "ownerAction": {".validate": "newData.isBoolean()"}}}}}}