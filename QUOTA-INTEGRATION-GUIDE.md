# 📊 دليل تكامل نظام الرصيد مع رفع الفيديو

## 📱 **anime-app-QUOTA-INTEGRATION-debug.apk**

### **🔗 ربط نظام الرصيد بالكامل مع رفع الفيديو + شريط الرصيد في الأعلى**

---

## 🎯 **التحديثات الجديدة:**

### 1. **📊 شريط الرصيد في أعلى الصفحة الرئيسية**
```
┌─────────────────────────────────────┐
│ ⭐ 45MB متبقي اليوم    ████░░ 85% │
│ يتجدد كل 24 ساعة                   │
├─────────────────────────────────────┤
│        مجموعات المشاهدة            │
└─────────────────────────────────────┘
```

### 2. **🔍 فحص الرصيد عند اختيار الفيديو**
- **فحص فوري** عند اختيار ملف الفيديو
- **تحذير واضح** إذا كان الرصيد غير كافي
- **عرض الرصيد الحالي** تحت معلومات الملف

### 3. **⚡ استهلاك الرصيد التلقائي**
- **خصم تلقائي** من الرصيد عند نجاح الرفع
- **تحديث فوري** لعرض الرصيد في الواجهة
- **تحويل تلقائي** للمجاني عند انتهاء الرصيد المدفوع

### 4. **🔄 إعادة تعيين يومية للمجاني فقط**
- **300MB يومياً** للمستوى المجاني
- **إعادة تعيين تلقائية** كل 24 ساعة
- **المستويات المدفوعة محمية** من الإعادة التلقائية

---

## 🎬 **تجربة رفع الفيديو الجديدة:**

### **1. اختيار الفيديو:**
```
┌─────────────────────────────────────┐
│        📤 رفع فيديو جديد            │
├─────────────────────────────────────┤
│ ✅ تم اختيار الفيديو               │
│ فيديو_تجريبي.mp4                   │
│ 150 MB                              │
│                                     │
│ ⚠️ الرصيد اليومي غير كافي          │
│ متبقي: 45MB، مطلوب: 150MB          │
│                                     │
│ 💾 45MB متبقي اليوم                │
├─────────────────────────────────────┤
│ [إلغاء]              [🚀 رفع] 🔒   │
└─────────────────────────────────────┘
```

### **2. رفع ناجح:**
```
┌─────────────────────────────────────┐
│        📤 جاري رفع الفيديو...       │
├─────────────────────────────────────┤
│ ████████████████████░ 95%           │
│ 95% مكتمل                          │
│                                     │
│ ✅ تم رفع الفيديو بنجاح!           │
│ ✅ تم خصم 50MB من رصيدك            │
│                                     │
│ 💾 250MB متبقي اليوم               │
└─────────────────────────────────────┘
```

---

## 🔧 **آلية العمل التفصيلية:**

### **1. عند اختيار الفيديو:**
```kotlin
// فحص فوري للرصيد
quotaError = if (subscriptionViewModel.canUploadVideo(videoFileSizeInMB)) {
    null
} else {
    subscriptionViewModel.getQuotaErrorMessage(videoFileSizeInMB)
}
```

### **2. عند الضغط على زر الرفع:**
```kotlin
// فحص مرة أخرى قبل الرفع
if (!subscriptionViewModel.canUploadVideo(videoFileSizeInMB)) {
    quotaError = subscriptionViewModel.getQuotaErrorMessage(videoFileSizeInMB)
    return
}
```

### **3. عند نجاح الرفع:**
```kotlin
// استهلاك الرصيد تلقائياً
subscriptionViewModel.consumeUploadQuota(
    sizeInMB = videoSizeInMB,
    onSuccess = { /* تحديث الواجهة */ },
    onError = { /* معالجة الخطأ */ }
)
```

### **4. إعادة التعيين اليومية:**
```kotlin
// للمجاني فقط - كل 24 ساعة
if (subscription.tier == SubscriptionTier.FREE && subscription.needsDailyReset()) {
    subscription.copy(
        dailyUploadUsedMB = 0.0,
        lastDailyReset = System.currentTimeMillis()
    )
}
```

---

## 📊 **شريط الرصيد في الأعلى:**

### **للمستوى المجاني:**
```
┌─────────────────────────────────────┐
│ 📊 45MB متبقي اليوم    ████░░ 85% │
│ يتجدد كل 24 ساعة                   │
└─────────────────────────────────────┘
```

### **للمستوى البريميوم:**
```
┌─────────────────────────────────────┐
│ ⭐ 6.2GB متبقي         ████████░░ 60% │
└─────────────────────────────────────┘
```

### **عند انتهاء الرصيد:**
```
┌─────────────────────────────────────┐
│ ❌ انتهى الرصيد       ██████████ 100% │
└─────────────────────────────────────┘
```

---

## ⚠️ **رسائل التحذير والأخطاء:**

### **1. رصيد غير كافي (مجاني):**
```
⚠️ الرصيد اليومي غير كافي
متبقي: 45MB، مطلوب: 150MB
```

### **2. رصيد غير كافي (مدفوع):**
```
⚠️ الرصيد غير كافي
متبقي: 0.5GB، مطلوب: 1.2GB
```

### **3. انتهاء الرصيد المدفوع:**
```
🔴 انتهى رصيدك المدفوع!
تم تحويلك للمستوى المجاني
المتاح الآن: 300MB يومياً
```

---

## 🔄 **سيناريوهات الاستخدام:**

### **1. مستخدم مجاني - رفع ناجح:**
1. **الرصيد الحالي**: 200MB متبقي من 300MB
2. **اختيار فيديو**: 50MB ✅
3. **فحص الرصيد**: كافي ✅
4. **رفع الفيديو**: نجح ✅
5. **استهلاك الرصيد**: 150MB متبقي
6. **تحديث الشريط**: 150MB متبقي اليوم

### **2. مستخدم مجاني - رصيد غير كافي:**
1. **الرصيد الحالي**: 50MB متبقي
2. **اختيار فيديو**: 100MB ❌
3. **تحذير فوري**: "الرصيد اليومي غير كافي"
4. **زر الرفع**: مقفل 🔒
5. **الخيارات**: انتظار 24 ساعة أو ترقية الاشتراك

### **3. مستخدم بريميوم - انتهاء الرصيد:**
1. **الرصيد الحالي**: 0.1GB متبقي
2. **رفع فيديو**: 0.2GB
3. **استهلاك الرصيد**: 0GB متبقي
4. **تحويل تلقائي**: للمستوى المجاني
5. **رصيد جديد**: 300MB يومياً

### **4. إعادة التعيين اليومية:**
1. **منتصف الليل**: فحص تلقائي
2. **المستخدمين المجانيين**: إعادة تعيين لـ 300MB
3. **المستخدمين المدفوعين**: لا تغيير
4. **تحديث الواجهة**: عرض الرصيد الجديد

---

## 🛠️ **التحديثات التقنية:**

### **الملفات المحدثة:**

#### 1. **HomeScreen.kt**
- **شريط الرصيد** في أعلى الصفحة
- **مؤشر دائري** لنسبة الاستهلاك
- **ألوان تحذيرية** حسب النسبة

#### 2. **VideoUploadDialog.kt**
- **فحص الرصيد** عند اختيار الفيديو
- **عرض تحذيرات** الرصيد غير الكافي
- **استهلاك الرصيد** عند نجاح الرفع
- **تمرير SubscriptionViewModel**

#### 3. **SubscriptionViewModel.kt**
- **دوال فحص الرصيد** المحسنة
- **استهلاك الرصيد** مع معالجة الأخطاء
- **فحص وإعادة تعيين** الحد اليومي

#### 4. **SubscriptionRepository.kt**
- **إعادة تعيين تلقائية** للمجاني فقط
- **حماية المستويات المدفوعة** من الإعادة
- **تحويل تلقائي** للمجاني عند انتهاء الرصيد

#### 5. **UserSubscription.kt**
- **دالة needsDailyReset()** للفحص اليومي
- **نصوص محسنة** للرصيد المتبقي
- **معالجة حالات** انتهاء الرصيد

---

## 🎨 **التحسينات البصرية:**

### **شريط الرصيد:**
- **ألوان متدرجة**: حسب نوع الاشتراك
- **مؤشر تقدم**: خطي صغير
- **نسبة مئوية**: واضحة ومقروءة

### **حوار رفع الفيديو:**
- **تحذيرات ملونة**: أحمر للأخطاء
- **عرض الرصيد**: تحت معلومات الملف
- **زر مقفل**: عند عدم كفاية الرصيد

### **رسائل التحذير:**
- **أيقونات واضحة**: ⚠️ للتحذير، ❌ للخطأ
- **نصوص مفصلة**: تشرح المشكلة والحل
- **ألوان مناسبة**: حسب نوع الرسالة

---

## 🎉 **المزايا الجديدة:**

✅ **شريط رصيد دائم** في أعلى الصفحة الرئيسية  
✅ **فحص فوري للرصيد** عند اختيار الفيديو  
✅ **استهلاك تلقائي** للرصيد عند نجاح الرفع  
✅ **إعادة تعيين يومية** للمجاني فقط (300MB)  
✅ **حماية المستويات المدفوعة** من الإعادة التلقائية  
✅ **تحويل تلقائي** للمجاني عند انتهاء الرصيد  
✅ **تحذيرات واضحة** للرصيد غير الكافي  
✅ **واجهة محسنة** مع مؤشرات بصرية  

**النظام الآن متكامل بالكامل ويعمل بسلاسة!** 🚀

## 📱 **ملف APK الجديد:**
**`anime-app-QUOTA-INTEGRATION-debug.apk`** - جاهز للتثبيت والاختبار!
