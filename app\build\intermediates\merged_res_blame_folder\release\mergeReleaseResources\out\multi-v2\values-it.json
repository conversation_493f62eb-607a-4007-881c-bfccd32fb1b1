{"logs": [{"outputFile": "com.newt.anime.app-mergeReleaseResources-62:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5f82ddb128f9d8fc1f894d4feaff6b09\\transformed\\exoplayer-core-2.19.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,405,471,558,643", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "121,182,254,324,400,466,553,638,712"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6560,6631,6692,6764,6834,6910,6976,7063,7148", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "6626,6687,6759,6829,6905,6971,7058,7143,7217"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4366d762d4e78881da860b75d1c4c59f\\transformed\\play-services-basement-18.4.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "38", "startColumns": "4", "startOffsets": "2872", "endColumns": "135", "endOffsets": "3003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf7927868eeddad3b4319378a3201862\\transformed\\browser-1.4.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "48,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "4095,8371,8469,8579", "endColumns": "99,97,109,102", "endOffsets": "4190,8464,8574,8677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8f761d99cc674c08e5b049b2a7bbf06\\transformed\\core-1.16.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "21,22,23,24,25,26,27,173", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "939,1037,1139,1238,1340,1449,1556,15952", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "1032,1134,1233,1335,1444,1551,1681,16048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9c9112f3b5828659e41d3d6d877dc27\\transformed\\credentials-1.2.0-rc01\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "19,20", "startColumns": "4,4", "startOffsets": "713,825", "endColumns": "111,113", "endOffsets": "820,934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f341d0dad5e087546904e2a03fd632f3\\transformed\\play-services-base-18.1.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "30,31,32,33,34,35,36,37,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1873,1978,2129,2254,2362,2520,2648,2768,3008,3165,3272,3426,3553,3709,3890,3957,4018", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "1973,2124,2249,2357,2515,2643,2763,2867,3160,3267,3421,3548,3704,3885,3952,4013,4090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95a85adbd9a0258061c7c940036f2eec\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1034,1122,1207,1283,1355,1425,1503,1572", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,75,71,69,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1029,1117,1202,1278,1350,1420,1498,1567,1688"}, "to": {"startLines": "28,29,49,50,51,106,107,165,166,167,168,169,170,171,172,174,175,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1686,1786,4195,4293,4393,8682,8761,15289,15382,15477,15561,15649,15734,15810,15882,16053,16131,16200", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,75,71,69,77,68,120", "endOffsets": "1781,1868,4288,4388,4475,8756,8862,15377,15472,15556,15644,15729,15805,15877,15947,16126,16195,16316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\85c261ec03eee0a4101b5d8231a63563\\transformed\\material3-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,306,428,552,657,753,866,1009,1128,1286,1370,1482,1576,1676,1795,1917,2034,2176,2316,2459,2635,2770,2890,3013,3143,3238,3335,3462,3600,3700,3810,3916,4059,4207,4317,4418,4507,4603,4696,4811,4897,4983,5086,5166,5249,5348,5454,5554,5655,5743,5853,5953,6058,6176,6256,6370", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,114,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "177,301,423,547,652,748,861,1004,1123,1281,1365,1477,1571,1671,1790,1912,2029,2171,2311,2454,2630,2765,2885,3008,3138,3233,3330,3457,3595,3695,3805,3911,4054,4202,4312,4413,4502,4598,4691,4806,4892,4978,5081,5161,5244,5343,5449,5549,5650,5738,5848,5948,6053,6171,6251,6365,6472"}, "to": {"startLines": "108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8867,8994,9118,9240,9364,9469,9565,9678,9821,9940,10098,10182,10294,10388,10488,10607,10729,10846,10988,11128,11271,11447,11582,11702,11825,11955,12050,12147,12274,12412,12512,12622,12728,12871,13019,13129,13230,13319,13415,13508,13623,13709,13795,13898,13978,14061,14160,14266,14366,14467,14555,14665,14765,14870,14988,15068,15182", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,114,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "8989,9113,9235,9359,9464,9560,9673,9816,9935,10093,10177,10289,10383,10483,10602,10724,10841,10983,11123,11266,11442,11577,11697,11820,11950,12045,12142,12269,12407,12507,12617,12723,12866,13014,13124,13225,13314,13410,13503,13618,13704,13790,13893,13973,14056,14155,14261,14361,14462,14550,14660,14760,14865,14983,15063,15177,15284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dc398bc18186089f1a6c4c36afb914b8\\transformed\\foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "16321,16419", "endColumns": "97,98", "endOffsets": "16414,16513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6436519ada9dddee292e4f473475ac25\\transformed\\exoplayer-ui-2.19.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,663,749,837,916,1008,1100,1178,1243,1343,1441,1506,1574,1639,1710,1838,1972,2098,2168,2261,2336,2412,2508,2606,2675,2743,2796,2854,2902,2963,3037,3108,3171,3252,3310,3371,3437,3502,3568,3620,3682,3758,3834", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,127,133,125,69,92,74,75,95,97,68,67,52,57,47,60,73,70,62,80,57,60,65,64,65,51,61,75,75,57", "endOffsets": "281,470,658,744,832,911,1003,1095,1173,1238,1338,1436,1501,1569,1634,1705,1833,1967,2093,2163,2256,2331,2407,2503,2601,2670,2738,2791,2849,2897,2958,3032,3103,3166,3247,3305,3366,3432,3497,3563,3615,3677,3753,3829,3887"}, "to": {"startLines": "2,11,15,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,525,4480,4566,4654,4733,4825,4917,4995,5060,5160,5258,5323,5391,5456,5527,5655,5789,5915,5985,6078,6153,6229,6325,6423,6492,7222,7275,7333,7381,7442,7516,7587,7650,7731,7789,7850,7916,7981,8047,8099,8161,8237,8313", "endLines": "10,14,18,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,127,133,125,69,92,74,75,95,97,68,67,52,57,47,60,73,70,62,80,57,60,65,64,65,51,61,75,75,57", "endOffsets": "331,520,708,4561,4649,4728,4820,4912,4990,5055,5155,5253,5318,5386,5451,5522,5650,5784,5910,5980,6073,6148,6224,6320,6418,6487,6555,7270,7328,7376,7437,7511,7582,7645,7726,7784,7845,7911,7976,8042,8094,8156,8232,8308,8366"}}]}]}