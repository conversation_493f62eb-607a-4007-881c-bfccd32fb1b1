#!/bin/bash

echo "========================================"
echo "    Firebase Rules Deployment Tool"
echo "========================================"
echo

echo "اختر نوع القواعد للنشر:"
echo "[1] قواعد شاملة ومفصلة (FIREBASE-REALTIME-DATABASE-RULES.json)"
echo "[2] قواعد مبسطة للتطوير (FIREBASE-RULES-SIMPLE.json)"
echo "[3] قواعد محسنة للإنتاج (FIREBASE-RULES-PRODUCTION.json)"
echo "[4] إلغاء"
echo

read -p "أدخل اختيارك (1-4): " choice

case $choice in
    1)
        rules_file="FIREBASE-REALTIME-DATABASE-RULES.json"
        rules_name="الشاملة والمفصلة"
        ;;
    2)
        rules_file="FIREBASE-RULES-SIMPLE.json"
        rules_name="المبسطة للتطوير"
        ;;
    3)
        rules_file="FIREBASE-RULES-PRODUCTION.json"
        rules_name="المحسنة للإنتاج"
        ;;
    4)
        echo "تم الإلغاء."
        exit 0
        ;;
    *)
        echo "اختيار غير صحيح!"
        exit 1
        ;;
esac

echo
echo "تم اختيار القواعد $rules_name"
echo "الملف: $rules_file"
echo

if [ ! -f "$rules_file" ]; then
    echo "خطأ: الملف $rules_file غير موجود!"
    exit 1
fi

read -p "هل تريد المتابعة؟ (y/n): " confirm
if [[ $confirm != [yY] ]]; then
    echo "تم الإلغاء."
    exit 0
fi

echo
echo "[1/3] التحقق من Firebase CLI..."
if ! command -v firebase &> /dev/null; then
    echo "خطأ: Firebase CLI غير مثبت!"
    echo "يرجى تثبيته باستخدام: npm install -g firebase-tools"
    exit 1
fi

echo "[2/3] التحقق من تسجيل الدخول..."
if ! firebase projects:list &> /dev/null; then
    echo "يرجى تسجيل الدخول أولاً..."
    firebase login
    if [ $? -ne 0 ]; then
        echo "فشل في تسجيل الدخول!"
        exit 1
    fi
fi

echo "[3/3] نشر القواعد..."
echo

# إنشاء نسخة احتياطية
echo "إنشاء نسخة احتياطية..."
timestamp=$(date +"%Y%m%d_%H%M%S")
firebase database:get / > "backup-rules-$timestamp.json" 2>/dev/null

# نسخ الملف المختار إلى database.rules.json
cp "$rules_file" database.rules.json

# نشر القواعد
firebase deploy --only database
if [ $? -ne 0 ]; then
    echo "فشل في نشر القواعد!"
    exit 1
fi

echo
echo "========================================"
echo "       تم نشر القواعد بنجاح! ✅"
echo "========================================"
echo
echo "القواعد المنشورة: $rules_name"
echo "الملف: $rules_file"
echo
echo "ملاحظات:"
echo "- تم إنشاء نسخة احتياطية"
echo "- يمكنك مراجعة القواعد في Firebase Console"
echo "- اختبر التطبيق للتأكد من عمل القواعد"
echo
