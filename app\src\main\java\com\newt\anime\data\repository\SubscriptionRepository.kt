package com.newt.anime.data.repository

import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseError
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.database.ValueEventListener
import com.newt.anime.data.models.*
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await

class SubscriptionRepository {
    private val database = FirebaseDatabase.getInstance()
    private val subscriptionsRef = database.reference.child("subscriptions")
    private val paymentsRef = database.reference.child("payments")
    private val usageStatsRef = database.reference.child("usage_stats")

    // الحصول على اشتراك المستخدم مع فحص التحديث اليومي
    suspend fun getUserSubscription(userId: String): Result<UserSubscription> {
        return try {
            val snapshot = subscriptionsRef.child(userId).get().await()
            var subscription = if (snapshot.exists()) {
                snapshot.getValue(UserSubscription::class.java) ?: UserSubscription(userId = userId)
            } else {
                // إنشاء اشتراك مجاني افتراضي
                val defaultSubscription = UserSubscription(
                    userId = userId,
                    tier = SubscriptionTier.FREE,
                    isActive = true
                )
                subscriptionsRef.child(userId).setValue(defaultSubscription).await()
                defaultSubscription
            }

            // فحص وإعادة تعيين الحد اليومي للمجاني فقط
            if (subscription.tier == SubscriptionTier.FREE && subscription.needsDailyReset()) {
                subscription = subscription.copy(
                    dailyUploadUsedMB = 0.0,
                    lastDailyReset = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis()
                )
                subscriptionsRef.child(userId).setValue(subscription).await()
                android.util.Log.d("SubscriptionRepository", "Daily quota reset for FREE user: $userId")
            }

            Result.success(subscription)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error getting user subscription: ${e.message}")
            Result.failure(e)
        }
    }

    // مراقبة اشتراك المستخدم في الوقت الفعلي
    fun observeUserSubscription(userId: String): Flow<UserSubscription> = callbackFlow {
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val subscription = if (snapshot.exists()) {
                    snapshot.getValue(UserSubscription::class.java) ?: UserSubscription(userId = userId)
                } else {
                    UserSubscription(userId = userId)
                }
                trySend(subscription)
            }

            override fun onCancelled(error: DatabaseError) {
                android.util.Log.e("SubscriptionRepository", "Error observing subscription: ${error.message}")
                close(error.toException())
            }
        }

        subscriptionsRef.child(userId).addValueEventListener(listener)
        awaitClose { subscriptionsRef.child(userId).removeEventListener(listener) }
    }

    // تحديث اشتراك المستخدم
    suspend fun updateUserSubscription(subscription: UserSubscription): Result<Unit> {
        return try {
            val updatedSubscription = subscription.copy(updatedAt = System.currentTimeMillis())
            subscriptionsRef.child(subscription.userId).setValue(updatedSubscription).await()
            android.util.Log.d("SubscriptionRepository", "Subscription updated for user: ${subscription.userId}")
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error updating subscription: ${e.message}")
            Result.failure(e)
        }
    }

    // ترقية الاشتراك (شراء رصيد)
    suspend fun upgradeSubscription(
        userId: String,
        newTier: SubscriptionTier
    ): Result<UserSubscription> {
        return try {
            val upgradedSubscription = UserSubscription(
                userId = userId,
                tier = newTier,
                isActive = true,
                remainingUploadGB = newTier.uploadLimitGB, // رصيد كامل
                dailyUploadUsedMB = 0.0,
                lastDailyReset = System.currentTimeMillis(),
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            subscriptionsRef.child(userId).setValue(upgradedSubscription).await()
            android.util.Log.d("SubscriptionRepository", "Subscription upgraded to ${newTier.nameAr} with ${newTier.uploadLimitGB}GB for user: $userId")
            Result.success(upgradedSubscription)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error upgrading subscription: ${e.message}")
            Result.failure(e)
        }
    }

    // إلغاء الاشتراك
    suspend fun cancelSubscription(userId: String): Result<Unit> {
        return try {
            val currentSubscription = getUserSubscription(userId).getOrNull()
            if (currentSubscription != null) {
                val cancelledSubscription = currentSubscription.copy(
                    isActive = false,
                    updatedAt = System.currentTimeMillis()
                )
                subscriptionsRef.child(userId).setValue(cancelledSubscription).await()
                android.util.Log.d("SubscriptionRepository", "Subscription cancelled for user: $userId")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error cancelling subscription: ${e.message}")
            Result.failure(e)
        }
    }

    // الحصول على خيارات إنشاء المجموعة للمستخدم
    suspend fun getGroupCreationOptions(userId: String): List<GroupCreationOption> {
        return try {
            val subscription = getUserSubscription(userId).getOrNull() ?: UserSubscription(userId = userId)
            val currentTier = if (subscription.isValid()) subscription.tier else SubscriptionTier.FREE

            listOf(
                // الخيار المجاني
                GroupCreationOption(
                    maxMembers = SubscriptionTier.FREE.maxMembers,
                    tier = SubscriptionTier.FREE,
                    isAvailable = true,
                    requiresUpgrade = false
                ),
                // الخيار المدفوع الأول
                GroupCreationOption(
                    maxMembers = SubscriptionTier.PREMIUM.maxMembers,
                    tier = SubscriptionTier.PREMIUM,
                    isAvailable = currentTier == SubscriptionTier.PREMIUM || currentTier == SubscriptionTier.UNLIMITED,
                    requiresUpgrade = currentTier == SubscriptionTier.FREE
                ),
                // الخيار غير المحدود
                GroupCreationOption(
                    maxMembers = SubscriptionTier.UNLIMITED.maxMembers,
                    tier = SubscriptionTier.UNLIMITED,
                    isAvailable = currentTier == SubscriptionTier.UNLIMITED,
                    requiresUpgrade = currentTier != SubscriptionTier.UNLIMITED
                )
            )
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error getting group creation options: ${e.message}")
            // إرجاع الخيار المجاني فقط في حالة الخطأ
            listOf(
                GroupCreationOption(
                    maxMembers = SubscriptionTier.FREE.maxMembers,
                    tier = SubscriptionTier.FREE,
                    isAvailable = true,
                    requiresUpgrade = false
                )
            )
        }
    }

    // التحقق من إمكانية انضمام عضو جديد للمجموعة
    suspend fun canAddMemberToGroup(groupId: String, currentMemberCount: Int, groupOwnerId: String): Boolean {
        return try {
            val subscription = getUserSubscription(groupOwnerId).getOrNull() ?: UserSubscription(userId = groupOwnerId)
            val maxMembers = subscription.getMaxMembers()
            
            val canAdd = currentMemberCount < maxMembers
            android.util.Log.d("SubscriptionRepository", "Can add member to group $groupId: $canAdd (current: $currentMemberCount, max: $maxMembers)")
            canAdd
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error checking member limit: ${e.message}")
            // في حالة الخطأ، استخدم الحد المجاني
            currentMemberCount < SubscriptionTier.FREE.maxMembers
        }
    }

    // حفظ معلومات الدفع
    suspend fun savePaymentInfo(paymentInfo: PaymentInfo): Result<Unit> {
        return try {
            paymentsRef.child(paymentInfo.id).setValue(paymentInfo).await()
            android.util.Log.d("SubscriptionRepository", "Payment info saved: ${paymentInfo.id}")
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error saving payment info: ${e.message}")
            Result.failure(e)
        }
    }

    // تحديث إحصائيات الاستخدام
    suspend fun updateUsageStats(userId: String, stats: UsageStats): Result<Unit> {
        return try {
            usageStatsRef.child(userId).setValue(stats).await()
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error updating usage stats: ${e.message}")
            Result.failure(e)
        }
    }

    // الحصول على إحصائيات الاستخدام
    suspend fun getUsageStats(userId: String): Result<UsageStats> {
        return try {
            val snapshot = usageStatsRef.child(userId).get().await()
            val stats = if (snapshot.exists()) {
                snapshot.getValue(UsageStats::class.java) ?: UsageStats(userId = userId)
            } else {
                UsageStats(userId = userId)
            }
            Result.success(stats)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error getting usage stats: ${e.message}")
            Result.failure(e)
        }
    }

    // استهلاك رصيد الرفع
    suspend fun consumeUploadQuota(userId: String, sizeInMB: Double): Result<Unit> {
        return try {
            val subscription = getUserSubscription(userId).getOrNull() ?: return Result.failure(Exception("No subscription found"))

            // التحقق من إمكانية الرفع أولاً
            if (!subscription.canUploadVideo(sizeInMB)) {
                return Result.failure(Exception("الرصيد غير كافي لرفع هذا الفيديو"))
            }

            val updatedSubscription = when (subscription.tier) {
                SubscriptionTier.FREE -> {
                    // للمجاني: زيادة الاستهلاك اليومي فقط
                    val newDailyUsed = subscription.dailyUploadUsedMB + sizeInMB
                    subscription.copy(
                        dailyUploadUsedMB = newDailyUsed,
                        updatedAt = System.currentTimeMillis()
                    )
                }
                else -> {
                    // للمدفوع: تقليل الرصيد
                    val sizeInGB = sizeInMB / 1024.0
                    val newRemaining = subscription.remainingUploadGB - sizeInGB

                    // إذا انتهى الرصيد، تحويل للمجاني مع رصيد يومي جديد
                    val isStillActive = newRemaining > 0

                    if (isStillActive) {
                        subscription.copy(
                            remainingUploadGB = newRemaining,
                            updatedAt = System.currentTimeMillis()
                        )
                    } else {
                        // تحويل للمجاني مع رصيد يومي كامل
                        subscription.copy(
                            tier = SubscriptionTier.FREE,
                            isActive = true,
                            remainingUploadGB = 0.0,
                            dailyUploadUsedMB = 0.0, // رصيد يومي جديد
                            lastDailyReset = System.currentTimeMillis(),
                            updatedAt = System.currentTimeMillis()
                        )
                    }
                }
            }

            subscriptionsRef.child(userId).setValue(updatedSubscription).await()
            android.util.Log.d("SubscriptionRepository", "Upload quota consumed: ${sizeInMB}MB for user: $userId, tier: ${subscription.tier.nameAr}")

            // إذا تم التحويل للمجاني، إرسال إشعار
            if (subscription.tier != SubscriptionTier.FREE && updatedSubscription.tier == SubscriptionTier.FREE) {
                android.util.Log.i("SubscriptionRepository", "User $userId converted to FREE tier due to quota exhaustion")
            }

            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error consuming upload quota: ${e.message}")
            Result.failure(e)
        }
    }

    // إعادة تعيين الحد اليومي للمجاني فقط (تلقائياً كل 24 ساعة)
    suspend fun resetDailyQuotaIfNeeded(userId: String): Result<Unit> {
        return try {
            val subscription = getUserSubscription(userId).getOrNull() ?: return Result.failure(Exception("No subscription found"))

            // إعادة التعيين للمجاني فقط
            if (subscription.tier == SubscriptionTier.FREE && subscription.needsDailyReset()) {
                val resetSubscription = subscription.copy(
                    dailyUploadUsedMB = 0.0,
                    lastDailyReset = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis()
                )

                subscriptionsRef.child(userId).setValue(resetSubscription).await()
                android.util.Log.d("SubscriptionRepository", "Daily quota reset for FREE user: $userId (300MB restored)")
            }

            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error resetting daily quota: ${e.message}")
            Result.failure(e)
        }
    }

    // فحص وإعادة تعيين جميع المستخدمين المجانيين (يمكن استدعاؤها دورياً)
    suspend fun resetAllFreeDailyQuotas(): Result<Unit> {
        return try {
            val snapshot = subscriptionsRef.get().await()
            var resetCount = 0

            for (userSnapshot in snapshot.children) {
                val subscription = userSnapshot.getValue(UserSubscription::class.java)
                if (subscription?.tier == SubscriptionTier.FREE && subscription.needsDailyReset()) {
                    val resetSubscription = subscription.copy(
                        dailyUploadUsedMB = 0.0,
                        lastDailyReset = System.currentTimeMillis(),
                        updatedAt = System.currentTimeMillis()
                    )

                    subscriptionsRef.child(subscription.userId).setValue(resetSubscription).await()
                    resetCount++
                }
            }

            android.util.Log.d("SubscriptionRepository", "Daily quota reset for $resetCount FREE users")
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error resetting all daily quotas: ${e.message}")
            Result.failure(e)
        }
    }
}
