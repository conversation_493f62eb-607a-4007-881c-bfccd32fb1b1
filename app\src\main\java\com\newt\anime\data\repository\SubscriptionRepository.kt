package com.newt.anime.data.repository

import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseError
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.database.ValueEventListener
import com.newt.anime.data.models.*
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await

class SubscriptionRepository {
    private val database = FirebaseDatabase.getInstance()
    private val subscriptionsRef = database.reference.child("subscriptions")
    private val paymentsRef = database.reference.child("payments")
    private val usageStatsRef = database.reference.child("usage_stats")

    // الحصول على اشتراك المستخدم
    suspend fun getUserSubscription(userId: String): Result<UserSubscription> {
        return try {
            val snapshot = subscriptionsRef.child(userId).get().await()
            val subscription = if (snapshot.exists()) {
                snapshot.getValue(UserSubscription::class.java) ?: UserSubscription(userId = userId)
            } else {
                // إنشاء اشتراك مجاني افتراضي
                val defaultSubscription = UserSubscription(
                    userId = userId,
                    tier = SubscriptionTier.FREE,
                    isActive = true
                )
                subscriptionsRef.child(userId).setValue(defaultSubscription).await()
                defaultSubscription
            }
            Result.success(subscription)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error getting user subscription: ${e.message}")
            Result.failure(e)
        }
    }

    // مراقبة اشتراك المستخدم في الوقت الفعلي
    fun observeUserSubscription(userId: String): Flow<UserSubscription> = callbackFlow {
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val subscription = if (snapshot.exists()) {
                    snapshot.getValue(UserSubscription::class.java) ?: UserSubscription(userId = userId)
                } else {
                    UserSubscription(userId = userId)
                }
                trySend(subscription)
            }

            override fun onCancelled(error: DatabaseError) {
                android.util.Log.e("SubscriptionRepository", "Error observing subscription: ${error.message}")
                close(error.toException())
            }
        }

        subscriptionsRef.child(userId).addValueEventListener(listener)
        awaitClose { subscriptionsRef.child(userId).removeEventListener(listener) }
    }

    // تحديث اشتراك المستخدم
    suspend fun updateUserSubscription(subscription: UserSubscription): Result<Unit> {
        return try {
            val updatedSubscription = subscription.copy(updatedAt = System.currentTimeMillis())
            subscriptionsRef.child(subscription.userId).setValue(updatedSubscription).await()
            android.util.Log.d("SubscriptionRepository", "Subscription updated for user: ${subscription.userId}")
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error updating subscription: ${e.message}")
            Result.failure(e)
        }
    }

    // ترقية الاشتراك
    suspend fun upgradeSubscription(
        userId: String,
        newTier: SubscriptionTier,
        durationMonths: Int = 1
    ): Result<UserSubscription> {
        return try {
            val endDate = if (newTier == SubscriptionTier.FREE) {
                0L // غير محدود للمجاني
            } else {
                System.currentTimeMillis() + (durationMonths * 30L * 24 * 60 * 60 * 1000) // شهر = 30 يوم
            }

            val upgradedSubscription = UserSubscription(
                userId = userId,
                tier = newTier,
                isActive = true,
                startDate = System.currentTimeMillis(),
                endDate = endDate,
                autoRenew = true,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )

            subscriptionsRef.child(userId).setValue(upgradedSubscription).await()
            android.util.Log.d("SubscriptionRepository", "Subscription upgraded to ${newTier.nameAr} for user: $userId")
            Result.success(upgradedSubscription)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error upgrading subscription: ${e.message}")
            Result.failure(e)
        }
    }

    // إلغاء الاشتراك
    suspend fun cancelSubscription(userId: String): Result<Unit> {
        return try {
            val currentSubscription = getUserSubscription(userId).getOrNull()
            if (currentSubscription != null) {
                val cancelledSubscription = currentSubscription.copy(
                    autoRenew = false,
                    updatedAt = System.currentTimeMillis()
                )
                subscriptionsRef.child(userId).setValue(cancelledSubscription).await()
                android.util.Log.d("SubscriptionRepository", "Subscription cancelled for user: $userId")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error cancelling subscription: ${e.message}")
            Result.failure(e)
        }
    }

    // الحصول على خيارات إنشاء المجموعة للمستخدم
    suspend fun getGroupCreationOptions(userId: String): List<GroupCreationOption> {
        return try {
            val subscription = getUserSubscription(userId).getOrNull() ?: UserSubscription(userId = userId)
            val currentTier = if (subscription.isValid()) subscription.tier else SubscriptionTier.FREE

            listOf(
                // الخيار المجاني
                GroupCreationOption(
                    maxMembers = SubscriptionTier.FREE.maxMembers,
                    tier = SubscriptionTier.FREE,
                    isAvailable = true,
                    requiresUpgrade = false
                ),
                // الخيار المدفوع الأول
                GroupCreationOption(
                    maxMembers = SubscriptionTier.PREMIUM.maxMembers,
                    tier = SubscriptionTier.PREMIUM,
                    isAvailable = currentTier == SubscriptionTier.PREMIUM || currentTier == SubscriptionTier.UNLIMITED,
                    requiresUpgrade = currentTier == SubscriptionTier.FREE
                ),
                // الخيار غير المحدود
                GroupCreationOption(
                    maxMembers = SubscriptionTier.UNLIMITED.maxMembers,
                    tier = SubscriptionTier.UNLIMITED,
                    isAvailable = currentTier == SubscriptionTier.UNLIMITED,
                    requiresUpgrade = currentTier != SubscriptionTier.UNLIMITED
                )
            )
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error getting group creation options: ${e.message}")
            // إرجاع الخيار المجاني فقط في حالة الخطأ
            listOf(
                GroupCreationOption(
                    maxMembers = SubscriptionTier.FREE.maxMembers,
                    tier = SubscriptionTier.FREE,
                    isAvailable = true,
                    requiresUpgrade = false
                )
            )
        }
    }

    // التحقق من إمكانية انضمام عضو جديد للمجموعة
    suspend fun canAddMemberToGroup(groupId: String, currentMemberCount: Int, groupOwnerId: String): Boolean {
        return try {
            val subscription = getUserSubscription(groupOwnerId).getOrNull() ?: UserSubscription(userId = groupOwnerId)
            val maxMembers = subscription.getMaxMembers()
            
            val canAdd = currentMemberCount < maxMembers
            android.util.Log.d("SubscriptionRepository", "Can add member to group $groupId: $canAdd (current: $currentMemberCount, max: $maxMembers)")
            canAdd
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error checking member limit: ${e.message}")
            // في حالة الخطأ، استخدم الحد المجاني
            currentMemberCount < SubscriptionTier.FREE.maxMembers
        }
    }

    // حفظ معلومات الدفع
    suspend fun savePaymentInfo(paymentInfo: PaymentInfo): Result<Unit> {
        return try {
            paymentsRef.child(paymentInfo.id).setValue(paymentInfo).await()
            android.util.Log.d("SubscriptionRepository", "Payment info saved: ${paymentInfo.id}")
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error saving payment info: ${e.message}")
            Result.failure(e)
        }
    }

    // تحديث إحصائيات الاستخدام
    suspend fun updateUsageStats(userId: String, stats: UsageStats): Result<Unit> {
        return try {
            usageStatsRef.child(userId).setValue(stats).await()
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error updating usage stats: ${e.message}")
            Result.failure(e)
        }
    }

    // الحصول على إحصائيات الاستخدام
    suspend fun getUsageStats(userId: String): Result<UsageStats> {
        return try {
            val snapshot = usageStatsRef.child(userId).get().await()
            val stats = if (snapshot.exists()) {
                snapshot.getValue(UsageStats::class.java) ?: UsageStats(userId = userId)
            } else {
                UsageStats(userId = userId)
            }
            Result.success(stats)
        } catch (e: Exception) {
            android.util.Log.e("SubscriptionRepository", "Error getting usage stats: ${e.message}")
            Result.failure(e)
        }
    }
}
