package com.newt.anime.navigation

import androidx.compose.runtime.*
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.newt.anime.data.models.Group
import com.newt.anime.ui.screens.*
import com.newt.anime.ui.viewmodel.AuthViewModel
import com.newt.anime.ui.viewmodel.GroupViewModel
import com.newt.anime.ui.viewmodel.SubscriptionViewModel

@Composable
fun AnimeNavigation(
    navController: NavHostController = rememberNavController(),
    authViewModel: AuthViewModel = viewModel(),
    groupViewModel: GroupViewModel = viewModel(),
    subscriptionViewModel: SubscriptionViewModel = viewModel()
) {
    val isAuthenticated by authViewModel.isAuthenticated.collectAsState()
    
    NavHost(
        navController = navController,
        startDestination = if (isAuthenticated) "home" else "login"
    ) {
        composable("login") {
            LoginScreen(
                onNavigateToSignUp = {
                    navController.navigate("signup")
                },
                authViewModel = authViewModel
            )
        }
        
        composable("signup") {
            SignUpScreen(
                onNavigateToLogin = {
                    navController.navigate("login") {
                        popUpTo("signup") { inclusive = true }
                    }
                },
                authViewModel = authViewModel
            )
        }
        
        composable("home") {
            HomeScreen(
                onNavigateToGroup = { group ->
                    // Pass group data through navigation
                    groupViewModel.selectGroup(group)
                    navController.navigate("group")
                },
                onNavigateToSubscription = {
                    navController.navigate("subscription")
                },
                authViewModel = authViewModel,
                groupViewModel = groupViewModel,
                subscriptionViewModel = subscriptionViewModel
            )
        }
        
        composable("group") {
            val currentGroup by groupViewModel.currentGroup.collectAsState()
            val group = currentGroup
            if (group != null) {
                GroupScreen(
                    group = group,
                    onNavigateBack = {
                        navController.popBackStack()
                    },
                    groupViewModel = groupViewModel,
                    subscriptionViewModel = subscriptionViewModel,
                    onNavigateToSubscription = {
                        navController.navigate("subscription")
                    }
                )
            } else {
                // إذا لم تكن هناك مجموعة محددة، ارجع للصفحة الرئيسية
                LaunchedEffect(Unit) {
                    android.util.Log.e("Navigation", "No group selected, returning to home")
                    navController.popBackStack()
                }
            }
        }

        composable("subscription") {
            SubscriptionScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                subscriptionViewModel = subscriptionViewModel
            )
        }
    }
    
    // Handle authentication state changes
    LaunchedEffect(isAuthenticated) {
        if (isAuthenticated) {
            navController.navigate("home") {
                popUpTo("login") { inclusive = true }
                popUpTo("signup") { inclusive = true }
            }
        } else {
            navController.navigate("login") {
                popUpTo("home") { inclusive = true }
                popUpTo("group") { inclusive = true }
            }
        }
    }
}
