{
  "rules": {
    "users": {
      "$uid": {
        ".read": "$uid === auth.uid", // المستخدم يقدر يقرأ بياناته فقط
        ".write": "$uid === auth.uid", // المستخدم يقدر يكتب بياناته فقط
        "fcmToken": {
          ".validate": "newData.isString()" // فقط نصوص يتم قبولها كـ FCM Token
        }
      }
    },
    "groups": {
      ".read": "auth != null", // أي مستخدم مسجل يقدر يقرأ قائمة المجموعات
      ".write": "auth != null", // أي مستخدم مسجل يقدر ينشئ مجموعة جديدة
      ".indexOn": ["code", "ownerId", "name"],

      "$groupId": {
        // فقط الأعضاء المسموحين يشوفون بيانات المجموعة
        ".read": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()",
        ".write": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()",

        // قائمة الأعضاء
        "members": {
          ".read": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()",
          ".write": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()"
        },

        // إدارة الحظر فقط للمالك
        "bannedMembers": {
          ".read": "auth != null && root.child('groups').child($groupId).child('ownerId').val() === auth.uid",
          ".write": "auth != null && root.child('groups').child($groupId).child('ownerId').val() === auth.uid",
          "$userId": {
            ".validate": "newData.isNumber() && newData.val() > 0"
          }
        },

        // 🎬 بيانات الفيديو الحالي - فقط المالك يقدر يعدل
        "currentVideo": {
          ".read": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()",
          ".write": "auth != null && root.child('groups').child($groupId).child('ownerId').val() === auth.uid"
        },



        // 👤 التواجد (presence) لكل عضو
        "presence": {
          ".read": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()",
          ".write": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()",
          "$userId": {
            ".read": "auth != null",
            ".write": "auth != null",
            ".validate": "newData.isNumber() && newData.val() > 0"
          }
        },

        // 💬 المحادثات
        "chat": {
          ".read": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()",
          ".write": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()",
          "$messageId": {
            ".write": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()"
          }
        },

        // ❤️ التفاعلات
        "reactions": {
          ".read": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()",
          ".write": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()",
          "$reactionId": {
            ".write": "auth != null && !root.child('groups').child($groupId).child('bannedMembers').child(auth.uid).exists()"
          }
        }
      }
    }
  }
}
