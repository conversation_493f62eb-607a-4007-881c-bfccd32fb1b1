{"logs": [{"outputFile": "com.newt.anime.app-mergeDebugResources-66:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\85c261ec03eee0a4101b5d8231a63563\\transformed\\material3-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,315,424,553,663,758,870,1014,1132,1288,1373,1478,1573,1675,1793,1919,2029,2165,2302,2437,2616,2744,2867,2995,3120,3216,3314,3434,3563,3663,3768,3870,4011,4159,4265,4367,4447,4543,4638,4758,4844,4933,5034,5114,5200,5300,5406,5501,5602,5690,5799,5900,6004,6142,6231,6336", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,85,88,100,79,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "181,310,419,548,658,753,865,1009,1127,1283,1368,1473,1568,1670,1788,1914,2024,2160,2297,2432,2611,2739,2862,2990,3115,3211,3309,3429,3558,3658,3763,3865,4006,4154,4260,4362,4442,4538,4633,4753,4839,4928,5029,5109,5195,5295,5401,5496,5597,5685,5794,5895,5999,6137,6226,6331,6427"}, "to": {"startLines": "108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9010,9141,9270,9379,9508,9618,9713,9825,9969,10087,10243,10328,10433,10528,10630,10748,10874,10984,11120,11257,11392,11571,11699,11822,11950,12075,12171,12269,12389,12518,12618,12723,12825,12966,13114,13220,13322,13402,13498,13593,13713,13799,13888,13989,14069,14155,14255,14361,14456,14557,14645,14754,14855,14959,15097,15186,15291", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,85,88,100,79,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "9136,9265,9374,9503,9613,9708,9820,9964,10082,10238,10323,10428,10523,10625,10743,10869,10979,11115,11252,11387,11566,11694,11817,11945,12070,12166,12264,12384,12513,12613,12718,12820,12961,13109,13215,13317,13397,13493,13588,13708,13794,13883,13984,14064,14150,14250,14356,14451,14552,14640,14749,14850,14954,15092,15181,15286,15382"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9c9112f3b5828659e41d3d6d877dc27\\transformed\\credentials-1.2.0-rc01\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "19,20", "startColumns": "4,4", "startOffsets": "742,854", "endColumns": "111,113", "endOffsets": "849,963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f341d0dad5e087546904e2a03fd632f3\\transformed\\play-services-base-18.1.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "30,31,32,33,34,35,36,37,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1883,1992,2156,2284,2396,2574,2705,2826,3090,3270,3382,3551,3682,3844,4020,4091,4154", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "1987,2151,2279,2391,2569,2700,2821,2940,3265,3377,3546,3677,3839,4015,4086,4149,4229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf7927868eeddad3b4319378a3201862\\transformed\\browser-1.4.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "48,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "4234,8521,8622,8733", "endColumns": "103,100,110,99", "endOffsets": "4333,8617,8728,8828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8f761d99cc674c08e5b049b2a7bbf06\\transformed\\core-1.16.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "21,22,23,24,25,26,27,173", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "968,1066,1168,1268,1368,1476,1581,16021", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "1061,1163,1263,1363,1471,1576,1694,16117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4366d762d4e78881da860b75d1c4c59f\\transformed\\play-services-basement-18.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "38", "startColumns": "4", "startOffsets": "2945", "endColumns": "144", "endOffsets": "3085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95a85adbd9a0258061c7c940036f2eec\\transformed\\ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,1009,1093,1168,1243,1315,1385,1464,1530", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,74,71,69,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,1004,1088,1163,1238,1310,1380,1459,1525,1645"}, "to": {"startLines": "28,29,49,50,51,106,107,165,166,167,168,169,170,171,172,174,175,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1699,1795,4338,4436,4536,8833,8918,15387,15476,15564,15645,15729,15804,15879,15951,16122,16201,16267", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,74,71,69,78,65,119", "endOffsets": "1790,1878,4431,4531,4618,8913,9005,15471,15559,15640,15724,15799,15874,15946,16016,16196,16262,16382"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dc398bc18186089f1a6c4c36afb914b8\\transformed\\foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,89", "endOffsets": "137,227"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "16387,16474", "endColumns": "86,89", "endOffsets": "16469,16559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6436519ada9dddee292e4f473475ac25\\transformed\\exoplayer-ui-2.19.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,501,692,779,867,942,1032,1118,1197,1262,1366,1470,1539,1609,1681,1750,1877,2005,2138,2211,2295,2371,2448,2535,2623,2689,2754,2807,2867,2915,2976,3048,3118,3183,3254,3319,3377,3443,3507,3573,3625,3687,3763,3839", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,126,127,132,72,83,75,76,86,87,65,64,52,59,47,60,71,69,64,70,64,57,65,63,65,51,61,75,75,55", "endOffsets": "306,496,687,774,862,937,1027,1113,1192,1257,1361,1465,1534,1604,1676,1745,1872,2000,2133,2206,2290,2366,2443,2530,2618,2684,2749,2802,2862,2910,2971,3043,3113,3178,3249,3314,3372,3438,3502,3568,3620,3682,3758,3834,3890"}, "to": {"startLines": "2,11,15,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,361,551,4623,4710,4798,4873,4963,5049,5128,5193,5297,5401,5470,5540,5612,5681,5808,5936,6069,6142,6226,6302,6379,6466,6554,6620,7380,7433,7493,7541,7602,7674,7744,7809,7880,7945,8003,8069,8133,8199,8251,8313,8389,8465", "endLines": "10,14,18,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,126,127,132,72,83,75,76,86,87,65,64,52,59,47,60,71,69,64,70,64,57,65,63,65,51,61,75,75,55", "endOffsets": "356,546,737,4705,4793,4868,4958,5044,5123,5188,5292,5396,5465,5535,5607,5676,5803,5931,6064,6137,6221,6297,6374,6461,6549,6615,6680,7428,7488,7536,7597,7669,7739,7804,7875,7940,7998,8064,8128,8194,8246,8308,8384,8460,8516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5f82ddb128f9d8fc1f894d4feaff6b09\\transformed\\exoplayer-core-2.19.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,270,342,419,486,583,674", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "124,191,265,337,414,481,578,669,745"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6685,6759,6826,6900,6972,7049,7116,7213,7304", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "6754,6821,6895,6967,7044,7111,7208,7299,7375"}}]}]}