# دليل قواعد Firebase Realtime Database لتطبيق الأنمي

## نظرة عامة
هذا الدليل يوضح كيفية إعداد واستخدام قواعد Firebase Realtime Database لتطبيق الأنمي. تم إنشاء ملفين للقواعد:

1. **`FIREBASE-REALTIME-DATABASE-RULES.json`** - قواعد شاملة ومفصلة
2. **`FIREBASE-RULES-SIMPLE.json`** - قواعد مبسطة للاستخدام السريع

## 🏗️ بنية البيانات

### المستخدمين (Users)
```
users/
  {userId}/
    uid: string
    email: string
    displayName: string
    createdAt: number
    fcmToken: string (اختياري)
```

### المجموعات (Groups)
```
groups/
  {groupId}/
    id: string
    name: string
    code: string (6 أحرف)
    ownerId: string
    ownerName: string
    createdAt: number
    
    members/
      {userId}/
        uid: string
        name: string
        joinedAt: number
    
    currentVideo/
      videoUrl: string
      title: string
      isPlaying: boolean
      currentPosition: number
      lastUpdated: number
      syncCommand: string
      syncTimestamp: number
      ownerAction: boolean
      hasStarted: boolean
    
    presence/
      {userId}: timestamp
    
    chat/
      {messageId}/
        id: string
        userId: string
        userName: string
        message: string
        timestamp: number
        type: string
    
    reactions/
      {reactionId}/
        id: string
        userId: string
        userName: string
        emoji: string
        timestamp: number
```

## 🔐 ميزات الأمان

### 1. المصادقة المطلوبة
- جميع العمليات تتطلب مستخدم مسجل (`auth != null`)
- المستخدمون يمكنهم قراءة/كتابة بياناتهم الشخصية فقط

### 2. التحقق من صحة البيانات
- **أسماء المجموعات**: 1-50 حرف
- **رموز المجموعات**: 6 أحرف بالضبط (A-Z, 0-9)
- **رسائل الدردشة**: حد أقصى 500 حرف
- **أسماء المستخدمين**: 1-50 حرف

### 3. صلاحيات محددة
- **مالك المجموعة**: تحكم كامل في المجموعة
- **الأعضاء**: قراءة/كتابة محدودة
- **الدردشة**: المستخدم يمكنه إرسال رسائل باسمه فقط

### 4. الفهرسة للأداء
- فهرسة المجموعات حسب: `code`, `ownerId`, `name`
- فهرسة الرسائل حسب: `timestamp`
- فهرسة الفيديو حسب: `syncTimestamp`, `lastUpdated`

## 📋 كيفية التطبيق

### الطريقة 1: Firebase Console
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروعك
3. اذهب إلى **Realtime Database**
4. انقر على تبويب **Rules**
5. انسخ محتوى أحد الملفين والصقه
6. انقر على **Publish**

### الطريقة 2: Firebase CLI
```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# تهيئة المشروع
firebase init database

# نشر القواعد
firebase deploy --only database
```

## 🎯 اختيار النوع المناسب

### استخدم القواعد الشاملة إذا:
- تريد أمان عالي ومراقبة دقيقة
- التطبيق في بيئة الإنتاج
- تحتاج تسجيل مفصل للأنشطة
- لديك متطلبات امتثال أمني

### استخدم القواعد المبسطة إذا:
- في مرحلة التطوير والاختبار
- تريد إعداد سريع
- التطبيق للاستخدام الشخصي أو المحدود
- لا تحتاج تعقيدات إضافية

## 🧪 اختبار القواعد

### اختبار أساسي
```javascript
// اختبار إنشاء مستخدم
{
  "users": {
    "user123": {
      "uid": "user123",
      "email": "<EMAIL>",
      "displayName": "Test User",
      "createdAt": 1640995200000
    }
  }
}

// اختبار إنشاء مجموعة
{
  "groups": {
    "group123": {
      "id": "group123",
      "name": "مجموعة الأنمي",
      "code": "ABC123",
      "ownerId": "user123",
      "ownerName": "Test User",
      "createdAt": 1640995200000
    }
  }
}
```

### اختبار الدردشة
```javascript
{
  "groups": {
    "group123": {
      "chat": {
        "msg123": {
          "id": "msg123",
          "userId": "user123",
          "userName": "Test User",
          "message": "مرحبا بالجميع!",
          "timestamp": 1640995200000,
          "type": "TEXT"
        }
      }
    }
  }
}
```

## ⚠️ تحذيرات مهمة

### 1. النسخ الاحتياطي
احتفظ بنسخة احتياطية من القواعد الحالية قبل التحديث:
```bash
firebase database:get / > backup.json
```

### 2. الاختبار التدريجي
- اختبر القواعد في بيئة التطوير أولاً
- استخدم Firebase Emulator للاختبار المحلي
- راقب الأخطاء في Firebase Console

### 3. مراقبة الأداء
- راقب استخدام قاعدة البيانات
- تحقق من سرعة الاستعلامات
- راجع تقارير الأمان بانتظام

## 🔧 استكشاف الأخطاء

### خطأ "Permission Denied"
```
PERMISSION_DENIED: Permission denied
```
**الحل**: تحقق من:
- المستخدم مسجل الدخول
- القواعد تسمح بالعملية المطلوبة
- البيانات تتطابق مع قواعد التحقق

### خطأ "Validation Failed"
```
PERMISSION_DENIED: Validation failed
```
**الحل**: تحقق من:
- نوع البيانات صحيح (string, number, boolean)
- طول النصوص ضمن الحدود المسموحة
- القيم المطلوبة موجودة

### خطأ "Index Not Defined"
```
Using an unspecified index
```
**الحل**: أضف الفهرس المطلوب في `.indexOn`

## 📊 مراقبة الاستخدام

### Firebase Console
- **Usage**: مراقبة عدد العمليات
- **Performance**: سرعة الاستعلامات
- **Security**: محاولات الوصول المرفوضة

### تسجيل مخصص
```javascript
// في التطبيق
console.log('Database operation:', operation, result);

// في القواعد (للتطوير فقط)
".validate": "debug() && newData.isString()"
```

## 🚀 التحسينات المستقبلية

1. **إضافة نظام الأدوار**: مشرف، عضو، زائر
2. **حدود زمنية**: انتهاء صلاحية المجموعات
3. **تصفية المحتوى**: منع الكلمات غير المناسبة
4. **إحصائيات متقدمة**: تتبع الاستخدام والأنشطة
5. **نظام الإبلاغ**: الإبلاغ عن المحتوى المخالف
