1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.newt.anime"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:7:5-80
13-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:7:22-77
14    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
14-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:8:5-75
14-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:8:22-72
15    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
15-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7bf79abb5eefabf570c9b9d5649778\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
15-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7bf79abb5eefabf570c9b9d5649778\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
16
17    <permission
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8f761d99cc674c08e5b049b2a7bbf06\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.newt.anime.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8f761d99cc674c08e5b049b2a7bbf06\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8f761d99cc674c08e5b049b2a7bbf06\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.newt.anime.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8f761d99cc674c08e5b049b2a7bbf06\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8f761d99cc674c08e5b049b2a7bbf06\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:10:5-32:19
24        android:allowBackup="true"
24-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:11:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8f761d99cc674c08e5b049b2a7bbf06\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:12:9-65
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:17:9-35
33        android:theme="@style/Theme.Anime" >
33-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:18:9-43
34        <activity
34-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:20:9-31:20
35            android:name="com.newt.anime.MainActivity"
35-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:21:13-41
36            android:configChanges="orientation|screenSize|keyboardHidden"
36-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:24:13-74
37            android:exported="true"
37-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:22:13-36
38            android:label="@string/app_name"
38-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:23:13-45
39            android:theme="@style/Theme.Anime" >
39-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:25:13-47
40            <intent-filter>
40-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:26:13-30:29
41                <action android:name="android.intent.action.MAIN" />
41-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:27:17-69
41-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:27:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:29:17-77
43-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:29:27-74
44            </intent-filter>
45        </activity>
46
47        <service
47-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f55d8baef200b16f577a7ee663617c7\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:8:9-14:19
48            android:name="com.google.firebase.components.ComponentDiscoveryService"
48-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f55d8baef200b16f577a7ee663617c7\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:9:13-84
49            android:directBootAware="true"
49-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
50            android:exported="false" >
50-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f55d8baef200b16f577a7ee663617c7\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:10:13-37
51            <meta-data
51-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f55d8baef200b16f577a7ee663617c7\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:11:13-13:85
52                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
52-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f55d8baef200b16f577a7ee663617c7\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:12:17-119
53                android:value="com.google.firebase.components.ComponentRegistrar" />
53-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f55d8baef200b16f577a7ee663617c7\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:13:17-82
54            <meta-data
54-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
55                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
55-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
56                android:value="com.google.firebase.components.ComponentRegistrar" />
56-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
57            <meta-data
57-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fecb64bf210323d6e99b23a0e30bd39\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:12:13-14:85
58                android:name="com.google.firebase.components:com.google.firebase.storage.ktx.FirebaseStorageLegacyRegistrar"
58-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fecb64bf210323d6e99b23a0e30bd39\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:13:17-125
59                android:value="com.google.firebase.components.ComponentRegistrar" />
59-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fecb64bf210323d6e99b23a0e30bd39\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:14:17-82
60            <meta-data
60-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6da2756227281b4ea2fa2f91a2327ee\transformed\firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
61                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
61-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6da2756227281b4ea2fa2f91a2327ee\transformed\firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
62                android:value="com.google.firebase.components.ComponentRegistrar" />
62-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6da2756227281b4ea2fa2f91a2327ee\transformed\firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
63            <meta-data
63-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6da2756227281b4ea2fa2f91a2327ee\transformed\firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
64                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
64-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6da2756227281b4ea2fa2f91a2327ee\transformed\firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
65                android:value="com.google.firebase.components.ComponentRegistrar" />
65-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6da2756227281b4ea2fa2f91a2327ee\transformed\firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
66            <meta-data
66-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee53e9f74af6515bc2e52704c67fcc5e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
67                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
67-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee53e9f74af6515bc2e52704c67fcc5e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
68                android:value="com.google.firebase.components.ComponentRegistrar" />
68-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee53e9f74af6515bc2e52704c67fcc5e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
69            <meta-data
69-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\689af1533326f19d71171930c5104c1b\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
70                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
70-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\689af1533326f19d71171930c5104c1b\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
71                android:value="com.google.firebase.components.ComponentRegistrar" />
71-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\689af1533326f19d71171930c5104c1b\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
72            <meta-data
72-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\689af1533326f19d71171930c5104c1b\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
73                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
73-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\689af1533326f19d71171930c5104c1b\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
74                android:value="com.google.firebase.components.ComponentRegistrar" />
74-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\689af1533326f19d71171930c5104c1b\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
75            <meta-data
75-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43e19c78424370108c7d354cbd31dace\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
76                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
76-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43e19c78424370108c7d354cbd31dace\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
77                android:value="com.google.firebase.components.ComponentRegistrar" />
77-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43e19c78424370108c7d354cbd31dace\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
78            <meta-data
78-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43e19c78424370108c7d354cbd31dace\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
79                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
79-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43e19c78424370108c7d354cbd31dace\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
80                android:value="com.google.firebase.components.ComponentRegistrar" />
80-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43e19c78424370108c7d354cbd31dace\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
81            <meta-data
81-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8663db35fed1d8fe5a2268b4054a56a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
82                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
82-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8663db35fed1d8fe5a2268b4054a56a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
83                android:value="com.google.firebase.components.ComponentRegistrar" />
83-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8663db35fed1d8fe5a2268b4054a56a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
84            <meta-data
84-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
85                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
85-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
86                android:value="com.google.firebase.components.ComponentRegistrar" />
86-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
87        </service>
88
89        <activity
89-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
90            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
90-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
91            android:excludeFromRecents="true"
91-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
92            android:exported="true"
92-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
93            android:launchMode="singleTask"
93-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
94            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
94-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
95            <intent-filter>
95-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
96                <action android:name="android.intent.action.VIEW" />
96-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
96-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
97
98                <category android:name="android.intent.category.DEFAULT" />
98-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
98-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
99                <category android:name="android.intent.category.BROWSABLE" />
99-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
99-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
100
101                <data
101-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
102                    android:host="firebase.auth"
102-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
103                    android:path="/"
103-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
104                    android:scheme="genericidp" />
104-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
105            </intent-filter>
106        </activity>
107        <activity
107-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
108            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
108-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
109            android:excludeFromRecents="true"
109-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
110            android:exported="true"
110-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
111            android:launchMode="singleTask"
111-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
112            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
112-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
113            <intent-filter>
113-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
114                <action android:name="android.intent.action.VIEW" />
114-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
114-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
115
116                <category android:name="android.intent.category.DEFAULT" />
116-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
116-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
117                <category android:name="android.intent.category.BROWSABLE" />
117-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
117-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
118
119                <data
119-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
120                    android:host="firebase.auth"
120-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
121                    android:path="/"
121-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
122                    android:scheme="recaptcha" />
122-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
123            </intent-filter>
124        </activity>
125
126        <service
126-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
127            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
127-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
128            android:enabled="true"
128-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
129            android:exported="false" >
129-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
130            <meta-data
130-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
131                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
131-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
132                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
132-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
133        </service>
134
135        <activity
135-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
136            android:name="androidx.credentials.playservices.HiddenActivity"
136-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
137            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
137-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
138            android:enabled="true"
138-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
139            android:exported="false"
139-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
140            android:fitsSystemWindows="true"
140-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
141            android:theme="@style/Theme.Hidden" >
141-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
142        </activity>
143        <activity
143-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
144            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
144-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
145            android:excludeFromRecents="true"
145-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
146            android:exported="false"
146-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
147            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
147-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
148        <!--
149            Service handling Google Sign-In user revocation. For apps that do not integrate with
150            Google Sign-In, this service will never be started.
151        -->
152        <service
152-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
153            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
153-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
154            android:exported="true"
154-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
155            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
155-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
156            android:visibleToInstantApps="true" />
156-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
157
158        <activity
158-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f341d0dad5e087546904e2a03fd632f3\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
159            android:name="com.google.android.gms.common.api.GoogleApiActivity"
159-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f341d0dad5e087546904e2a03fd632f3\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
160            android:exported="false"
160-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f341d0dad5e087546904e2a03fd632f3\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
161            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
161-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f341d0dad5e087546904e2a03fd632f3\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
162
163        <provider
163-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
164            android:name="com.google.firebase.provider.FirebaseInitProvider"
164-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
165            android:authorities="com.newt.anime.firebaseinitprovider"
165-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
166            android:directBootAware="true"
166-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
167            android:exported="false"
167-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
168            android:initOrder="100" />
168-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
169        <provider
169-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
170            android:name="androidx.startup.InitializationProvider"
170-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
171            android:authorities="com.newt.anime.androidx-startup"
171-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
172            android:exported="false" >
172-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
173            <meta-data
173-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
174                android:name="androidx.emoji2.text.EmojiCompatInitializer"
174-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
175                android:value="androidx.startup" />
175-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
176            <meta-data
176-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167db888705b0b33989cb385bedff820\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
177                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
177-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167db888705b0b33989cb385bedff820\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
178                android:value="androidx.startup" />
178-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167db888705b0b33989cb385bedff820\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
179            <meta-data
179-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
180                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
180-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
181                android:value="androidx.startup" />
181-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
182        </provider>
183
184        <meta-data
184-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4366d762d4e78881da860b75d1c4c59f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
185            android:name="com.google.android.gms.version"
185-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4366d762d4e78881da860b75d1c4c59f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
186            android:value="@integer/google_play_services_version" />
186-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4366d762d4e78881da860b75d1c4c59f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
187
188        <receiver
188-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
189            android:name="androidx.profileinstaller.ProfileInstallReceiver"
189-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
190            android:directBootAware="false"
190-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
191            android:enabled="true"
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
192            android:exported="true"
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
193            android:permission="android.permission.DUMP" >
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
194            <intent-filter>
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
195                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
196            </intent-filter>
197            <intent-filter>
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
198                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
199            </intent-filter>
200            <intent-filter>
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
201                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
202            </intent-filter>
203            <intent-filter>
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
204                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
205            </intent-filter>
206        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
207        <activity
207-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56e7fd73ffc0fc8f87edb11391c42ff\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
208            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
208-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56e7fd73ffc0fc8f87edb11391c42ff\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
209            android:exported="false"
209-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56e7fd73ffc0fc8f87edb11391c42ff\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
210            android:stateNotNeeded="true"
210-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56e7fd73ffc0fc8f87edb11391c42ff\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
211            android:theme="@style/Theme.PlayCore.Transparent" />
211-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56e7fd73ffc0fc8f87edb11391c42ff\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
212    </application>
213
214</manifest>
