#!/bin/bash

echo "========================================"
echo "        Anime App APK Builder"
echo "========================================"
echo

echo "[1/4] تنظيف المشروع..."
./gradlew clean
if [ $? -ne 0 ]; then
    echo "خطأ في تنظيف المشروع!"
    exit 1
fi

echo
echo "[2/4] بناء إصدار التطوير..."
./gradlew assembleDebug
if [ $? -ne 0 ]; then
    echo "خطأ في بناء إصدار التطوير!"
    exit 1
fi

echo
echo "[3/4] بناء إصدار الإنتاج..."
./gradlew assembleRelease
if [ $? -ne 0 ]; then
    echo "خطأ في بناء إصدار الإنتاج!"
    exit 1
fi

echo
echo "[4/4] نسخ ملفات APK..."

# إنشاء مجلد للإصدارات إذا لم يكن موجوداً
mkdir -p releases

# إنشاء timestamp
timestamp=$(date +"%Y-%m-%d_%H-%M")

# نسخ ملفات APK مع أسماء واضحة وتاريخ
cp "app/build/outputs/apk/debug/app-debug.apk" "releases/anime-app-debug-$timestamp.apk"
cp "app/build/outputs/apk/release/app-release-unsigned.apk" "releases/anime-app-release-unsigned-$timestamp.apk"

# نسخ أحدث إصدار للمجلد الرئيسي
cp "app/build/outputs/apk/debug/app-debug.apk" "anime-app-debug-latest.apk"
cp "app/build/outputs/apk/release/app-release-unsigned.apk" "anime-app-release-unsigned-latest.apk"

echo
echo "========================================"
echo "           البناء مكتمل بنجاح!"
echo "========================================"
echo
echo "ملفات APK المُنشأة:"
echo "- anime-app-debug-latest.apk (للتطوير والاختبار)"
echo "- anime-app-release-unsigned-latest.apk (للإنتاج - غير موقع)"
echo
echo "ملفات مؤرشفة في مجلد releases:"
echo "- anime-app-debug-$timestamp.apk"
echo "- anime-app-release-unsigned-$timestamp.apk"
echo
echo "ملاحظة: إصدار الإنتاج غير موقع ويحتاج توقيع للنشر"
echo
