  Activity android.app  AnimeNavigation android.app.Activity  
AnimeTheme android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  Surface android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  window android.app.Activity  Context android.content  AnimeNavigation android.content.Context  
AnimeTheme android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Surface android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  
setContent android.content.Context  AnimeNavigation android.content.ContextWrapper  
AnimeTheme android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Surface android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  
setContent android.content.ContextWrapper  ActivityInfo android.content.pm  MEDIA_INFO_BUFFERING_END android.media.MediaPlayer  MEDIA_INFO_BUFFERING_START android.media.MediaPlayer  OnCompletionListener android.media.MediaPlayer  OnErrorListener android.media.MediaPlayer  OnInfoListener android.media.MediaPlayer  OnPreparedListener android.media.MediaPlayer  -VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING android.media.MediaPlayer  duration android.media.MediaPlayer  	isLooping android.media.MediaPlayer  setVideoScalingMode android.media.MediaPlayer  setWakeMode android.media.MediaPlayer  <SAM-CONSTRUCTOR> .android.media.MediaPlayer.OnCompletionListener  <SAM-CONSTRUCTOR> )android.media.MediaPlayer.OnErrorListener  <SAM-CONSTRUCTOR> (android.media.MediaPlayer.OnInfoListener  <SAM-CONSTRUCTOR> ,android.media.MediaPlayer.OnPreparedListener  Uri android.net  let android.net.Uri  parse android.net.Uri  toString android.net.Uri  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  PARTIAL_WAKE_LOCK android.os.PowerManager  d android.util.Log  e android.util.Log  w android.util.Log  View android.view  AnimeNavigation  android.view.ContextThemeWrapper  
AnimeTheme  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  OnClickListener android.view.View  isClickable android.view.View  isFocusable android.view.View  setOnClickListener android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  	decorView android.view.Window  WebResourceRequest android.webkit  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  LOAD_CACHE_ELSE_NETWORK android.webkit.WebSettings  MIXED_CONTENT_ALWAYS_ALLOW android.webkit.WebSettings  allowContentAccess android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  allowFileAccessFromFileURLs android.webkit.WebSettings   allowUniversalAccessFromFileURLs android.webkit.WebSettings  builtInZoomControls android.webkit.WebSettings  	cacheMode android.webkit.WebSettings  databaseEnabled android.webkit.WebSettings  displayZoomControls android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  loadWithOverviewMode android.webkit.WebSettings   mediaPlaybackRequiresUserGesture android.webkit.WebSettings  mixedContentMode android.webkit.WebSettings  setSupportZoom android.webkit.WebSettings  useWideViewPort android.webkit.WebSettings  userAgentString android.webkit.WebSettings  android android.webkit.WebView  apply android.webkit.WebView  contains android.webkit.WebView  convertToEmbedUrl android.webkit.WebView  endsWith android.webkit.WebView  evaluateJavascript android.webkit.WebView  let android.webkit.WebView  loadDataWithBaseURL android.webkit.WebView  loadUrl android.webkit.WebView  settings android.webkit.WebView  
trimIndent android.webkit.WebView  
webViewClient android.webkit.WebView  android android.webkit.WebViewClient  onPageFinished android.webkit.WebViewClient  onReceivedError android.webkit.WebViewClient  
trimIndent android.webkit.WebViewClient  	VideoView android.widget  android android.widget.VideoView  apply android.widget.VideoView  currentPosition android.widget.VideoView  duration android.widget.VideoView  isClickable android.widget.VideoView  isFocusable android.widget.VideoView  	isPlaying android.widget.VideoView  let android.widget.VideoView  pause android.widget.VideoView  seekTo android.widget.VideoView  setOnClickListener android.widget.VideoView  setOnCompletionListener android.widget.VideoView  setOnErrorListener android.widget.VideoView  setOnInfoListener android.widget.VideoView  setOnPreparedListener android.widget.VideoView  setVideoPath android.widget.VideoView  start android.widget.VideoView  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  AnimeNavigation #androidx.activity.ComponentActivity  
AnimeTheme #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  AnimeNavigation -androidx.activity.ComponentActivity.Companion  
AnimeTheme -androidx.activity.ComponentActivity.Companion  
MaterialTheme -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  Surface -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  
GetContent 9androidx.activity.result.contract.ActivityResultContracts  	Alignment androidx.compose.animation  AnimatedChatMessage androidx.compose.animation  AnimatedContentScope androidx.compose.animation  AnimatedEmojiReaction androidx.compose.animation  AnimatedVisibility androidx.compose.animation  AnimatedVisibilityScope androidx.compose.animation  Arrangement androidx.compose.animation  AvailableEmojis androidx.compose.animation  Boolean androidx.compose.animation  Box androidx.compose.animation  Card androidx.compose.animation  CardDefaults androidx.compose.animation  ChatMessage androidx.compose.animation  ChatMessageItem androidx.compose.animation  
ChatWindow androidx.compose.animation  CircleShape androidx.compose.animation  Color androidx.compose.animation  Column androidx.compose.animation  
Composable androidx.compose.animation  
EmojiPanel androidx.compose.animation  
EmojiReaction androidx.compose.animation  EnterTransition androidx.compose.animation  ExitTransition androidx.compose.animation  FloatingActionButton androidx.compose.animation  
FontWeight androidx.compose.animation  	GridCells androidx.compose.animation  Icon androidx.compose.animation  
IconButton androidx.compose.animation  Icons androidx.compose.animation  	ImeAction androidx.compose.animation  KeyboardActions androidx.compose.animation  KeyboardOptions androidx.compose.animation  LaunchedEffect androidx.compose.animation  
LazyColumn androidx.compose.animation  LazyVerticalGrid androidx.compose.animation  List androidx.compose.animation  Long androidx.compose.animation  
MaterialTheme androidx.compose.animation  Modifier androidx.compose.animation  OutlinedTextField androidx.compose.animation  OutlinedTextFieldDefaults androidx.compose.animation  RoundedCornerShape androidx.compose.animation  Row androidx.compose.animation  SimpleChatMessageItem androidx.compose.animation  Spacer androidx.compose.animation  	SpanStyle androidx.compose.animation  String androidx.compose.animation  System androidx.compose.animation  Text androidx.compose.animation  	TextAlign androidx.compose.animation  Unit androidx.compose.animation  align androidx.compose.animation  androidx androidx.compose.animation  
background androidx.compose.animation  buildAnnotatedString androidx.compose.animation  
cardColors androidx.compose.animation  	clickable androidx.compose.animation  colors androidx.compose.animation  com androidx.compose.animation  delay androidx.compose.animation  	emptyList androidx.compose.animation  fadeIn androidx.compose.animation  fadeOut androidx.compose.animation  fillMaxSize androidx.compose.animation  fillMaxWidth androidx.compose.animation  filter androidx.compose.animation  forEachIndexed androidx.compose.animation  
formatTime androidx.compose.animation  getUserColor androidx.compose.animation  getValue androidx.compose.animation  height androidx.compose.animation  
isNotBlank androidx.compose.animation  key androidx.compose.animation  let androidx.compose.animation  listOf androidx.compose.animation  mutableStateOf androidx.compose.animation  padding androidx.compose.animation  provideDelegate androidx.compose.animation  remember androidx.compose.animation  reversed androidx.compose.animation  scaleIn androidx.compose.animation  scaleOut androidx.compose.animation  setValue androidx.compose.animation  size androidx.compose.animation  slideInHorizontally androidx.compose.animation  slideInVertically androidx.compose.animation  slideOutHorizontally androidx.compose.animation  slideOutVertically androidx.compose.animation  spacedBy androidx.compose.animation  takeLast androidx.compose.animation  weight androidx.compose.animation  width androidx.compose.animation  widthIn androidx.compose.animation  wrapContentSize androidx.compose.animation  GroupScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  LaunchedEffect /androidx.compose.animation.AnimatedContentScope  LoginScreen /androidx.compose.animation.AnimatedContentScope  SignUpScreen /androidx.compose.animation.AnimatedContentScope  Unit /androidx.compose.animation.AnimatedContentScope  android /androidx.compose.animation.AnimatedContentScope  collectAsState /androidx.compose.animation.AnimatedContentScope  getValue /androidx.compose.animation.AnimatedContentScope  provideDelegate /androidx.compose.animation.AnimatedContentScope  	Alignment 2androidx.compose.animation.AnimatedVisibilityScope  Card 2androidx.compose.animation.AnimatedVisibilityScope  CardDefaults 2androidx.compose.animation.AnimatedVisibilityScope  
ChatWindow 2androidx.compose.animation.AnimatedVisibilityScope  Color 2androidx.compose.animation.AnimatedVisibilityScope  Column 2androidx.compose.animation.AnimatedVisibilityScope  
EmojiPanel 2androidx.compose.animation.AnimatedVisibilityScope  
FontWeight 2androidx.compose.animation.AnimatedVisibilityScope  Modifier 2androidx.compose.animation.AnimatedVisibilityScope  RoundedCornerShape 2androidx.compose.animation.AnimatedVisibilityScope  	SpanStyle 2androidx.compose.animation.AnimatedVisibilityScope  Text 2androidx.compose.animation.AnimatedVisibilityScope  	TextAlign 2androidx.compose.animation.AnimatedVisibilityScope  buildAnnotatedString 2androidx.compose.animation.AnimatedVisibilityScope  
cardColors 2androidx.compose.animation.AnimatedVisibilityScope  dp 2androidx.compose.animation.AnimatedVisibilityScope  getUserColor 2androidx.compose.animation.AnimatedVisibilityScope  
isNotBlank 2androidx.compose.animation.AnimatedVisibilityScope  padding 2androidx.compose.animation.AnimatedVisibilityScope  sp 2androidx.compose.animation.AnimatedVisibilityScope  widthIn 2androidx.compose.animation.AnimatedVisibilityScope  	withStyle 2androidx.compose.animation.AnimatedVisibilityScope  plus *androidx.compose.animation.EnterTransition  plus )androidx.compose.animation.ExitTransition  newt androidx.compose.animation.com  anime #androidx.compose.animation.com.newt  data )androidx.compose.animation.com.newt.anime  models .androidx.compose.animation.com.newt.anime.data  ChatMessage 5androidx.compose.animation.com.newt.anime.data.models  	TweenSpec androidx.compose.animation.core  animate androidx.compose.animation.core  tween androidx.compose.animation.core  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  Activity "androidx.compose.foundation.layout  ActivityResultContracts "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  AndroidView "androidx.compose.foundation.layout  AnimatedChatMessage "androidx.compose.foundation.layout  AnimatedEmojiReaction "androidx.compose.foundation.layout  AnimatedVisibility "androidx.compose.foundation.layout  AnnotatedString "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
AuthViewModel "androidx.compose.foundation.layout  AvailableEmojis "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  ChatMessage "androidx.compose.foundation.layout  ChatMessageItem "androidx.compose.foundation.layout  ChatOverlay "androidx.compose.foundation.layout  
ChatWindow "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Close "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Dispatchers "androidx.compose.foundation.layout  DisposableEffect "androidx.compose.foundation.layout  DropdownMenu "androidx.compose.foundation.layout  DropdownMenuItem "androidx.compose.foundation.layout  
EmojiPanel "androidx.compose.foundation.layout  
EmojiReaction "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  	ExoPlayer "androidx.compose.foundation.layout  ExoVideoPlayer "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  FirebaseAuth "androidx.compose.foundation.layout  FirebaseStorage "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  	GridCells "androidx.compose.foundation.layout  Group "androidx.compose.foundation.layout  	GroupCard "androidx.compose.foundation.layout  GroupRepository "androidx.compose.foundation.layout  GroupViewModel "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  ImageVector "androidx.compose.foundation.layout  	ImeAction "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  KeyboardActions "androidx.compose.foundation.layout  KeyboardArrowLeft "androidx.compose.foundation.layout  KeyboardArrowRight "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LazyVerticalGrid "androidx.compose.foundation.layout  LinearProgressIndicator "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  Long "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  	MediaItem "androidx.compose.foundation.layout  Member "androidx.compose.foundation.layout  MessageOverlay "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  OutlinedTextFieldDefaults "androidx.compose.foundation.layout  PasswordVisualTransformation "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  
PlayerView "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  SimpleChatMessageItem "androidx.compose.foundation.layout  SimpleControlButton "androidx.compose.foundation.layout  Slider "androidx.compose.foundation.layout  SliderDefaults "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  	SpanStyle "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  System "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  UUID "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  VideoSession "androidx.compose.foundation.layout  WebView "androidx.compose.foundation.layout  
WebViewClient "androidx.compose.foundation.layout  WindowCompat "androidx.compose.foundation.layout  WindowInsetsCompat "androidx.compose.foundation.layout  WindowInsetsControllerCompat "androidx.compose.foundation.layout  
YouTubeButton "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  buildAnnotatedString "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  colors "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  contains "androidx.compose.foundation.layout  convertToEmbedUrl "androidx.compose.foundation.layout  delay "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  endsWith "androidx.compose.foundation.layout  fadeIn "androidx.compose.foundation.layout  fadeOut "androidx.compose.foundation.layout  
fillMaxHeight "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  filter "androidx.compose.foundation.layout  forEachIndexed "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  
formatTime "androidx.compose.foundation.layout  getUserColor "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  heightIn "androidx.compose.foundation.layout  ifEmpty "androidx.compose.foundation.layout  isEmpty "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  key "androidx.compose.foundation.layout  kotlin "androidx.compose.foundation.layout  kotlinx "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  maxOf "androidx.compose.foundation.layout  minOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  reversed "androidx.compose.foundation.layout  scaleIn "androidx.compose.foundation.layout  scaleOut "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  slideInHorizontally "androidx.compose.foundation.layout  slideInVertically "androidx.compose.foundation.layout  slideOutHorizontally "androidx.compose.foundation.layout  slideOutVertically "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  substringAfter "androidx.compose.foundation.layout  substringBefore "androidx.compose.foundation.layout  takeIf "androidx.compose.foundation.layout  takeLast "androidx.compose.foundation.layout  toList "androidx.compose.foundation.layout  
trimIndent "androidx.compose.foundation.layout  uploadToFirebase "androidx.compose.foundation.layout  	uppercase "androidx.compose.foundation.layout  verticalGradient "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  widthIn "androidx.compose.foundation.layout  wrapContentSize "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  AndroidView +androidx.compose.foundation.layout.BoxScope  AnimatedChatMessage +androidx.compose.foundation.layout.BoxScope  AnimatedEmojiReaction +androidx.compose.foundation.layout.BoxScope  AnimatedVisibility +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  ChatOverlay +androidx.compose.foundation.layout.BoxScope  
ChatWindow +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Close +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  Delete +androidx.compose.foundation.layout.BoxScope  DropdownMenu +androidx.compose.foundation.layout.BoxScope  DropdownMenuItem +androidx.compose.foundation.layout.BoxScope  
EmojiPanel +androidx.compose.foundation.layout.BoxScope  	ExoPlayer +androidx.compose.foundation.layout.BoxScope  ExoVideoPlayer +androidx.compose.foundation.layout.BoxScope  FloatingActionButton +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  KeyboardArrowLeft +androidx.compose.foundation.layout.BoxScope  KeyboardArrowRight +androidx.compose.foundation.layout.BoxScope  LinearProgressIndicator +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  	MediaItem +androidx.compose.foundation.layout.BoxScope  MessageOverlay +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  MoreVert +androidx.compose.foundation.layout.BoxScope  	PlayArrow +androidx.compose.foundation.layout.BoxScope  
PlayerView +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  SimpleControlButton +androidx.compose.foundation.layout.BoxScope  Slider +androidx.compose.foundation.layout.BoxScope  SliderDefaults +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  Uri +androidx.compose.foundation.layout.BoxScope  WebView +androidx.compose.foundation.layout.BoxScope  
YouTubeButton +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  android +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  	clickable +androidx.compose.foundation.layout.BoxScope  colors +androidx.compose.foundation.layout.BoxScope  contains +androidx.compose.foundation.layout.BoxScope  convertToEmbedUrl +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  endsWith +androidx.compose.foundation.layout.BoxScope  fadeIn +androidx.compose.foundation.layout.BoxScope  fadeOut +androidx.compose.foundation.layout.BoxScope  
fillMaxHeight +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  forEachIndexed +androidx.compose.foundation.layout.BoxScope  
formatTime +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  isEmpty +androidx.compose.foundation.layout.BoxScope  
isNotBlank +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  key +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  maxOf +androidx.compose.foundation.layout.BoxScope  minOf +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  slideInHorizontally +androidx.compose.foundation.layout.BoxScope  slideInVertically +androidx.compose.foundation.layout.BoxScope  slideOutHorizontally +androidx.compose.foundation.layout.BoxScope  slideOutVertically +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  takeLast +androidx.compose.foundation.layout.BoxScope  
trimIndent +androidx.compose.foundation.layout.BoxScope  verticalGradient +androidx.compose.foundation.layout.BoxScope  weight +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  AndroidView .androidx.compose.foundation.layout.ColumnScope  AnnotatedString .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  AvailableEmojis .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Close .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  DropdownMenu .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  	ExitToApp .androidx.compose.foundation.layout.ColumnScope  ExoVideoPlayer .androidx.compose.foundation.layout.ColumnScope  FloatingActionButton .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  	GridCells .androidx.compose.foundation.layout.ColumnScope  	GroupCard .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  	ImeAction .androidx.compose.foundation.layout.ColumnScope  KeyboardActions .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowLeft .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowRight .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  LaunchedEffect .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LazyVerticalGrid .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  MoreVert .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  OutlinedTextFieldDefaults .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  Person .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Send .androidx.compose.foundation.layout.ColumnScope  Share .androidx.compose.foundation.layout.ColumnScope  SimpleChatMessageItem .androidx.compose.foundation.layout.ColumnScope  SimpleControlButton .androidx.compose.foundation.layout.ColumnScope  Slider .androidx.compose.foundation.layout.ColumnScope  SliderDefaults .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  	SpanStyle .androidx.compose.foundation.layout.ColumnScope  Star .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  	TopAppBar .androidx.compose.foundation.layout.ColumnScope  Unit .androidx.compose.foundation.layout.ColumnScope  WebView .androidx.compose.foundation.layout.ColumnScope  android .androidx.compose.foundation.layout.ColumnScope  apply .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  buildAnnotatedString .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope  contains .androidx.compose.foundation.layout.ColumnScope  convertToEmbedUrl .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  endsWith .androidx.compose.foundation.layout.ColumnScope  
fillMaxHeight .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  
formatTime .androidx.compose.foundation.layout.ColumnScope  getUserColor .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  isEmpty .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  kotlinx .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  maxOf .androidx.compose.foundation.layout.ColumnScope  minOf .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  reversed .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  takeLast .androidx.compose.foundation.layout.ColumnScope  toList .androidx.compose.foundation.layout.ColumnScope  
trimIndent .androidx.compose.foundation.layout.ColumnScope  uploadToFirebase .androidx.compose.foundation.layout.ColumnScope  	uppercase .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  	withStyle .androidx.compose.foundation.layout.ColumnScope  wrapContentSize .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  AnnotatedString +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Close +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  DropdownMenu +androidx.compose.foundation.layout.RowScope  DropdownMenuItem +androidx.compose.foundation.layout.RowScope  	ExitToApp +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  	ImeAction +androidx.compose.foundation.layout.RowScope  KeyboardActions +androidx.compose.foundation.layout.RowScope  KeyboardArrowLeft +androidx.compose.foundation.layout.RowScope  KeyboardArrowRight +androidx.compose.foundation.layout.RowScope  KeyboardOptions +androidx.compose.foundation.layout.RowScope  LinearProgressIndicator +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  MoreVert +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  OutlinedTextField +androidx.compose.foundation.layout.RowScope  OutlinedTextFieldDefaults +androidx.compose.foundation.layout.RowScope  Person +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Send +androidx.compose.foundation.layout.RowScope  Share +androidx.compose.foundation.layout.RowScope  SimpleControlButton +androidx.compose.foundation.layout.RowScope  Slider +androidx.compose.foundation.layout.RowScope  SliderDefaults +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Star +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  
YouTubeButton +androidx.compose.foundation.layout.RowScope  android +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  	clickable +androidx.compose.foundation.layout.RowScope  colors +androidx.compose.foundation.layout.RowScope  convertToEmbedUrl +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  
fillMaxHeight +androidx.compose.foundation.layout.RowScope  fillMaxWidth +androidx.compose.foundation.layout.RowScope  
formatTime +androidx.compose.foundation.layout.RowScope  getUserColor +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  
isNotBlank +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  maxOf +androidx.compose.foundation.layout.RowScope  minOf +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  spacedBy +androidx.compose.foundation.layout.RowScope  
trimIndent +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  webkit *androidx.compose.foundation.layout.android  widget *androidx.compose.foundation.layout.android  WebResourceRequest 1androidx.compose.foundation.layout.android.webkit  WebView 1androidx.compose.foundation.layout.android.webkit  
WebViewClient 1androidx.compose.foundation.layout.android.webkit  	VideoView 1androidx.compose.foundation.layout.android.widget  google &androidx.compose.foundation.layout.com  newt &androidx.compose.foundation.layout.com  android -androidx.compose.foundation.layout.com.google  
exoplayer2 5androidx.compose.foundation.layout.com.google.android  Player @androidx.compose.foundation.layout.com.google.android.exoplayer2  Listener Gandroidx.compose.foundation.layout.com.google.android.exoplayer2.Player  anime +androidx.compose.foundation.layout.com.newt  data 1androidx.compose.foundation.layout.com.newt.anime  models 6androidx.compose.foundation.layout.com.newt.anime.data  ChatMessage =androidx.compose.foundation.layout.com.newt.anime.data.models  
EmojiReaction =androidx.compose.foundation.layout.com.newt.anime.data.models  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Add .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  AnnotatedString .androidx.compose.foundation.lazy.LazyItemScope  Arrangement .androidx.compose.foundation.lazy.LazyItemScope  Box .androidx.compose.foundation.lazy.LazyItemScope  Button .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyItemScope  CardDefaults .androidx.compose.foundation.lazy.LazyItemScope  ChatMessageItem .androidx.compose.foundation.lazy.LazyItemScope  Color .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  Delete .androidx.compose.foundation.lazy.LazyItemScope  ExoVideoPlayer .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  	GroupCard .androidx.compose.foundation.lazy.LazyItemScope  Icon .androidx.compose.foundation.lazy.LazyItemScope  
IconButton .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  Person .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  Share .androidx.compose.foundation.lazy.LazyItemScope  SimpleChatMessageItem .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  Star .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  android .androidx.compose.foundation.lazy.LazyItemScope  
cardColors .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  isEmpty .androidx.compose.foundation.lazy.LazyItemScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  size .androidx.compose.foundation.lazy.LazyItemScope  sp .androidx.compose.foundation.lazy.LazyItemScope  spacedBy .androidx.compose.foundation.lazy.LazyItemScope  weight .androidx.compose.foundation.lazy.LazyItemScope  width .androidx.compose.foundation.lazy.LazyItemScope  Add .androidx.compose.foundation.lazy.LazyListScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  AnnotatedString .androidx.compose.foundation.lazy.LazyListScope  Arrangement .androidx.compose.foundation.lazy.LazyListScope  Box .androidx.compose.foundation.lazy.LazyListScope  Button .androidx.compose.foundation.lazy.LazyListScope  Card .androidx.compose.foundation.lazy.LazyListScope  CardDefaults .androidx.compose.foundation.lazy.LazyListScope  ChatMessageItem .androidx.compose.foundation.lazy.LazyListScope  Color .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  Delete .androidx.compose.foundation.lazy.LazyListScope  ExoVideoPlayer .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  	GroupCard .androidx.compose.foundation.lazy.LazyListScope  Icon .androidx.compose.foundation.lazy.LazyListScope  
IconButton .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  Person .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  Share .androidx.compose.foundation.lazy.LazyListScope  SimpleChatMessageItem .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  Star .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  android .androidx.compose.foundation.lazy.LazyListScope  
cardColors .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  isEmpty .androidx.compose.foundation.lazy.LazyListScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  reversed .androidx.compose.foundation.lazy.LazyListScope  size .androidx.compose.foundation.lazy.LazyListScope  sp .androidx.compose.foundation.lazy.LazyListScope  spacedBy .androidx.compose.foundation.lazy.LazyListScope  takeLast .androidx.compose.foundation.lazy.LazyListScope  toList .androidx.compose.foundation.lazy.LazyListScope  weight .androidx.compose.foundation.lazy.LazyListScope  width .androidx.compose.foundation.lazy.LazyListScope  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  Fixed /androidx.compose.foundation.lazy.grid.GridCells  	Alignment 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  Box 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  Color 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  Modifier 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  RoundedCornerShape 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  Text 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  
background 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  	clickable 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  dp 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  size 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  sp 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  	Alignment 3androidx.compose.foundation.lazy.grid.LazyGridScope  AvailableEmojis 3androidx.compose.foundation.lazy.grid.LazyGridScope  Box 3androidx.compose.foundation.lazy.grid.LazyGridScope  Color 3androidx.compose.foundation.lazy.grid.LazyGridScope  Modifier 3androidx.compose.foundation.lazy.grid.LazyGridScope  RoundedCornerShape 3androidx.compose.foundation.lazy.grid.LazyGridScope  Text 3androidx.compose.foundation.lazy.grid.LazyGridScope  
background 3androidx.compose.foundation.lazy.grid.LazyGridScope  	clickable 3androidx.compose.foundation.lazy.grid.LazyGridScope  dp 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  size 3androidx.compose.foundation.lazy.grid.LazyGridScope  sp 3androidx.compose.foundation.lazy.grid.LazyGridScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardActionScope  androidx.compose.foundation.text  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  Close ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  	ExitToApp ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowLeft ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowRight ,androidx.compose.material.icons.Icons.Filled  MoreVert ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Send ,androidx.compose.material.icons.Icons.Filled  Share ,androidx.compose.material.icons.Icons.Filled  Star ,androidx.compose.material.icons.Icons.Filled  Activity &androidx.compose.material.icons.filled  Add &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  AndroidView &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  Boolean &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  ChatOverlay &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  CircularProgressIndicator &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  DisposableEffect &androidx.compose.material.icons.filled  	Exception &androidx.compose.material.icons.filled  	ExitToApp &androidx.compose.material.icons.filled  	ExoPlayer &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  ImageVector &androidx.compose.material.icons.filled  Int &androidx.compose.material.icons.filled  KeyboardArrowLeft &androidx.compose.material.icons.filled  KeyboardArrowRight &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  LinearProgressIndicator &androidx.compose.material.icons.filled  List &androidx.compose.material.icons.filled  Long &androidx.compose.material.icons.filled  	MediaItem &androidx.compose.material.icons.filled  MessageOverlay &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  MoreVert &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  
PlayerView &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Send &androidx.compose.material.icons.filled  Share &androidx.compose.material.icons.filled  SimpleControlButton &androidx.compose.material.icons.filled  Slider &androidx.compose.material.icons.filled  SliderDefaults &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  Star &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  Uri &androidx.compose.material.icons.filled  VideoSession &androidx.compose.material.icons.filled  WindowCompat &androidx.compose.material.icons.filled  WindowInsetsCompat &androidx.compose.material.icons.filled  WindowInsetsControllerCompat &androidx.compose.material.icons.filled  
YouTubeButton &androidx.compose.material.icons.filled  align &androidx.compose.material.icons.filled  android &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  apply &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  	clickable &androidx.compose.material.icons.filled  colors &androidx.compose.material.icons.filled  com &androidx.compose.material.icons.filled  delay &androidx.compose.material.icons.filled  	emptyList &androidx.compose.material.icons.filled  
fillMaxHeight &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  format &androidx.compose.material.icons.filled  
formatTime &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  isEmpty &androidx.compose.material.icons.filled  
isNotEmpty &androidx.compose.material.icons.filled  kotlin &androidx.compose.material.icons.filled  kotlinx &androidx.compose.material.icons.filled  let &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  maxOf &androidx.compose.material.icons.filled  minOf &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  spacedBy &androidx.compose.material.icons.filled  takeIf &androidx.compose.material.icons.filled  verticalGradient &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  widget .androidx.compose.material.icons.filled.android  	VideoView 5androidx.compose.material.icons.filled.android.widget  google *androidx.compose.material.icons.filled.com  newt *androidx.compose.material.icons.filled.com  android 1androidx.compose.material.icons.filled.com.google  
exoplayer2 9androidx.compose.material.icons.filled.com.google.android  Player Dandroidx.compose.material.icons.filled.com.google.android.exoplayer2  Listener Kandroidx.compose.material.icons.filled.com.google.android.exoplayer2.Player  anime /androidx.compose.material.icons.filled.com.newt  data 5androidx.compose.material.icons.filled.com.newt.anime  models :androidx.compose.material.icons.filled.com.newt.anime.data  ChatMessage Aandroidx.compose.material.icons.filled.com.newt.anime.data.models  
EmojiReaction Aandroidx.compose.material.icons.filled.com.newt.anime.data.models  Activity androidx.compose.material3  ActivityResultContracts androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  AndroidView androidx.compose.material3  AnimatedChatMessage androidx.compose.material3  AnimatedEmojiReaction androidx.compose.material3  AnimatedVisibility androidx.compose.material3  AnnotatedString androidx.compose.material3  Arrangement androidx.compose.material3  
AuthViewModel androidx.compose.material3  AvailableEmojis androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  ChatMessage androidx.compose.material3  ChatMessageItem androidx.compose.material3  ChatOverlay androidx.compose.material3  
ChatWindow androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Close androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  Dispatchers androidx.compose.material3  DisposableEffect androidx.compose.material3  DropdownMenu androidx.compose.material3  DropdownMenuItem androidx.compose.material3  
EmojiPanel androidx.compose.material3  
EmojiReaction androidx.compose.material3  	Exception androidx.compose.material3  	ExoPlayer androidx.compose.material3  ExoVideoPlayer androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FirebaseAuth androidx.compose.material3  FirebaseStorage androidx.compose.material3  FloatingActionButton androidx.compose.material3  
FontWeight androidx.compose.material3  	GridCells androidx.compose.material3  Group androidx.compose.material3  	GroupCard androidx.compose.material3  GroupRepository androidx.compose.material3  GroupViewModel androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  ImageVector androidx.compose.material3  	ImeAction androidx.compose.material3  Int androidx.compose.material3  KeyboardActions androidx.compose.material3  KeyboardArrowLeft androidx.compose.material3  KeyboardArrowRight androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  LazyVerticalGrid androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  List androidx.compose.material3  Long androidx.compose.material3  
MaterialTheme androidx.compose.material3  	MediaItem androidx.compose.material3  Member androidx.compose.material3  MessageOverlay androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  PasswordVisualTransformation androidx.compose.material3  	PlayArrow androidx.compose.material3  
PlayerView androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  SimpleChatMessageItem androidx.compose.material3  SimpleControlButton androidx.compose.material3  Slider androidx.compose.material3  SliderColors androidx.compose.material3  SliderDefaults androidx.compose.material3  Spacer androidx.compose.material3  	SpanStyle androidx.compose.material3  String androidx.compose.material3  Surface androidx.compose.material3  System androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  TextFieldColors androidx.compose.material3  	TopAppBar androidx.compose.material3  
Typography androidx.compose.material3  UUID androidx.compose.material3  Unit androidx.compose.material3  Uri androidx.compose.material3  VideoSession androidx.compose.material3  WebView androidx.compose.material3  
WebViewClient androidx.compose.material3  WindowCompat androidx.compose.material3  WindowInsetsCompat androidx.compose.material3  WindowInsetsControllerCompat androidx.compose.material3  
YouTubeButton androidx.compose.material3  align androidx.compose.material3  android androidx.compose.material3  androidx androidx.compose.material3  apply androidx.compose.material3  
background androidx.compose.material3  buildAnnotatedString androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  	clickable androidx.compose.material3  collectAsState androidx.compose.material3  colors androidx.compose.material3  com androidx.compose.material3  contains androidx.compose.material3  convertToEmbedUrl androidx.compose.material3  darkColorScheme androidx.compose.material3  delay androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  endsWith androidx.compose.material3  fadeIn androidx.compose.material3  fadeOut androidx.compose.material3  
fillMaxHeight androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  filter androidx.compose.material3  forEachIndexed androidx.compose.material3  format androidx.compose.material3  
formatTime androidx.compose.material3  getUserColor androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  heightIn androidx.compose.material3  ifEmpty androidx.compose.material3  isEmpty androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  key androidx.compose.material3  kotlin androidx.compose.material3  kotlinx androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  maxOf androidx.compose.material3  minOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  reversed androidx.compose.material3  scaleIn androidx.compose.material3  scaleOut androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  slideInHorizontally androidx.compose.material3  slideInVertically androidx.compose.material3  slideOutHorizontally androidx.compose.material3  slideOutVertically androidx.compose.material3  spacedBy androidx.compose.material3  substringAfter androidx.compose.material3  substringBefore androidx.compose.material3  takeIf androidx.compose.material3  takeLast androidx.compose.material3  toList androidx.compose.material3  
trimIndent androidx.compose.material3  uploadToFirebase androidx.compose.material3  	uppercase androidx.compose.material3  verticalGradient androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  widthIn androidx.compose.material3  wrapContentSize androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  	secondary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  colors 4androidx.compose.material3.OutlinedTextFieldDefaults  colors )androidx.compose.material3.SliderDefaults  webkit "androidx.compose.material3.android  widget "androidx.compose.material3.android  WebResourceRequest )androidx.compose.material3.android.webkit  WebView )androidx.compose.material3.android.webkit  
WebViewClient )androidx.compose.material3.android.webkit  	VideoView )androidx.compose.material3.android.widget  google androidx.compose.material3.com  newt androidx.compose.material3.com  android %androidx.compose.material3.com.google  
exoplayer2 -androidx.compose.material3.com.google.android  Player 8androidx.compose.material3.com.google.android.exoplayer2  Listener ?androidx.compose.material3.com.google.android.exoplayer2.Player  anime #androidx.compose.material3.com.newt  data )androidx.compose.material3.com.newt.anime  models .androidx.compose.material3.com.newt.anime.data  ChatMessage 5androidx.compose.material3.com.newt.anime.data.models  
EmojiReaction 5androidx.compose.material3.com.newt.anime.data.models  Activity androidx.compose.runtime  ActivityResultContracts androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  AndroidView androidx.compose.runtime  AnimatedChatMessage androidx.compose.runtime  AnimatedEmojiReaction androidx.compose.runtime  AnimatedVisibility androidx.compose.runtime  AnnotatedString androidx.compose.runtime  Arrangement androidx.compose.runtime  
AuthViewModel androidx.compose.runtime  AvailableEmojis androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  ChatMessage androidx.compose.runtime  ChatMessageItem androidx.compose.runtime  ChatOverlay androidx.compose.runtime  
ChatWindow androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Close androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  Dispatchers androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  DropdownMenu androidx.compose.runtime  DropdownMenuItem androidx.compose.runtime  
EmojiPanel androidx.compose.runtime  
EmojiReaction androidx.compose.runtime  	Exception androidx.compose.runtime  	ExoPlayer androidx.compose.runtime  ExoVideoPlayer androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  FirebaseAuth androidx.compose.runtime  FirebaseStorage androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  
FontWeight androidx.compose.runtime  	GridCells androidx.compose.runtime  Group androidx.compose.runtime  	GroupCard androidx.compose.runtime  GroupRepository androidx.compose.runtime  GroupScreen androidx.compose.runtime  GroupViewModel androidx.compose.runtime  
HomeScreen androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  ImageVector androidx.compose.runtime  	ImeAction androidx.compose.runtime  Int androidx.compose.runtime  KeyboardActions androidx.compose.runtime  KeyboardArrowLeft androidx.compose.runtime  KeyboardArrowRight androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LazyVerticalGrid androidx.compose.runtime  LinearProgressIndicator androidx.compose.runtime  List androidx.compose.runtime  LoginScreen androidx.compose.runtime  Long androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  	MediaItem androidx.compose.runtime  Member androidx.compose.runtime  MessageOverlay androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  NavHostController androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  OutlinedTextFieldDefaults androidx.compose.runtime  PasswordVisualTransformation androidx.compose.runtime  	PlayArrow androidx.compose.runtime  
PlayerView androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  SignUpScreen androidx.compose.runtime  SimpleChatMessageItem androidx.compose.runtime  SimpleControlButton androidx.compose.runtime  Slider androidx.compose.runtime  SliderDefaults androidx.compose.runtime  Spacer androidx.compose.runtime  	SpanStyle androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  System androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  	TopAppBar androidx.compose.runtime  UUID androidx.compose.runtime  Unit androidx.compose.runtime  Uri androidx.compose.runtime  VideoSession androidx.compose.runtime  WebView androidx.compose.runtime  
WebViewClient androidx.compose.runtime  WindowCompat androidx.compose.runtime  WindowInsetsCompat androidx.compose.runtime  WindowInsetsControllerCompat androidx.compose.runtime  
YouTubeButton androidx.compose.runtime  align androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  apply androidx.compose.runtime  
background androidx.compose.runtime  buildAnnotatedString androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  	clickable androidx.compose.runtime  collectAsState androidx.compose.runtime  colors androidx.compose.runtime  com androidx.compose.runtime  contains androidx.compose.runtime  convertToEmbedUrl androidx.compose.runtime  delay androidx.compose.runtime  	emptyList androidx.compose.runtime  endsWith androidx.compose.runtime  fadeIn androidx.compose.runtime  fadeOut androidx.compose.runtime  
fillMaxHeight androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  filter androidx.compose.runtime  forEachIndexed androidx.compose.runtime  format androidx.compose.runtime  
formatTime androidx.compose.runtime  getUserColor androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  heightIn androidx.compose.runtime  ifEmpty androidx.compose.runtime  isEmpty androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  key androidx.compose.runtime  kotlin androidx.compose.runtime  kotlinx androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  maxOf androidx.compose.runtime  minOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  reversed androidx.compose.runtime  scaleIn androidx.compose.runtime  scaleOut androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  slideInHorizontally androidx.compose.runtime  slideInVertically androidx.compose.runtime  slideOutHorizontally androidx.compose.runtime  slideOutVertically androidx.compose.runtime  spacedBy androidx.compose.runtime  substringAfter androidx.compose.runtime  substringBefore androidx.compose.runtime  takeIf androidx.compose.runtime  takeLast androidx.compose.runtime  toList androidx.compose.runtime  
trimIndent androidx.compose.runtime  uploadToFirebase androidx.compose.runtime  	uppercase androidx.compose.runtime  verticalGradient androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  widthIn androidx.compose.runtime  wrapContentSize androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  System .androidx.compose.runtime.DisposableEffectScope  WindowCompat .androidx.compose.runtime.DisposableEffectScope  WindowInsetsCompat .androidx.compose.runtime.DisposableEffectScope  android .androidx.compose.runtime.DisposableEffectScope  	onDispose .androidx.compose.runtime.DisposableEffectScope  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  webkit  androidx.compose.runtime.android  widget  androidx.compose.runtime.android  WebResourceRequest 'androidx.compose.runtime.android.webkit  WebView 'androidx.compose.runtime.android.webkit  
WebViewClient 'androidx.compose.runtime.android.webkit  	VideoView 'androidx.compose.runtime.android.widget  google androidx.compose.runtime.com  newt androidx.compose.runtime.com  android #androidx.compose.runtime.com.google  
exoplayer2 +androidx.compose.runtime.com.google.android  Player 6androidx.compose.runtime.com.google.android.exoplayer2  Listener =androidx.compose.runtime.com.google.android.exoplayer2.Player  anime !androidx.compose.runtime.com.newt  data 'androidx.compose.runtime.com.newt.anime  models ,androidx.compose.runtime.com.newt.anime.data  ChatMessage 3androidx.compose.runtime.com.newt.anime.data.models  
EmojiReaction 3androidx.compose.runtime.com.newt.anime.data.models  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  BottomCenter androidx.compose.ui.Alignment  	BottomEnd androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterStart androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  	TopCenter androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  TopStart androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  	BottomEnd 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterStart 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	TopCenter 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  TopStart 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  
fillMaxHeight androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  heightIn androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  widthIn androidx.compose.ui.Modifier  wrapContentSize androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  	clickable &androidx.compose.ui.Modifier.Companion  
fillMaxHeight &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  heightIn &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  widthIn &androidx.compose.ui.Modifier.Companion  wrapContentSize &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  ClipboardManager androidx.compose.ui.platform  LocalClipboardManager androidx.compose.ui.platform  LocalContext androidx.compose.ui.platform  LocalSoftwareKeyboardController androidx.compose.ui.platform  SoftwareKeyboardController androidx.compose.ui.platform  setText -androidx.compose.ui.platform.ClipboardManager  hide 7androidx.compose.ui.platform.SoftwareKeyboardController  AnnotatedString androidx.compose.ui.text  	SpanStyle androidx.compose.ui.text  	TextStyle androidx.compose.ui.text  buildAnnotatedString androidx.compose.ui.text  	withStyle androidx.compose.ui.text  Builder (androidx.compose.ui.text.AnnotatedString  Color 0androidx.compose.ui.text.AnnotatedString.Builder  
FontWeight 0androidx.compose.ui.text.AnnotatedString.Builder  	SpanStyle 0androidx.compose.ui.text.AnnotatedString.Builder  append 0androidx.compose.ui.text.AnnotatedString.Builder  getUserColor 0androidx.compose.ui.text.AnnotatedString.Builder  	withStyle 0androidx.compose.ui.text.AnnotatedString.Builder  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  	Companion (androidx.compose.ui.text.input.ImeAction  Send (androidx.compose.ui.text.input.ImeAction  Send 2androidx.compose.ui.text.input.ImeAction.Companion  	Companion +androidx.compose.ui.text.input.KeyboardType  Email +androidx.compose.ui.text.input.KeyboardType  Password +androidx.compose.ui.text.input.KeyboardType  Email 5androidx.compose.ui.text.input.KeyboardType.Companion  Password 5androidx.compose.ui.text.input.KeyboardType.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Dialog androidx.compose.ui.window  AnimeNavigation #androidx.core.app.ComponentActivity  
AnimeTheme #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  WindowInsetsCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  
systemBars *androidx.core.view.WindowInsetsCompat.Type  %BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE /androidx.core.view.WindowInsetsControllerCompat  hide /androidx.core.view.WindowInsetsControllerCompat  show /androidx.core.view.WindowInsetsControllerCompat  systemBarsBehavior /androidx.core.view.WindowInsetsControllerCompat  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  	viewModel $androidx.lifecycle.viewmodel.compose  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  GroupScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  LaunchedEffect #androidx.navigation.NavGraphBuilder  LoginScreen #androidx.navigation.NavGraphBuilder  SignUpScreen #androidx.navigation.NavGraphBuilder  Unit #androidx.navigation.NavGraphBuilder  android #androidx.navigation.NavGraphBuilder  collectAsState #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  getValue #androidx.navigation.NavGraphBuilder  provideDelegate #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  popUpTo %androidx.navigation.NavOptionsBuilder  	inclusive "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  	ExoPlayer com.google.android.exoplayer2  	MediaItem com.google.android.exoplayer2  Player com.google.android.exoplayer2  Builder 'com.google.android.exoplayer2.ExoPlayer  STATE_BUFFERING 'com.google.android.exoplayer2.ExoPlayer  STATE_ENDED 'com.google.android.exoplayer2.ExoPlayer  STATE_READY 'com.google.android.exoplayer2.ExoPlayer  addListener 'com.google.android.exoplayer2.ExoPlayer  currentPosition 'com.google.android.exoplayer2.ExoPlayer  duration 'com.google.android.exoplayer2.ExoPlayer  	isPlaying 'com.google.android.exoplayer2.ExoPlayer  let 'com.google.android.exoplayer2.ExoPlayer  pause 'com.google.android.exoplayer2.ExoPlayer  play 'com.google.android.exoplayer2.ExoPlayer  prepare 'com.google.android.exoplayer2.ExoPlayer  release 'com.google.android.exoplayer2.ExoPlayer  seekTo 'com.google.android.exoplayer2.ExoPlayer  setMediaItem 'com.google.android.exoplayer2.ExoPlayer  build /com.google.android.exoplayer2.ExoPlayer.Builder  fromUri 'com.google.android.exoplayer2.MediaItem  Listener $com.google.android.exoplayer2.Player  STATE_BUFFERING $com.google.android.exoplayer2.Player  STATE_ENDED $com.google.android.exoplayer2.Player  STATE_READY $com.google.android.exoplayer2.Player  addListener $com.google.android.exoplayer2.Player  currentPosition $com.google.android.exoplayer2.Player  duration $com.google.android.exoplayer2.Player  	isPlaying $com.google.android.exoplayer2.Player  pause $com.google.android.exoplayer2.Player  play $com.google.android.exoplayer2.Player  prepare $com.google.android.exoplayer2.Player  release $com.google.android.exoplayer2.Player  seekTo $com.google.android.exoplayer2.Player  setMediaItem $com.google.android.exoplayer2.Player  
PlayerView  com.google.android.exoplayer2.ui  	ExoPlayer +com.google.android.exoplayer2.ui.PlayerView  	MediaItem +com.google.android.exoplayer2.ui.PlayerView  Uri +com.google.android.exoplayer2.ui.PlayerView  android +com.google.android.exoplayer2.ui.PlayerView  apply +com.google.android.exoplayer2.ui.PlayerView  
isNotEmpty +com.google.android.exoplayer2.ui.PlayerView  player +com.google.android.exoplayer2.ui.PlayerView  setOnClickListener +com.google.android.exoplayer2.ui.PlayerView  
useController +com.google.android.exoplayer2.ui.PlayerView  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  await !com.google.android.gms.tasks.Task  FirebaseAuth com.google.firebase.auth  FirebaseUser com.google.firebase.auth  user #com.google.firebase.auth.AuthResult  createUserWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  currentUser %com.google.firebase.auth.FirebaseAuth  getInstance %com.google.firebase.auth.FirebaseAuth  signInWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  signOut %com.google.firebase.auth.FirebaseAuth  displayName %com.google.firebase.auth.FirebaseUser  email %com.google.firebase.auth.FirebaseUser  uid %com.google.firebase.auth.FirebaseUser  DataSnapshot com.google.firebase.database  
DatabaseError com.google.firebase.database  DatabaseException com.google.firebase.database  DatabaseReference com.google.firebase.database  FirebaseDatabase com.google.firebase.database  Query com.google.firebase.database  ValueEventListener com.google.firebase.database  children )com.google.firebase.database.DataSnapshot  exists )com.google.firebase.database.DataSnapshot  getValue )com.google.firebase.database.DataSnapshot  value )com.google.firebase.database.DataSnapshot  message *com.google.firebase.database.DatabaseError  toException *com.google.firebase.database.DatabaseError  addValueEventListener .com.google.firebase.database.DatabaseReference  child .com.google.firebase.database.DatabaseReference  get .com.google.firebase.database.DatabaseReference  key .com.google.firebase.database.DatabaseReference  orderByChild .com.google.firebase.database.DatabaseReference  push .com.google.firebase.database.DatabaseReference  removeEventListener .com.google.firebase.database.DatabaseReference  removeValue .com.google.firebase.database.DatabaseReference  setValue .com.google.firebase.database.DatabaseReference  updateChildren .com.google.firebase.database.DatabaseReference  getInstance -com.google.firebase.database.FirebaseDatabase  	reference -com.google.firebase.database.FirebaseDatabase  addValueEventListener "com.google.firebase.database.Query  get "com.google.firebase.database.Query  limitToLast "com.google.firebase.database.Query  orderByChild "com.google.firebase.database.Query  removeEventListener "com.google.firebase.database.Query  FirebaseStorage com.google.firebase.storage  OnProgressListener com.google.firebase.storage  StorageReference com.google.firebase.storage  StorageTask com.google.firebase.storage  
UploadTask com.google.firebase.storage  getInstance +com.google.firebase.storage.FirebaseStorage  getReferenceFromUrl +com.google.firebase.storage.FirebaseStorage  	reference +com.google.firebase.storage.FirebaseStorage  <SAM-CONSTRUCTOR> .com.google.firebase.storage.OnProgressListener  child ,com.google.firebase.storage.StorageReference  delete ,com.google.firebase.storage.StorageReference  downloadUrl ,com.google.firebase.storage.StorageReference  path ,com.google.firebase.storage.StorageReference  putFile ,com.google.firebase.storage.StorageReference  TaskSnapshot &com.google.firebase.storage.UploadTask  addOnFailureListener &com.google.firebase.storage.UploadTask  addOnProgressListener &com.google.firebase.storage.UploadTask  addOnSuccessListener &com.google.firebase.storage.UploadTask  await &com.google.firebase.storage.UploadTask  bytesTransferred 3com.google.firebase.storage.UploadTask.TaskSnapshot  totalByteCount 3com.google.firebase.storage.UploadTask.TaskSnapshot  AnimeNavigation com.newt.anime  
AnimeTheme com.newt.anime  Bundle com.newt.anime  ComponentActivity com.newt.anime  MainActivity com.newt.anime  
MaterialTheme com.newt.anime  Modifier com.newt.anime  Surface com.newt.anime  fillMaxSize com.newt.anime  AnimeNavigation com.newt.anime.MainActivity  
AnimeTheme com.newt.anime.MainActivity  
MaterialTheme com.newt.anime.MainActivity  Modifier com.newt.anime.MainActivity  Surface com.newt.anime.MainActivity  enableEdgeToEdge com.newt.anime.MainActivity  fillMaxSize com.newt.anime.MainActivity  
setContent com.newt.anime.MainActivity  QUALITY_720P com.newt.anime.data.model  String com.newt.anime.data.model  VideoQuality com.newt.anime.data.model  find com.newt.anime.data.model  values com.newt.anime.data.model  QUALITY_720P &com.newt.anime.data.model.VideoQuality  String &com.newt.anime.data.model.VideoQuality  VideoQuality &com.newt.anime.data.model.VideoQuality  find &com.newt.anime.data.model.VideoQuality  value &com.newt.anime.data.model.VideoQuality  values &com.newt.anime.data.model.VideoQuality  QUALITY_720P 0com.newt.anime.data.model.VideoQuality.Companion  find 0com.newt.anime.data.model.VideoQuality.Companion  values 0com.newt.anime.data.model.VideoQuality.Companion  AvailableEmojis com.newt.anime.data.models  Boolean com.newt.anime.data.models  ChatMessage com.newt.anime.data.models  
EmojiReaction com.newt.anime.data.models  Group com.newt.anime.data.models  Int com.newt.anime.data.models  List com.newt.anime.data.models  Long com.newt.anime.data.models  Map com.newt.anime.data.models  Member com.newt.anime.data.models  MessageType com.newt.anime.data.models  String com.newt.anime.data.models  System com.newt.anime.data.models  User com.newt.anime.data.models  VideoSession com.newt.anime.data.models  
component1 com.newt.anime.data.models  
component2 com.newt.anime.data.models  count com.newt.anime.data.models  emptyMap com.newt.anime.data.models  filter com.newt.anime.data.models  listOf com.newt.anime.data.models  
mapNotNull com.newt.anime.data.models  ALL *com.newt.anime.data.models.AvailableEmojis  ANGRY *com.newt.anime.data.models.AvailableEmojis  HEART *com.newt.anime.data.models.AvailableEmojis  LAUGH *com.newt.anime.data.models.AvailableEmojis  SAD *com.newt.anime.data.models.AvailableEmojis  SHOCKED *com.newt.anime.data.models.AvailableEmojis  STOP *com.newt.anime.data.models.AvailableEmojis  listOf *com.newt.anime.data.models.AvailableEmojis  id &com.newt.anime.data.models.ChatMessage  message &com.newt.anime.data.models.ChatMessage  	timestamp &com.newt.anime.data.models.ChatMessage  userId &com.newt.anime.data.models.ChatMessage  userName &com.newt.anime.data.models.ChatMessage  emoji (com.newt.anime.data.models.EmojiReaction  id (com.newt.anime.data.models.EmojiReaction  	timestamp (com.newt.anime.data.models.EmojiReaction  userId (com.newt.anime.data.models.EmojiReaction  userName (com.newt.anime.data.models.EmojiReaction  System  com.newt.anime.data.models.Group  code  com.newt.anime.data.models.Group  
component1  com.newt.anime.data.models.Group  
component2  com.newt.anime.data.models.Group  copy  com.newt.anime.data.models.Group  count  com.newt.anime.data.models.Group  currentVideo  com.newt.anime.data.models.Group  filter  com.newt.anime.data.models.Group  getActiveViewers  com.newt.anime.data.models.Group  getActiveViewersCount  com.newt.anime.data.models.Group  id  com.newt.anime.data.models.Group  
mapNotNull  com.newt.anime.data.models.Group  members  com.newt.anime.data.models.Group  name  com.newt.anime.data.models.Group  ownerId  com.newt.anime.data.models.Group  	ownerName  com.newt.anime.data.models.Group  presence  com.newt.anime.data.models.Group  let !com.newt.anime.data.models.Member  name !com.newt.anime.data.models.Member  uid !com.newt.anime.data.models.Member  TEXT &com.newt.anime.data.models.MessageType  displayName com.newt.anime.data.models.User  currentPosition 'com.newt.anime.data.models.VideoSession  
hasStarted 'com.newt.anime.data.models.VideoSession  	isPlaying 'com.newt.anime.data.models.VideoSession  lastUpdated 'com.newt.anime.data.models.VideoSession  let 'com.newt.anime.data.models.VideoSession  ownerAction 'com.newt.anime.data.models.VideoSession  syncCommand 'com.newt.anime.data.models.VideoSession  
syncTimestamp 'com.newt.anime.data.models.VideoSession  title 'com.newt.anime.data.models.VideoSession  videoUrl 'com.newt.anime.data.models.VideoSession  Boolean  com.newt.anime.data.repositories  	Exception  com.newt.anime.data.repositories  FirebaseAuth  com.newt.anime.data.repositories  FirebaseStorage  com.newt.anime.data.repositories  Inject  com.newt.anime.data.repositories  Int  com.newt.anime.data.repositories  Long  com.newt.anime.data.repositories  	Singleton  com.newt.anime.data.repositories  StorageReference  com.newt.anime.data.repositories  String  com.newt.anime.data.repositories  UUID  com.newt.anime.data.repositories  Unit  com.newt.anime.data.repositories  UploadProgress  com.newt.anime.data.repositories  UploadResult  com.newt.anime.data.repositories  Uri  com.newt.anime.data.repositories  VideoUploadRepository  com.newt.anime.data.repositories  await  com.newt.anime.data.repositories  Boolean 6com.newt.anime.data.repositories.VideoUploadRepository  	Exception 6com.newt.anime.data.repositories.VideoUploadRepository  FirebaseAuth 6com.newt.anime.data.repositories.VideoUploadRepository  FirebaseStorage 6com.newt.anime.data.repositories.VideoUploadRepository  Inject 6com.newt.anime.data.repositories.VideoUploadRepository  Int 6com.newt.anime.data.repositories.VideoUploadRepository  Long 6com.newt.anime.data.repositories.VideoUploadRepository  StorageReference 6com.newt.anime.data.repositories.VideoUploadRepository  String 6com.newt.anime.data.repositories.VideoUploadRepository  UUID 6com.newt.anime.data.repositories.VideoUploadRepository  Unit 6com.newt.anime.data.repositories.VideoUploadRepository  UploadProgress 6com.newt.anime.data.repositories.VideoUploadRepository  UploadResult 6com.newt.anime.data.repositories.VideoUploadRepository  Uri 6com.newt.anime.data.repositories.VideoUploadRepository  auth 6com.newt.anime.data.repositories.VideoUploadRepository  await 6com.newt.anime.data.repositories.VideoUploadRepository  storage 6com.newt.anime.data.repositories.VideoUploadRepository  Any com.newt.anime.data.repository  AuthRepository com.newt.anime.data.repository  Boolean com.newt.anime.data.repository  DataSnapshot com.newt.anime.data.repository  
DatabaseError com.newt.anime.data.repository  	Exception com.newt.anime.data.repository  FirebaseAuth com.newt.anime.data.repository  FirebaseDatabase com.newt.anime.data.repository  FirebaseStorage com.newt.anime.data.repository  FirebaseUser com.newt.anime.data.repository  Flow com.newt.anime.data.repository  Group com.newt.anime.data.repository  GroupRepository com.newt.anime.data.repository  List com.newt.anime.data.repository  Map com.newt.anime.data.repository  Member com.newt.anime.data.repository  Number com.newt.anime.data.repository  Random com.newt.anime.data.repository  Result com.newt.anime.data.repository  String com.newt.anime.data.repository  System com.newt.anime.data.repository  Unit com.newt.anime.data.repository  User com.newt.anime.data.repository  ValueEventListener com.newt.anime.data.repository  VideoSession com.newt.anime.data.repository  android com.newt.anime.data.repository  await com.newt.anime.data.repository  callbackFlow com.newt.anime.data.repository  close com.newt.anime.data.repository  com com.newt.anime.data.repository  contains com.newt.anime.data.repository  database com.newt.anime.data.repository  	emptyList com.newt.anime.data.repository  equals com.newt.anime.data.repository  failure com.newt.anime.data.repository  	groupsRef com.newt.anime.data.repository  isBlank com.newt.anime.data.repository  
isNotEmpty com.newt.anime.data.repository  java com.newt.anime.data.repository  joinToString com.newt.anime.data.repository  let com.newt.anime.data.repository  map com.newt.anime.data.repository  mapOf com.newt.anime.data.repository  
mutableListOf com.newt.anime.data.repository  nextInt com.newt.anime.data.repository  set com.newt.anime.data.repository  sortedBy com.newt.anime.data.repository  success com.newt.anime.data.repository  to com.newt.anime.data.repository  toMutableMap com.newt.anime.data.repository  trySend com.newt.anime.data.repository  	uppercase com.newt.anime.data.repository  	Exception -com.newt.anime.data.repository.AuthRepository  FirebaseAuth -com.newt.anime.data.repository.AuthRepository  FirebaseDatabase -com.newt.anime.data.repository.AuthRepository  Result -com.newt.anime.data.repository.AuthRepository  User -com.newt.anime.data.repository.AuthRepository  auth -com.newt.anime.data.repository.AuthRepository  await -com.newt.anime.data.repository.AuthRepository  contains -com.newt.anime.data.repository.AuthRepository  currentUser -com.newt.anime.data.repository.AuthRepository  database -com.newt.anime.data.repository.AuthRepository  failure -com.newt.anime.data.repository.AuthRepository  getCurrentUserData -com.newt.anime.data.repository.AuthRepository  java -com.newt.anime.data.repository.AuthRepository  signIn -com.newt.anime.data.repository.AuthRepository  signOut -com.newt.anime.data.repository.AuthRepository  signUp -com.newt.anime.data.repository.AuthRepository  success -com.newt.anime.data.repository.AuthRepository  	Exception .com.newt.anime.data.repository.GroupRepository  FirebaseDatabase .com.newt.anime.data.repository.GroupRepository  FirebaseStorage .com.newt.anime.data.repository.GroupRepository  Group .com.newt.anime.data.repository.GroupRepository  Member .com.newt.anime.data.repository.GroupRepository  Random .com.newt.anime.data.repository.GroupRepository  Result .com.newt.anime.data.repository.GroupRepository  System .com.newt.anime.data.repository.GroupRepository  Unit .com.newt.anime.data.repository.GroupRepository  android .com.newt.anime.data.repository.GroupRepository  await .com.newt.anime.data.repository.GroupRepository  
awaitClose .com.newt.anime.data.repository.GroupRepository  	banMember .com.newt.anime.data.repository.GroupRepository  callbackFlow .com.newt.anime.data.repository.GroupRepository  close .com.newt.anime.data.repository.GroupRepository  com .com.newt.anime.data.repository.GroupRepository  contains .com.newt.anime.data.repository.GroupRepository  createGroup .com.newt.anime.data.repository.GroupRepository  database .com.newt.anime.data.repository.GroupRepository  deleteGroup .com.newt.anime.data.repository.GroupRepository  	emptyList .com.newt.anime.data.repository.GroupRepository  equals .com.newt.anime.data.repository.GroupRepository  failure .com.newt.anime.data.repository.GroupRepository  generateGroupCode .com.newt.anime.data.repository.GroupRepository  
getUserGroups .com.newt.anime.data.repository.GroupRepository  	groupsRef .com.newt.anime.data.repository.GroupRepository  isBlank .com.newt.anime.data.repository.GroupRepository  
isNotEmpty .com.newt.anime.data.repository.GroupRepository  java .com.newt.anime.data.repository.GroupRepository  	joinGroup .com.newt.anime.data.repository.GroupRepository  joinToString .com.newt.anime.data.repository.GroupRepository  let .com.newt.anime.data.repository.GroupRepository  map .com.newt.anime.data.repository.GroupRepository  mapOf .com.newt.anime.data.repository.GroupRepository  
mutableListOf .com.newt.anime.data.repository.GroupRepository  nextInt .com.newt.anime.data.repository.GroupRepository  observeChatMessages .com.newt.anime.data.repository.GroupRepository  observeEmojiReactions .com.newt.anime.data.repository.GroupRepository  observeGroup .com.newt.anime.data.repository.GroupRepository  removeVideoFromGroup .com.newt.anime.data.repository.GroupRepository  sendChatMessage .com.newt.anime.data.repository.GroupRepository  sendEmojiReaction .com.newt.anime.data.repository.GroupRepository  sendRemovalNotification .com.newt.anime.data.repository.GroupRepository  set .com.newt.anime.data.repository.GroupRepository  sortedBy .com.newt.anime.data.repository.GroupRepository  storage .com.newt.anime.data.repository.GroupRepository  success .com.newt.anime.data.repository.GroupRepository  to .com.newt.anime.data.repository.GroupRepository  toMutableMap .com.newt.anime.data.repository.GroupRepository  trySend .com.newt.anime.data.repository.GroupRepository  updateUserPresence .com.newt.anime.data.repository.GroupRepository  updateVideoSession .com.newt.anime.data.repository.GroupRepository  	uppercase .com.newt.anime.data.repository.GroupRepository  newt "com.newt.anime.data.repository.com  anime 'com.newt.anime.data.repository.com.newt  data -com.newt.anime.data.repository.com.newt.anime  models 2com.newt.anime.data.repository.com.newt.anime.data  ChatMessage 9com.newt.anime.data.repository.com.newt.anime.data.models  
EmojiReaction 9com.newt.anime.data.repository.com.newt.anime.data.models  AnimeNavigation com.newt.anime.navigation  
AuthViewModel com.newt.anime.navigation  
Composable com.newt.anime.navigation  GroupScreen com.newt.anime.navigation  GroupViewModel com.newt.anime.navigation  
HomeScreen com.newt.anime.navigation  LaunchedEffect com.newt.anime.navigation  LoginScreen com.newt.anime.navigation  NavHostController com.newt.anime.navigation  SignUpScreen com.newt.anime.navigation  Unit com.newt.anime.navigation  android com.newt.anime.navigation  collectAsState com.newt.anime.navigation  getValue com.newt.anime.navigation  provideDelegate com.newt.anime.navigation  Activity com.newt.anime.ui.components  ActivityResultContracts com.newt.anime.ui.components  	Alignment com.newt.anime.ui.components  AndroidView com.newt.anime.ui.components  AnimatedChatMessage com.newt.anime.ui.components  AnimatedEmojiReaction com.newt.anime.ui.components  AnimatedVisibility com.newt.anime.ui.components  Arrangement com.newt.anime.ui.components  AvailableEmojis com.newt.anime.ui.components  Boolean com.newt.anime.ui.components  Box com.newt.anime.ui.components  Button com.newt.anime.ui.components  ButtonDefaults com.newt.anime.ui.components  Card com.newt.anime.ui.components  CardDefaults com.newt.anime.ui.components  ChatMessage com.newt.anime.ui.components  ChatMessageItem com.newt.anime.ui.components  ChatMessagesList com.newt.anime.ui.components  ChatOverlay com.newt.anime.ui.components  
ChatWindow com.newt.anime.ui.components  CircleShape com.newt.anime.ui.components  CircularProgressIndicator com.newt.anime.ui.components  Close com.newt.anime.ui.components  Color com.newt.anime.ui.components  Column com.newt.anime.ui.components  
Composable com.newt.anime.ui.components  CreateGroupDialog com.newt.anime.ui.components  Dispatchers com.newt.anime.ui.components  DisposableEffect com.newt.anime.ui.components  EmojiButton com.newt.anime.ui.components  
EmojiPanel com.newt.anime.ui.components  
EmojiReaction com.newt.anime.ui.components  	Exception com.newt.anime.ui.components  	ExoPlayer com.newt.anime.ui.components  ExoVideoPlayer com.newt.anime.ui.components  ExperimentalMaterial3Api com.newt.anime.ui.components  FirebaseAuth com.newt.anime.ui.components  FirebaseStorage com.newt.anime.ui.components  FloatingActionButton com.newt.anime.ui.components  
FontWeight com.newt.anime.ui.components  	GridCells com.newt.anime.ui.components  GroupCodeDialog com.newt.anime.ui.components  GroupRepository com.newt.anime.ui.components  Icon com.newt.anime.ui.components  
IconButton com.newt.anime.ui.components  Icons com.newt.anime.ui.components  ImageVector com.newt.anime.ui.components  	ImeAction com.newt.anime.ui.components  Int com.newt.anime.ui.components  JoinGroupDialog com.newt.anime.ui.components  KeyboardActions com.newt.anime.ui.components  KeyboardArrowLeft com.newt.anime.ui.components  KeyboardArrowRight com.newt.anime.ui.components  KeyboardOptions com.newt.anime.ui.components  LaunchedEffect com.newt.anime.ui.components  
LazyColumn com.newt.anime.ui.components  LazyVerticalGrid com.newt.anime.ui.components  LinearProgressIndicator com.newt.anime.ui.components  List com.newt.anime.ui.components  Long com.newt.anime.ui.components  
MaterialTheme com.newt.anime.ui.components  	MediaItem com.newt.anime.ui.components  MessageOverlay com.newt.anime.ui.components  Modifier com.newt.anime.ui.components  NewVideoPlayer com.newt.anime.ui.components  OptIn com.newt.anime.ui.components  OutlinedButton com.newt.anime.ui.components  OutlinedTextField com.newt.anime.ui.components  OutlinedTextFieldDefaults com.newt.anime.ui.components  	PlayArrow com.newt.anime.ui.components  
PlayerView com.newt.anime.ui.components  RoundedCornerShape com.newt.anime.ui.components  Row com.newt.anime.ui.components  SimpleChatMessageItem com.newt.anime.ui.components  SimpleControlButton com.newt.anime.ui.components  Slider com.newt.anime.ui.components  SliderDefaults com.newt.anime.ui.components  Spacer com.newt.anime.ui.components  	SpanStyle com.newt.anime.ui.components  String com.newt.anime.ui.components  System com.newt.anime.ui.components  Text com.newt.anime.ui.components  	TextAlign com.newt.anime.ui.components  
TextButton com.newt.anime.ui.components  UUID com.newt.anime.ui.components  Unit com.newt.anime.ui.components  Uri com.newt.anime.ui.components  VideoPlayer com.newt.anime.ui.components  VideoSession com.newt.anime.ui.components  VideoUploadDialog com.newt.anime.ui.components  WebView com.newt.anime.ui.components  
WebViewClient com.newt.anime.ui.components  WindowCompat com.newt.anime.ui.components  WindowInsetsCompat com.newt.anime.ui.components  WindowInsetsControllerCompat com.newt.anime.ui.components  
YouTubeButton com.newt.anime.ui.components  align com.newt.anime.ui.components  android com.newt.anime.ui.components  androidx com.newt.anime.ui.components  apply com.newt.anime.ui.components  
background com.newt.anime.ui.components  buildAnnotatedString com.newt.anime.ui.components  buttonColors com.newt.anime.ui.components  
cardColors com.newt.anime.ui.components  	clickable com.newt.anime.ui.components  colors com.newt.anime.ui.components  com com.newt.anime.ui.components  contains com.newt.anime.ui.components  convertToEmbedUrl com.newt.anime.ui.components  delay com.newt.anime.ui.components  	emptyList com.newt.anime.ui.components  endsWith com.newt.anime.ui.components  fadeIn com.newt.anime.ui.components  fadeOut com.newt.anime.ui.components  
fillMaxHeight com.newt.anime.ui.components  fillMaxSize com.newt.anime.ui.components  fillMaxWidth com.newt.anime.ui.components  filter com.newt.anime.ui.components  forEachIndexed com.newt.anime.ui.components  format com.newt.anime.ui.components  
formatTime com.newt.anime.ui.components  getUserColor com.newt.anime.ui.components  getValue com.newt.anime.ui.components  height com.newt.anime.ui.components  ifEmpty com.newt.anime.ui.components  isEmpty com.newt.anime.ui.components  
isNotBlank com.newt.anime.ui.components  
isNotEmpty com.newt.anime.ui.components  key com.newt.anime.ui.components  kotlin com.newt.anime.ui.components  kotlinx com.newt.anime.ui.components  let com.newt.anime.ui.components  listOf com.newt.anime.ui.components  maxOf com.newt.anime.ui.components  minOf com.newt.anime.ui.components  mutableStateOf com.newt.anime.ui.components  padding com.newt.anime.ui.components  provideDelegate com.newt.anime.ui.components  remember com.newt.anime.ui.components  reversed com.newt.anime.ui.components  scaleIn com.newt.anime.ui.components  scaleOut com.newt.anime.ui.components  setValue com.newt.anime.ui.components  size com.newt.anime.ui.components  slideInHorizontally com.newt.anime.ui.components  slideInVertically com.newt.anime.ui.components  slideOutHorizontally com.newt.anime.ui.components  slideOutVertically com.newt.anime.ui.components  spacedBy com.newt.anime.ui.components  substringAfter com.newt.anime.ui.components  substringBefore com.newt.anime.ui.components  takeIf com.newt.anime.ui.components  takeLast com.newt.anime.ui.components  
trimIndent com.newt.anime.ui.components  uploadToFirebase com.newt.anime.ui.components  	uppercase com.newt.anime.ui.components  verticalGradient com.newt.anime.ui.components  weight com.newt.anime.ui.components  width com.newt.anime.ui.components  widthIn com.newt.anime.ui.components  wrapContentSize com.newt.anime.ui.components  webkit $com.newt.anime.ui.components.android  widget $com.newt.anime.ui.components.android  WebResourceRequest +com.newt.anime.ui.components.android.webkit  WebView +com.newt.anime.ui.components.android.webkit  
WebViewClient +com.newt.anime.ui.components.android.webkit  	VideoView +com.newt.anime.ui.components.android.widget  google  com.newt.anime.ui.components.com  newt  com.newt.anime.ui.components.com  android 'com.newt.anime.ui.components.com.google  
exoplayer2 /com.newt.anime.ui.components.com.google.android  Player :com.newt.anime.ui.components.com.google.android.exoplayer2  Listener Acom.newt.anime.ui.components.com.google.android.exoplayer2.Player  anime %com.newt.anime.ui.components.com.newt  data +com.newt.anime.ui.components.com.newt.anime  models 0com.newt.anime.ui.components.com.newt.anime.data  ChatMessage 7com.newt.anime.ui.components.com.newt.anime.data.models  
EmojiReaction 7com.newt.anime.ui.components.com.newt.anime.data.models  AlertDialog com.newt.anime.ui.screens  	Alignment com.newt.anime.ui.screens  AnnotatedString com.newt.anime.ui.screens  Arrangement com.newt.anime.ui.screens  
AuthViewModel com.newt.anime.ui.screens  Box com.newt.anime.ui.screens  Button com.newt.anime.ui.screens  Card com.newt.anime.ui.screens  CardDefaults com.newt.anime.ui.screens  CircleShape com.newt.anime.ui.screens  CircularProgressIndicator com.newt.anime.ui.screens  Color com.newt.anime.ui.screens  Column com.newt.anime.ui.screens  
Composable com.newt.anime.ui.screens  DisposableEffect com.newt.anime.ui.screens  DropdownMenu com.newt.anime.ui.screens  DropdownMenuItem com.newt.anime.ui.screens  	Exception com.newt.anime.ui.screens  ExoVideoPlayer com.newt.anime.ui.screens  ExperimentalMaterial3Api com.newt.anime.ui.screens  
FontWeight com.newt.anime.ui.screens  Group com.newt.anime.ui.screens  	GroupCard com.newt.anime.ui.screens  GroupScreen com.newt.anime.ui.screens  GroupViewModel com.newt.anime.ui.screens  
HomeScreen com.newt.anime.ui.screens  Icon com.newt.anime.ui.screens  
IconButton com.newt.anime.ui.screens  Icons com.newt.anime.ui.screens  KeyboardOptions com.newt.anime.ui.screens  KeyboardType com.newt.anime.ui.screens  LaunchedEffect com.newt.anime.ui.screens  
LazyColumn com.newt.anime.ui.screens  LoginScreen com.newt.anime.ui.screens  
MaterialTheme com.newt.anime.ui.screens  Member com.newt.anime.ui.screens  Modifier com.newt.anime.ui.screens  NavHostController com.newt.anime.ui.screens  OptIn com.newt.anime.ui.screens  OutlinedButton com.newt.anime.ui.screens  OutlinedTextField com.newt.anime.ui.screens  PasswordVisualTransformation com.newt.anime.ui.screens  Row com.newt.anime.ui.screens  SignUpScreen com.newt.anime.ui.screens  Spacer com.newt.anime.ui.screens  System com.newt.anime.ui.screens  Text com.newt.anime.ui.screens  
TextButton com.newt.anime.ui.screens  	TopAppBar com.newt.anime.ui.screens  Unit com.newt.anime.ui.screens  align com.newt.anime.ui.screens  android com.newt.anime.ui.screens  
background com.newt.anime.ui.screens  
cardColors com.newt.anime.ui.screens  	clickable com.newt.anime.ui.screens  collectAsState com.newt.anime.ui.screens  fillMaxSize com.newt.anime.ui.screens  fillMaxWidth com.newt.anime.ui.screens  getValue com.newt.anime.ui.screens  height com.newt.anime.ui.screens  heightIn com.newt.anime.ui.screens  isEmpty com.newt.anime.ui.screens  
isNotEmpty com.newt.anime.ui.screens  kotlinx com.newt.anime.ui.screens  let com.newt.anime.ui.screens  mutableStateOf com.newt.anime.ui.screens  padding com.newt.anime.ui.screens  provideDelegate com.newt.anime.ui.screens  remember com.newt.anime.ui.screens  setValue com.newt.anime.ui.screens  size com.newt.anime.ui.screens  spacedBy com.newt.anime.ui.screens  toList com.newt.anime.ui.screens  weight com.newt.anime.ui.screens  width com.newt.anime.ui.screens  
AnimeTheme com.newt.anime.ui.theme  Boolean com.newt.anime.ui.theme  Build com.newt.anime.ui.theme  
Composable com.newt.anime.ui.theme  DarkColorScheme com.newt.anime.ui.theme  
FontFamily com.newt.anime.ui.theme  
FontWeight com.newt.anime.ui.theme  LightColorScheme com.newt.anime.ui.theme  Pink40 com.newt.anime.ui.theme  Pink80 com.newt.anime.ui.theme  Purple40 com.newt.anime.ui.theme  Purple80 com.newt.anime.ui.theme  PurpleGrey40 com.newt.anime.ui.theme  PurpleGrey80 com.newt.anime.ui.theme  
Typography com.newt.anime.ui.theme  Unit com.newt.anime.ui.theme  AuthRepository com.newt.anime.ui.viewmodel  AuthUiState com.newt.anime.ui.viewmodel  
AuthViewModel com.newt.anime.ui.viewmodel  Boolean com.newt.anime.ui.viewmodel  	Exception com.newt.anime.ui.viewmodel  Group com.newt.anime.ui.viewmodel  GroupRepository com.newt.anime.ui.viewmodel  GroupUiState com.newt.anime.ui.viewmodel  GroupViewModel com.newt.anime.ui.viewmodel  List com.newt.anime.ui.viewmodel  Long com.newt.anime.ui.viewmodel  MutableStateFlow com.newt.anime.ui.viewmodel  	StateFlow com.newt.anime.ui.viewmodel  String com.newt.anime.ui.viewmodel  System com.newt.anime.ui.viewmodel  Unit com.newt.anime.ui.viewmodel  VideoSession com.newt.anime.ui.viewmodel  	ViewModel com.newt.anime.ui.viewmodel  
_currentGroup com.newt.anime.ui.viewmodel  _isAuthenticated com.newt.anime.ui.viewmodel  _uiState com.newt.anime.ui.viewmodel  _userGroups com.newt.anime.ui.viewmodel  android com.newt.anime.ui.viewmodel  asStateFlow com.newt.anime.ui.viewmodel  authRepository com.newt.anime.ui.viewmodel  com com.newt.anime.ui.viewmodel  	emptyList com.newt.anime.ui.viewmodel  filter com.newt.anime.ui.viewmodel  groupRepository com.newt.anime.ui.viewmodel  isBlank com.newt.anime.ui.viewmodel  launch com.newt.anime.ui.viewmodel  loadUserGroups com.newt.anime.ui.viewmodel  observeChatMessages com.newt.anime.ui.viewmodel  observeEmojiReactions com.newt.anime.ui.viewmodel  	onFailure com.newt.anime.ui.viewmodel  	onSuccess com.newt.anime.ui.viewmodel  removeGroupFromList com.newt.anime.ui.viewmodel  run com.newt.anime.ui.viewmodel  substringBefore com.newt.anime.ui.viewmodel  	uppercase com.newt.anime.ui.viewmodel  copy 'com.newt.anime.ui.viewmodel.AuthUiState  error 'com.newt.anime.ui.viewmodel.AuthUiState  	isLoading 'com.newt.anime.ui.viewmodel.AuthUiState  AuthRepository )com.newt.anime.ui.viewmodel.AuthViewModel  AuthUiState )com.newt.anime.ui.viewmodel.AuthViewModel  MutableStateFlow )com.newt.anime.ui.viewmodel.AuthViewModel  _isAuthenticated )com.newt.anime.ui.viewmodel.AuthViewModel  _uiState )com.newt.anime.ui.viewmodel.AuthViewModel  asStateFlow )com.newt.anime.ui.viewmodel.AuthViewModel  authRepository )com.newt.anime.ui.viewmodel.AuthViewModel  checkAuthState )com.newt.anime.ui.viewmodel.AuthViewModel  
clearError )com.newt.anime.ui.viewmodel.AuthViewModel  isAuthenticated )com.newt.anime.ui.viewmodel.AuthViewModel  isBlank )com.newt.anime.ui.viewmodel.AuthViewModel  launch )com.newt.anime.ui.viewmodel.AuthViewModel  	onFailure )com.newt.anime.ui.viewmodel.AuthViewModel  	onSuccess )com.newt.anime.ui.viewmodel.AuthViewModel  signIn )com.newt.anime.ui.viewmodel.AuthViewModel  signOut )com.newt.anime.ui.viewmodel.AuthViewModel  signUp )com.newt.anime.ui.viewmodel.AuthViewModel  uiState )com.newt.anime.ui.viewmodel.AuthViewModel  viewModelScope )com.newt.anime.ui.viewmodel.AuthViewModel  copy (com.newt.anime.ui.viewmodel.GroupUiState  createdGroupCode (com.newt.anime.ui.viewmodel.GroupUiState  error (com.newt.anime.ui.viewmodel.GroupUiState  	isLoading (com.newt.anime.ui.viewmodel.GroupUiState  AuthRepository *com.newt.anime.ui.viewmodel.GroupViewModel  GroupRepository *com.newt.anime.ui.viewmodel.GroupViewModel  GroupUiState *com.newt.anime.ui.viewmodel.GroupViewModel  MutableStateFlow *com.newt.anime.ui.viewmodel.GroupViewModel  System *com.newt.anime.ui.viewmodel.GroupViewModel  VideoSession *com.newt.anime.ui.viewmodel.GroupViewModel  
_chatMessages *com.newt.anime.ui.viewmodel.GroupViewModel  
_currentGroup *com.newt.anime.ui.viewmodel.GroupViewModel  _emojiReactions *com.newt.anime.ui.viewmodel.GroupViewModel  _uiState *com.newt.anime.ui.viewmodel.GroupViewModel  _userGroups *com.newt.anime.ui.viewmodel.GroupViewModel  android *com.newt.anime.ui.viewmodel.GroupViewModel  asStateFlow *com.newt.anime.ui.viewmodel.GroupViewModel  authRepository *com.newt.anime.ui.viewmodel.GroupViewModel  	banMember *com.newt.anime.ui.viewmodel.GroupViewModel  chatMessages *com.newt.anime.ui.viewmodel.GroupViewModel  clearCreatedGroupCode *com.newt.anime.ui.viewmodel.GroupViewModel  createGroup *com.newt.anime.ui.viewmodel.GroupViewModel  currentGroup *com.newt.anime.ui.viewmodel.GroupViewModel  deleteGroup *com.newt.anime.ui.viewmodel.GroupViewModel  emojiReactions *com.newt.anime.ui.viewmodel.GroupViewModel  	emptyList *com.newt.anime.ui.viewmodel.GroupViewModel  filter *com.newt.anime.ui.viewmodel.GroupViewModel  groupRepository *com.newt.anime.ui.viewmodel.GroupViewModel  isBlank *com.newt.anime.ui.viewmodel.GroupViewModel  	joinGroup *com.newt.anime.ui.viewmodel.GroupViewModel  launch *com.newt.anime.ui.viewmodel.GroupViewModel  loadUserGroups *com.newt.anime.ui.viewmodel.GroupViewModel  observeChatMessages *com.newt.anime.ui.viewmodel.GroupViewModel  observeEmojiReactions *com.newt.anime.ui.viewmodel.GroupViewModel  observeGroup *com.newt.anime.ui.viewmodel.GroupViewModel  	onFailure *com.newt.anime.ui.viewmodel.GroupViewModel  	onSuccess *com.newt.anime.ui.viewmodel.GroupViewModel  
refreshGroups *com.newt.anime.ui.viewmodel.GroupViewModel  removeGroupFromList *com.newt.anime.ui.viewmodel.GroupViewModel  run *com.newt.anime.ui.viewmodel.GroupViewModel  selectGroup *com.newt.anime.ui.viewmodel.GroupViewModel  sendChatMessage *com.newt.anime.ui.viewmodel.GroupViewModel  sendEmojiReaction *com.newt.anime.ui.viewmodel.GroupViewModel  substringBefore *com.newt.anime.ui.viewmodel.GroupViewModel  syncVideoAction *com.newt.anime.ui.viewmodel.GroupViewModel  uiState *com.newt.anime.ui.viewmodel.GroupViewModel  updateUserPresence *com.newt.anime.ui.viewmodel.GroupViewModel  updateVideoSession *com.newt.anime.ui.viewmodel.GroupViewModel  	uppercase *com.newt.anime.ui.viewmodel.GroupViewModel  
userGroups *com.newt.anime.ui.viewmodel.GroupViewModel  viewModelScope *com.newt.anime.ui.viewmodel.GroupViewModel  newt com.newt.anime.ui.viewmodel.com  anime $com.newt.anime.ui.viewmodel.com.newt  data *com.newt.anime.ui.viewmodel.com.newt.anime  models /com.newt.anime.ui.viewmodel.com.newt.anime.data  ChatMessage 6com.newt.anime.ui.viewmodel.com.newt.anime.data.models  
EmojiReaction 6com.newt.anime.ui.viewmodel.com.newt.anime.data.models  Class 	java.lang  	Exception 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  currentTimeMillis java.lang.System  gc java.lang.System  
Comparator 	java.util  UUID 	java.util  
randomUUID java.util.UUID  toString java.util.UUID  Inject javax.inject  	Singleton javax.inject  Array kotlin  CharSequence kotlin  Enum kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  Nothing kotlin  Number kotlin  OptIn kotlin  Pair kotlin  Result kotlin  String kotlin  	Throwable kotlin  apply kotlin  let kotlin  map kotlin  	onFailure kotlin  	onSuccess kotlin  run kotlin  takeIf kotlin  to kotlin  toList kotlin  equals 
kotlin.Any  hashCode 
kotlin.Any  find kotlin.Array  not kotlin.Boolean  isEmpty kotlin.CharSequence  sp 
kotlin.Double  	Companion kotlin.Enum  QUALITY_720P kotlin.Enum  String kotlin.Enum  VideoQuality kotlin.Enum  find kotlin.Enum  values kotlin.Enum  QUALITY_720P kotlin.Enum.Companion  find kotlin.Enum.Companion  values kotlin.Enum.Companion  div kotlin.Float  sp kotlin.Float  times kotlin.Float  toLong kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  	compareTo 
kotlin.Int  div 
kotlin.Int  dp 
kotlin.Int  let 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toLong 
kotlin.Int  
unaryMinus 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  plus kotlin.Long  rem kotlin.Long  takeIf kotlin.Long  times kotlin.Long  toFloat kotlin.Long  toInt kotlin.Long  toLong 
kotlin.Number  	Companion 
kotlin.Result  exceptionOrNull 
kotlin.Result  failure 
kotlin.Result  	isFailure 
kotlin.Result  	isSuccess 
kotlin.Result  	onFailure 
kotlin.Result  	onSuccess 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  	Companion 
kotlin.String  contains 
kotlin.String  endsWith 
kotlin.String  equals 
kotlin.String  format 
kotlin.String  get 
kotlin.String  hashCode 
kotlin.String  ifEmpty 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  plus 
kotlin.String  substringAfter 
kotlin.String  substringBefore 
kotlin.String  to 
kotlin.String  
trimIndent 
kotlin.String  	uppercase 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  
Collection kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  count kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  find kotlin.collections  forEachIndexed kotlin.collections  ifEmpty kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  maxOf kotlin.collections  minOf kotlin.collections  
mutableListOf kotlin.collections  reversed kotlin.collections  set kotlin.collections  sortedBy kotlin.collections  takeLast kotlin.collections  toList kotlin.collections  toMutableMap kotlin.collections  count kotlin.collections.Collection  toList kotlin.collections.Collection  filter kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  joinToString kotlin.collections.List  reversed kotlin.collections.List  size kotlin.collections.List  takeLast kotlin.collections.List  Entry kotlin.collections.Map  containsKey kotlin.collections.Map  filter kotlin.collections.Map  get kotlin.collections.Map  
mapNotNull kotlin.collections.Map  size kotlin.collections.Map  toMutableMap kotlin.collections.Map  values kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  iterator "kotlin.collections.MutableIterable  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  size kotlin.collections.MutableList  sortedBy kotlin.collections.MutableList  set kotlin.collections.MutableMap  maxOf kotlin.comparisons  minOf kotlin.comparisons  reversed kotlin.comparisons  SuspendFunction1 kotlin.coroutines  endsWith 	kotlin.io  java 
kotlin.jvm  kotlin 
kotlin.jvm  abs kotlin.math  Random 
kotlin.random  Default kotlin.random.Random  nextInt kotlin.random.Random  nextInt kotlin.random.Random.Default  CharProgression 
kotlin.ranges  IntProgression 
kotlin.ranges  IntRange 
kotlin.ranges  LongProgression 
kotlin.ranges  	LongRange 
kotlin.ranges  UIntProgression 
kotlin.ranges  ULongProgression 
kotlin.ranges  contains 
kotlin.ranges  reversed 
kotlin.ranges  map kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  forEachIndexed kotlin.sequences  ifEmpty kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  
mapNotNull kotlin.sequences  maxOf kotlin.sequences  minOf kotlin.sequences  sortedBy kotlin.sequences  toList kotlin.sequences  contains kotlin.text  count kotlin.text  endsWith kotlin.text  equals kotlin.text  filter kotlin.text  find kotlin.text  forEachIndexed kotlin.text  format kotlin.text  ifEmpty kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  
mapNotNull kotlin.text  maxOf kotlin.text  minOf kotlin.text  reversed kotlin.text  set kotlin.text  substringAfter kotlin.text  substringBefore kotlin.text  takeLast kotlin.text  toList kotlin.text  
trimIndent kotlin.text  	uppercase kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  WindowCompat !kotlinx.coroutines.CoroutineScope  WindowInsetsCompat !kotlinx.coroutines.CoroutineScope  WindowInsetsControllerCompat !kotlinx.coroutines.CoroutineScope  
_currentGroup !kotlinx.coroutines.CoroutineScope  _isAuthenticated !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  _userGroups !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  androidx !kotlinx.coroutines.CoroutineScope  authRepository !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  groupRepository !kotlinx.coroutines.CoroutineScope  kotlin !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  loadUserGroups !kotlinx.coroutines.CoroutineScope  observeChatMessages !kotlinx.coroutines.CoroutineScope  observeEmojiReactions !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  	onSuccess !kotlinx.coroutines.CoroutineScope  removeGroupFromList !kotlinx.coroutines.CoroutineScope  substringBefore !kotlinx.coroutines.CoroutineScope  takeIf !kotlinx.coroutines.CoroutineScope  	uppercase !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  
ChannelResult kotlinx.coroutines.channels  
ProducerScope kotlinx.coroutines.channels  
awaitClose kotlinx.coroutines.channels  Group )kotlinx.coroutines.channels.ProducerScope  android )kotlinx.coroutines.channels.ProducerScope  
awaitClose )kotlinx.coroutines.channels.ProducerScope  close )kotlinx.coroutines.channels.ProducerScope  com )kotlinx.coroutines.channels.ProducerScope  database )kotlinx.coroutines.channels.ProducerScope  	groupsRef )kotlinx.coroutines.channels.ProducerScope  java )kotlinx.coroutines.channels.ProducerScope  
mutableListOf )kotlinx.coroutines.channels.ProducerScope  sortedBy )kotlinx.coroutines.channels.ProducerScope  trySend )kotlinx.coroutines.channels.ProducerScope  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  callbackFlow kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  value !kotlinx.coroutines.flow.StateFlow  await kotlinx.coroutines.tasks  query android.content.ContentResolver  contentResolver android.content.Context  Cursor android.database  getColumnIndex android.database.Cursor  getLong android.database.Cursor  	getString android.database.Cursor  moveToFirst android.database.Cursor  use android.database.Cursor  
MediaStore android.provider  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  SIZE (android.provider.MediaStore.MediaColumns  DISPLAY_NAME 'android.provider.MediaStore.Video.Media  SIZE 'android.provider.MediaStore.Video.Media  border androidx.compose.foundation  Context "androidx.compose.foundation.layout  Float "androidx.compose.foundation.layout  
MediaStore "androidx.compose.foundation.layout  Pair "androidx.compose.foundation.layout  border "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  substringBeforeLast "androidx.compose.foundation.layout  uploadVideoToFirebase "androidx.compose.foundation.layout  use "androidx.compose.foundation.layout  CheckCircle .androidx.compose.foundation.layout.ColumnScope  border .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  ifEmpty .androidx.compose.foundation.layout.ColumnScope  substringBeforeLast .androidx.compose.foundation.layout.ColumnScope  uploadVideoToFirebase .androidx.compose.foundation.layout.ColumnScope  ifEmpty +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  substringBeforeLast +androidx.compose.foundation.layout.RowScope  uploadVideoToFirebase +androidx.compose.foundation.layout.RowScope  CheckCircle ,androidx.compose.material.icons.Icons.Filled  CheckCircle &androidx.compose.material.icons.filled  Context androidx.compose.material3  Float androidx.compose.material3  
MediaStore androidx.compose.material3  Pair androidx.compose.material3  border androidx.compose.material3  clip androidx.compose.material3  substringBeforeLast androidx.compose.material3  uploadVideoToFirebase androidx.compose.material3  use androidx.compose.material3  outline &androidx.compose.material3.ColorScheme  Context androidx.compose.runtime  Float androidx.compose.runtime  
MediaStore androidx.compose.runtime  Pair androidx.compose.runtime  border androidx.compose.runtime  clip androidx.compose.runtime  substringBeforeLast androidx.compose.runtime  uploadVideoToFirebase androidx.compose.runtime  use androidx.compose.runtime  border androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  Context com.newt.anime.ui.components  Float com.newt.anime.ui.components  
MediaStore com.newt.anime.ui.components  Pair com.newt.anime.ui.components  border com.newt.anime.ui.components  clip com.newt.anime.ui.components  formatFileSize com.newt.anime.ui.components  getVideoFileInfo com.newt.anime.ui.components  substringBeforeLast com.newt.anime.ui.components  uploadVideoToFirebase com.newt.anime.ui.components  use com.newt.anime.ui.components  use kotlin  toInt kotlin.Float  first kotlin.Pair  second kotlin.Pair  substringBeforeLast 
kotlin.String  use 	kotlin.io  substringBeforeLast kotlin.text  BorderStroke androidx.compose.foundation  BorderStroke "androidx.compose.foundation.layout  outlinedButtonColors "androidx.compose.foundation.layout  	TextAlign +androidx.compose.foundation.layout.BoxScope  BorderStroke .androidx.compose.foundation.layout.ColumnScope  outlinedButtonColors .androidx.compose.foundation.layout.ColumnScope  BorderStroke .androidx.compose.foundation.lazy.LazyItemScope  ButtonDefaults .androidx.compose.foundation.lazy.LazyItemScope  OutlinedButton .androidx.compose.foundation.lazy.LazyItemScope  	TextAlign .androidx.compose.foundation.lazy.LazyItemScope  fillMaxSize .androidx.compose.foundation.lazy.LazyItemScope  outlinedButtonColors .androidx.compose.foundation.lazy.LazyItemScope  BorderStroke .androidx.compose.foundation.lazy.LazyListScope  ButtonDefaults .androidx.compose.foundation.lazy.LazyListScope  OutlinedButton .androidx.compose.foundation.lazy.LazyListScope  	TextAlign .androidx.compose.foundation.lazy.LazyListScope  fillMaxSize .androidx.compose.foundation.lazy.LazyListScope  outlinedButtonColors .androidx.compose.foundation.lazy.LazyListScope  BorderStroke androidx.compose.material3  outlinedButtonColors androidx.compose.material3  outlinedButtonColors )androidx.compose.material3.ButtonDefaults  BorderStroke androidx.compose.runtime  outlinedButtonColors androidx.compose.runtime  deleteVideo .com.newt.anime.data.repository.GroupRepository  BorderStroke com.newt.anime.ui.screens  ButtonDefaults com.newt.anime.ui.screens  	TextAlign com.newt.anime.ui.screens  buttonColors com.newt.anime.ui.screens  outlinedButtonColors com.newt.anime.ui.screens  deleteVideo *com.newt.anime.ui.viewmodel.GroupViewModel  SubscriptionScreen /androidx.compose.animation.AnimatedContentScope  CurrentSubscriptionCard "androidx.compose.foundation.layout  GroupCreationOption "androidx.compose.foundation.layout  InfoCard "androidx.compose.foundation.layout  MemberLimitOptionCard "androidx.compose.foundation.layout  SubscriptionCard "androidx.compose.foundation.layout  SubscriptionTier "androidx.compose.foundation.layout  SubscriptionViewModel "androidx.compose.foundation.layout  TopAppBarDefaults "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  topAppBarColors "androidx.compose.foundation.layout  Check .androidx.compose.foundation.layout.ColumnScope  CurrentSubscriptionCard .androidx.compose.foundation.layout.ColumnScope  InfoCard .androidx.compose.foundation.layout.ColumnScope  Int .androidx.compose.foundation.layout.ColumnScope  Lock .androidx.compose.foundation.layout.ColumnScope  MemberLimitOptionCard .androidx.compose.foundation.layout.ColumnScope  SubscriptionCard .androidx.compose.foundation.layout.ColumnScope  SubscriptionTier .androidx.compose.foundation.layout.ColumnScope  TopAppBarDefaults .androidx.compose.foundation.layout.ColumnScope  heightIn .androidx.compose.foundation.layout.ColumnScope  take .androidx.compose.foundation.layout.ColumnScope  topAppBarColors .androidx.compose.foundation.layout.ColumnScope  Check +androidx.compose.foundation.layout.RowScope  Lock +androidx.compose.foundation.layout.RowScope  SubscriptionTier +androidx.compose.foundation.layout.RowScope  UserSubscription =androidx.compose.foundation.layout.com.newt.anime.data.models  CurrentSubscriptionCard .androidx.compose.foundation.lazy.LazyItemScope  InfoCard .androidx.compose.foundation.lazy.LazyItemScope  MemberLimitOptionCard .androidx.compose.foundation.lazy.LazyItemScope  SubscriptionCard .androidx.compose.foundation.lazy.LazyItemScope  CurrentSubscriptionCard .androidx.compose.foundation.lazy.LazyListScope  InfoCard .androidx.compose.foundation.lazy.LazyListScope  MemberLimitOptionCard .androidx.compose.foundation.lazy.LazyListScope  SubscriptionCard .androidx.compose.foundation.lazy.LazyListScope  SubscriptionTier .androidx.compose.foundation.lazy.LazyListScope  Check ,androidx.compose.material.icons.Icons.Filled  Lock ,androidx.compose.material.icons.Icons.Filled  Check &androidx.compose.material.icons.filled  Lock &androidx.compose.material.icons.filled  CurrentSubscriptionCard androidx.compose.material3  GroupCreationOption androidx.compose.material3  InfoCard androidx.compose.material3  MemberLimitOptionCard androidx.compose.material3  SubscriptionCard androidx.compose.material3  SubscriptionTier androidx.compose.material3  SubscriptionViewModel androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  forEach androidx.compose.material3  take androidx.compose.material3  topAppBarColors androidx.compose.material3  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  UserSubscription 5androidx.compose.material3.com.newt.anime.data.models  CurrentSubscriptionCard androidx.compose.runtime  GroupCreationOption androidx.compose.runtime  InfoCard androidx.compose.runtime  MemberLimitOptionCard androidx.compose.runtime  SubscriptionCard androidx.compose.runtime  SubscriptionScreen androidx.compose.runtime  SubscriptionTier androidx.compose.runtime  SubscriptionViewModel androidx.compose.runtime  TopAppBarDefaults androidx.compose.runtime  forEach androidx.compose.runtime  take androidx.compose.runtime  topAppBarColors androidx.compose.runtime  UserSubscription 3androidx.compose.runtime.com.newt.anime.data.models  then androidx.compose.ui.Modifier  border &androidx.compose.ui.Modifier.Companion  Gray "androidx.compose.ui.graphics.Color  Green "androidx.compose.ui.graphics.Color  Gray ,androidx.compose.ui.graphics.Color.Companion  Green ,androidx.compose.ui.graphics.Color.Companion  SubscriptionScreen #androidx.navigation.NavGraphBuilder  AuthRepository com.newt.anime.data.models  DataSnapshot com.newt.anime.data.models  
DatabaseError com.newt.anime.data.models  Double com.newt.anime.data.models  	Exception com.newt.anime.data.models  FirebaseDatabase com.newt.anime.data.models  Flow com.newt.anime.data.models  GroupCreationOption com.newt.anime.data.models  MutableStateFlow com.newt.anime.data.models  PaymentInfo com.newt.anime.data.models  
PaymentStatus com.newt.anime.data.models  Result com.newt.anime.data.models  	StateFlow com.newt.anime.data.models  SubscriptionRepository com.newt.anime.data.models  SubscriptionTier com.newt.anime.data.models  SubscriptionUiState com.newt.anime.data.models  	UNLIMITED com.newt.anime.data.models  Unit com.newt.anime.data.models  
UsageStats com.newt.anime.data.models  UserSubscription com.newt.anime.data.models  ValueEventListener com.newt.anime.data.models  	ViewModel com.newt.anime.data.models  _groupCreationOptions com.newt.anime.data.models  _uiState com.newt.anime.data.models  _userSubscription com.newt.anime.data.models  android com.newt.anime.data.models  asStateFlow com.newt.anime.data.models  authRepository com.newt.anime.data.models  await com.newt.anime.data.models  callbackFlow com.newt.anime.data.models  close com.newt.anime.data.models  	emptyList com.newt.anime.data.models  failure com.newt.anime.data.models  java com.newt.anime.data.models  kotlinx com.newt.anime.data.models  launch com.newt.anime.data.models  loadGroupCreationOptions com.newt.anime.data.models  maxOf com.newt.anime.data.models  subscriptionRepository com.newt.anime.data.models  subscriptionsRef com.newt.anime.data.models  success com.newt.anime.data.models  trySend com.newt.anime.data.models  upgradeSubscription com.newt.anime.data.models  Int  com.newt.anime.data.models.Group  canAddNewMember  com.newt.anime.data.models.Group  getMembershipStatus  com.newt.anime.data.models.Group  isFull  com.newt.anime.data.models.Group  
maxMembers  com.newt.anime.data.models.Group  maxOf  com.newt.anime.data.models.Group  Int .com.newt.anime.data.models.GroupCreationOption  SubscriptionTier .com.newt.anime.data.models.GroupCreationOption  getDisplayText .com.newt.anime.data.models.GroupCreationOption  getSubtitle .com.newt.anime.data.models.GroupCreationOption  isAvailable .com.newt.anime.data.models.GroupCreationOption  
maxMembers .com.newt.anime.data.models.GroupCreationOption  requiresUpgrade .com.newt.anime.data.models.GroupCreationOption  tier .com.newt.anime.data.models.GroupCreationOption  id &com.newt.anime.data.models.PaymentInfo  	COMPLETED (com.newt.anime.data.models.PaymentStatus  PENDING (com.newt.anime.data.models.PaymentStatus  FREE +com.newt.anime.data.models.SubscriptionTier  Int +com.newt.anime.data.models.SubscriptionTier  PREMIUM +com.newt.anime.data.models.SubscriptionTier  	UNLIMITED +com.newt.anime.data.models.SubscriptionTier  features +com.newt.anime.data.models.SubscriptionTier  getDisplayName +com.newt.anime.data.models.SubscriptionTier  getPrice +com.newt.anime.data.models.SubscriptionTier  let +com.newt.anime.data.models.SubscriptionTier  listOf +com.newt.anime.data.models.SubscriptionTier  
maxMembers +com.newt.anime.data.models.SubscriptionTier  nameAr +com.newt.anime.data.models.SubscriptionTier  priceUSD +com.newt.anime.data.models.SubscriptionTier  values +com.newt.anime.data.models.SubscriptionTier  SubscriptionTier +com.newt.anime.data.models.UserSubscription  System +com.newt.anime.data.models.UserSubscription  canCreateGroupWithMembers +com.newt.anime.data.models.UserSubscription  copy +com.newt.anime.data.models.UserSubscription  endDate +com.newt.anime.data.models.UserSubscription  getDaysRemaining +com.newt.anime.data.models.UserSubscription  
getMaxMembers +com.newt.anime.data.models.UserSubscription  isActive +com.newt.anime.data.models.UserSubscription  isValid +com.newt.anime.data.models.UserSubscription  let +com.newt.anime.data.models.UserSubscription  maxOf +com.newt.anime.data.models.UserSubscription  tier +com.newt.anime.data.models.UserSubscription  userId +com.newt.anime.data.models.UserSubscription  GroupCreationOption com.newt.anime.data.repository  Int com.newt.anime.data.repository  PaymentInfo com.newt.anime.data.repository  SubscriptionRepository com.newt.anime.data.repository  SubscriptionTier com.newt.anime.data.repository  
UsageStats com.newt.anime.data.repository  UserSubscription com.newt.anime.data.repository  listOf com.newt.anime.data.repository  subscriptionsRef com.newt.anime.data.repository  createGroupWithLimit .com.newt.anime.data.repository.GroupRepository  FirebaseDatabase 5com.newt.anime.data.repository.SubscriptionRepository  GroupCreationOption 5com.newt.anime.data.repository.SubscriptionRepository  Result 5com.newt.anime.data.repository.SubscriptionRepository  SubscriptionTier 5com.newt.anime.data.repository.SubscriptionRepository  System 5com.newt.anime.data.repository.SubscriptionRepository  Unit 5com.newt.anime.data.repository.SubscriptionRepository  
UsageStats 5com.newt.anime.data.repository.SubscriptionRepository  UserSubscription 5com.newt.anime.data.repository.SubscriptionRepository  android 5com.newt.anime.data.repository.SubscriptionRepository  await 5com.newt.anime.data.repository.SubscriptionRepository  
awaitClose 5com.newt.anime.data.repository.SubscriptionRepository  callbackFlow 5com.newt.anime.data.repository.SubscriptionRepository  canAddMemberToGroup 5com.newt.anime.data.repository.SubscriptionRepository  cancelSubscription 5com.newt.anime.data.repository.SubscriptionRepository  close 5com.newt.anime.data.repository.SubscriptionRepository  database 5com.newt.anime.data.repository.SubscriptionRepository  failure 5com.newt.anime.data.repository.SubscriptionRepository  getGroupCreationOptions 5com.newt.anime.data.repository.SubscriptionRepository  getUserSubscription 5com.newt.anime.data.repository.SubscriptionRepository  java 5com.newt.anime.data.repository.SubscriptionRepository  listOf 5com.newt.anime.data.repository.SubscriptionRepository  observeUserSubscription 5com.newt.anime.data.repository.SubscriptionRepository  paymentsRef 5com.newt.anime.data.repository.SubscriptionRepository  savePaymentInfo 5com.newt.anime.data.repository.SubscriptionRepository  subscriptionsRef 5com.newt.anime.data.repository.SubscriptionRepository  success 5com.newt.anime.data.repository.SubscriptionRepository  trySend 5com.newt.anime.data.repository.SubscriptionRepository  upgradeSubscription 5com.newt.anime.data.repository.SubscriptionRepository  
usageStatsRef 5com.newt.anime.data.repository.SubscriptionRepository  SubscriptionScreen com.newt.anime.navigation  SubscriptionViewModel com.newt.anime.navigation  AlertDialog com.newt.anime.ui.components  GroupCreationDialog com.newt.anime.ui.components  GroupCreationOption com.newt.anime.ui.components  MemberLimitOptionCard com.newt.anime.ui.components  SubscriptionTier com.newt.anime.ui.components  SubscriptionViewModel com.newt.anime.ui.components  UpgradeRequiredDialog com.newt.anime.ui.components  collectAsState com.newt.anime.ui.components  forEach com.newt.anime.ui.components  heightIn com.newt.anime.ui.components  take com.newt.anime.ui.components  Boolean com.newt.anime.ui.screens  CurrentSubscriptionCard com.newt.anime.ui.screens  InfoCard com.newt.anime.ui.screens  Int com.newt.anime.ui.screens  SubscriptionCard com.newt.anime.ui.screens  SubscriptionConfirmDialog com.newt.anime.ui.screens  SubscriptionScreen com.newt.anime.ui.screens  SubscriptionTier com.newt.anime.ui.screens  SubscriptionViewModel com.newt.anime.ui.screens  TopAppBarDefaults com.newt.anime.ui.screens  border com.newt.anime.ui.screens  com com.newt.anime.ui.screens  forEach com.newt.anime.ui.screens  take com.newt.anime.ui.screens  topAppBarColors com.newt.anime.ui.screens  newt com.newt.anime.ui.screens.com  anime "com.newt.anime.ui.screens.com.newt  data (com.newt.anime.ui.screens.com.newt.anime  models -com.newt.anime.ui.screens.com.newt.anime.data  UserSubscription 4com.newt.anime.ui.screens.com.newt.anime.data.models  GroupCreationOption com.newt.anime.ui.viewmodel  Int com.newt.anime.ui.viewmodel  PaymentInfo com.newt.anime.ui.viewmodel  
PaymentStatus com.newt.anime.ui.viewmodel  SubscriptionRepository com.newt.anime.ui.viewmodel  SubscriptionTier com.newt.anime.ui.viewmodel  SubscriptionUiState com.newt.anime.ui.viewmodel  SubscriptionViewModel com.newt.anime.ui.viewmodel  UserSubscription com.newt.anime.ui.viewmodel  _groupCreationOptions com.newt.anime.ui.viewmodel  _userSubscription com.newt.anime.ui.viewmodel  kotlinx com.newt.anime.ui.viewmodel  loadGroupCreationOptions com.newt.anime.ui.viewmodel  subscriptionRepository com.newt.anime.ui.viewmodel  upgradeSubscription com.newt.anime.ui.viewmodel  copy /com.newt.anime.ui.viewmodel.SubscriptionUiState  error /com.newt.anime.ui.viewmodel.SubscriptionUiState  	isLoading /com.newt.anime.ui.viewmodel.SubscriptionUiState  message /com.newt.anime.ui.viewmodel.SubscriptionUiState  AuthRepository 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  MutableStateFlow 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  PaymentInfo 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  
PaymentStatus 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  SubscriptionRepository 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  SubscriptionTier 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  SubscriptionUiState 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  System 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  _groupCreationOptions 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  _uiState 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  _userSubscription 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  android 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  asStateFlow 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  authRepository 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  
clearMessages 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  	emptyList 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  getDaysRemaining 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  groupCreationOptions 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  kotlinx 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  launch 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  loadGroupCreationOptions 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  loadUserSubscription 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  refresh 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  simulatePayment 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  subscriptionRepository 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  uiState 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  upgradeSubscription 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  userSubscription 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  viewModelScope 1com.newt.anime.ui.viewmodel.SubscriptionViewModel  Date 	java.util  Int kotlin  toList kotlin.Array  Boolean kotlin.Enum  Double kotlin.Enum  Int kotlin.Enum  List kotlin.Enum  	UNLIMITED kotlin.Enum  listOf kotlin.Enum  Int kotlin.Enum.Companion  	UNLIMITED kotlin.Enum.Companion  listOf kotlin.Enum.Companion  	Companion 
kotlin.Int  	MAX_VALUE 
kotlin.Int  	MAX_VALUE kotlin.Int.Companion  	getOrNull 
kotlin.Result  forEach kotlin.collections  take kotlin.collections  take kotlin.collections.List  forEach kotlin.sequences  take kotlin.sequences  forEach kotlin.text  take kotlin.text  PaymentInfo !kotlinx.coroutines.CoroutineScope  
PaymentStatus !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  _groupCreationOptions !kotlinx.coroutines.CoroutineScope  _userSubscription !kotlinx.coroutines.CoroutineScope  loadGroupCreationOptions !kotlinx.coroutines.CoroutineScope  subscriptionRepository !kotlinx.coroutines.CoroutineScope  upgradeSubscription !kotlinx.coroutines.CoroutineScope  UserSubscription )kotlinx.coroutines.channels.ProducerScope  subscriptionsRef )kotlinx.coroutines.channels.ProducerScope  AuthRepository kotlinx.coroutines.flow  Boolean kotlinx.coroutines.flow  	Exception kotlinx.coroutines.flow  GroupCreationOption kotlinx.coroutines.flow  Int kotlinx.coroutines.flow  List kotlinx.coroutines.flow  PaymentInfo kotlinx.coroutines.flow  
PaymentStatus kotlinx.coroutines.flow  String kotlinx.coroutines.flow  SubscriptionRepository kotlinx.coroutines.flow  SubscriptionTier kotlinx.coroutines.flow  SubscriptionUiState kotlinx.coroutines.flow  System kotlinx.coroutines.flow  UserSubscription kotlinx.coroutines.flow  	ViewModel kotlinx.coroutines.flow  _groupCreationOptions kotlinx.coroutines.flow  _uiState kotlinx.coroutines.flow  _userSubscription kotlinx.coroutines.flow  android kotlinx.coroutines.flow  authRepository kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  kotlinx kotlinx.coroutines.flow  launch kotlinx.coroutines.flow  loadGroupCreationOptions kotlinx.coroutines.flow  subscriptionRepository kotlinx.coroutines.flow  upgradeSubscription kotlinx.coroutines.flow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                