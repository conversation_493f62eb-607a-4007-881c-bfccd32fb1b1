{"logs": [{"outputFile": "com.newt.anime.app-mergeReleaseResources-62:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\85c261ec03eee0a4101b5d8231a63563\\transformed\\material3-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,425,548,647,745,860,1017,1147,1299,1385,1491,1587,1689,1805,1938,2049,2188,2323,2456,2634,2758,2876,2997,3124,3221,3318,3440,3578,3684,3793,3899,4038,4183,4293,4402,4478,4578,4678,4794,4881,4970,5081,5161,5245,5345,5453,5553,5654,5741,5854,5956,6061,6182,6262,6372", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,115,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "174,298,420,543,642,740,855,1012,1142,1294,1380,1486,1582,1684,1800,1933,2044,2183,2318,2451,2629,2753,2871,2992,3119,3216,3313,3435,3573,3679,3788,3894,4033,4178,4288,4397,4473,4573,4673,4789,4876,4965,5076,5156,5240,5340,5448,5548,5649,5736,5849,5951,6056,6177,6257,6367,6464"}, "to": {"startLines": "108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8980,9104,9228,9350,9473,9572,9670,9785,9942,10072,10224,10310,10416,10512,10614,10730,10863,10974,11113,11248,11381,11559,11683,11801,11922,12049,12146,12243,12365,12503,12609,12718,12824,12963,13108,13218,13327,13403,13503,13603,13719,13806,13895,14006,14086,14170,14270,14378,14478,14579,14666,14779,14881,14986,15107,15187,15297", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,115,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "9099,9223,9345,9468,9567,9665,9780,9937,10067,10219,10305,10411,10507,10609,10725,10858,10969,11108,11243,11376,11554,11678,11796,11917,12044,12141,12238,12360,12498,12604,12713,12819,12958,13103,13213,13322,13398,13498,13598,13714,13801,13890,14001,14081,14165,14265,14373,14473,14574,14661,14774,14876,14981,15102,15182,15292,15389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dc398bc18186089f1a6c4c36afb914b8\\transformed\\foundation-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "16412,16500", "endColumns": "87,94", "endOffsets": "16495,16590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6436519ada9dddee292e4f473475ac25\\transformed\\exoplayer-ui-2.19.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,316,543,747,842,939,1021,1118,1216,1293,1357,1460,1561,1626,1689,1749,1820,1942,2067,2187,2255,2342,2418,2494,2587,2683,2750,2814,2867,2925,2975,3036,3096,3158,3222,3284,3343,3408,3474,3538,3605,3659,3718,3791,3864", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,94,96,81,96,97,76,63,102,100,64,62,59,70,121,124,119,67,86,75,75,92,95,66,63,52,57,49,60,59,61,63,61,58,64,65,63,66,53,58,72,72,53", "endOffsets": "311,538,742,837,934,1016,1113,1211,1288,1352,1455,1556,1621,1684,1744,1815,1937,2062,2182,2250,2337,2413,2489,2582,2678,2745,2809,2862,2920,2970,3031,3091,3153,3217,3279,3338,3403,3469,3533,3600,3654,3713,3786,3859,3913"}, "to": {"startLines": "2,11,15,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,366,593,4586,4681,4778,4860,4957,5055,5132,5196,5299,5400,5465,5528,5588,5659,5781,5906,6026,6094,6181,6257,6333,6426,6522,6589,7382,7435,7493,7543,7604,7664,7726,7790,7852,7911,7976,8042,8106,8173,8227,8286,8359,8432", "endLines": "10,14,18,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "endColumns": "17,12,12,94,96,81,96,97,76,63,102,100,64,62,59,70,121,124,119,67,86,75,75,92,95,66,63,52,57,49,60,59,61,63,61,58,64,65,63,66,53,58,72,72,53", "endOffsets": "361,588,792,4676,4773,4855,4952,5050,5127,5191,5294,5395,5460,5523,5583,5654,5776,5901,6021,6089,6176,6252,6328,6421,6517,6584,6648,7430,7488,7538,7599,7659,7721,7785,7847,7906,7971,8037,8101,8168,8222,8281,8354,8427,8481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f341d0dad5e087546904e2a03fd632f3\\transformed\\play-services-base-18.1.0\\res\\values-kn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,466,595,706,845,970,1074,1169,1315,1424,1585,1716,1857,2010,2075,2134", "endColumns": "106,165,128,110,138,124,103,94,145,108,160,130,140,152,64,58,80", "endOffsets": "299,465,594,705,844,969,1073,1168,1314,1423,1584,1715,1856,2009,2074,2133,2214"}, "to": {"startLines": "30,31,32,33,34,35,36,37,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1951,2062,2232,2365,2480,2623,2752,2860,3105,3255,3368,3533,3668,3813,3970,4039,4102", "endColumns": "110,169,132,114,142,128,107,98,149,112,164,134,144,156,68,62,84", "endOffsets": "2057,2227,2360,2475,2618,2747,2855,2954,3250,3363,3528,3663,3808,3965,4034,4097,4182"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5f82ddb128f9d8fc1f894d4feaff6b09\\transformed\\exoplayer-core-2.19.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,193,270,341,422,504,605,698", "endColumns": "73,63,76,70,80,81,100,92,85", "endOffsets": "124,188,265,336,417,499,600,693,779"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6653,6727,6791,6868,6939,7020,7102,7203,7296", "endColumns": "73,63,76,70,80,81,100,92,85", "endOffsets": "6722,6786,6863,6934,7015,7097,7198,7291,7377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf7927868eeddad3b4319378a3201862\\transformed\\browser-1.4.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,269,385", "endColumns": "113,99,115,100", "endOffsets": "164,264,380,481"}, "to": {"startLines": "48,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "4187,8486,8586,8702", "endColumns": "113,99,115,100", "endOffsets": "4296,8581,8697,8798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8f761d99cc674c08e5b049b2a7bbf06\\transformed\\core-1.16.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "21,22,23,24,25,26,27,173", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1025,1123,1226,1327,1433,1534,1642,16047", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "1118,1221,1322,1428,1529,1637,1765,16143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9c9112f3b5828659e41d3d6d877dc27\\transformed\\credentials-1.2.0-rc01\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,113", "endOffsets": "164,278"}, "to": {"startLines": "19,20", "startColumns": "4,4", "startOffsets": "797,911", "endColumns": "113,113", "endOffsets": "906,1020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95a85adbd9a0258061c7c940036f2eec\\transformed\\ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,1006,1092,1171,1246,1324,1401,1478,1547", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,74,77,76,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,1001,1087,1166,1241,1319,1396,1473,1542,1660"}, "to": {"startLines": "28,29,49,50,51,106,107,165,166,167,168,169,170,171,172,174,175,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1770,1867,4301,4397,4497,8803,8887,15394,15485,15570,15652,15738,15817,15892,15970,16148,16225,16294", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,74,77,76,76,68,117", "endOffsets": "1862,1946,4392,4492,4581,8882,8975,15480,15565,15647,15733,15812,15887,15965,16042,16220,16289,16407"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4366d762d4e78881da860b75d1c4c59f\\transformed\\play-services-basement-18.4.0\\res\\values-kn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "38", "startColumns": "4", "startOffsets": "2959", "endColumns": "145", "endOffsets": "3100"}}]}]}