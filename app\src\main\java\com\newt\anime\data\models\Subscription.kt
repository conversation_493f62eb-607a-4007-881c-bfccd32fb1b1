package com.newt.anime.data.models

import java.util.Date

// مستويات الاشتراك
enum class SubscriptionTier(
    val id: String,
    val nameAr: String,
    val nameEn: String,
    val maxMembers: Int,
    val priceUSD: Double,
    val uploadLimitGB: Double, // حد الرفع بالجيجابايت
    val dailyUploadMB: Double, // الحد اليومي للمجاني
    val features: List<String>
) {
    FREE(
        id = "free",
        nameAr = "مجاني",
        nameEn = "Free",
        maxMembers = 4,
        priceUSD = 0.0,
        uploadLimitGB = 0.0, // لا يوجد حد إجمالي
        dailyUploadMB = 300.0, // 300MB يومياً
        features = listOf(
            "حتى 4 أعضاء",
            "300MB رفع يومياً",
            "مشاهدة الفيديوهات",
            "الدردشة الأساسية"
        )
    ),
    PREMIUM(
        id = "premium",
        nameAr = "بريميوم",
        nameEn = "Premium",
        maxMembers = 10,
        priceUSD = 4.99,
        uploadLimitGB = 15.0, // 15GB إجمالي
        dailyUploadMB = 0.0, // لا يوجد حد يومي
        features = listOf(
            "حتى 10 أعضاء",
            "15GB رصيد رفع",
            "مجموعات غير محدودة",
            "دردشة متقدمة",
            "إحصائيات المشاهدة"
        )
    ),
    UNLIMITED(
        id = "unlimited",
        nameAr = "غير محدود",
        nameEn = "Unlimited",
        maxMembers = Int.MAX_VALUE,
        priceUSD = 9.99,
        uploadLimitGB = 30.0, // 30GB إجمالي
        dailyUploadMB = 0.0, // لا يوجد حد يومي
        features = listOf(
            "أعضاء غير محدودين",
            "30GB رصيد رفع",
            "مجموعات غير محدودة",
            "جودة فيديو عالية",
            "دعم أولوية 24/7",
            "ميزات حصرية"
        )
    );

    fun getDisplayName(): String = nameAr
    fun getPrice(): String = if (priceUSD == 0.0) "مجاني" else "$${priceUSD}"
    fun isUnlimited(): Boolean = this == UNLIMITED
}

// بيانات اشتراك المستخدم
data class UserSubscription(
    val userId: String = "",
    val tier: SubscriptionTier = SubscriptionTier.FREE,
    val isActive: Boolean = true, // المجاني دائماً نشط
    val remainingUploadGB: Double = 0.0, // الرصيد المتبقي للرفع (للمدفوع)
    val dailyUploadUsedMB: Double = 0.0, // المستخدم اليوم (للمجاني)
    val lastDailyReset: Long = System.currentTimeMillis(), // آخر إعادة تعيين يومي
    val paymentId: String = "", // معرف الدفع
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    // التحقق من صلاحية الاشتراك
    fun isValid(): Boolean {
        return when (tier) {
            SubscriptionTier.FREE -> {
                // التحقق من إعادة تعيين الحد اليومي
                val today = System.currentTimeMillis()
                val daysSinceReset = (today - lastDailyReset) / (24 * 60 * 60 * 1000)
                daysSinceReset < 1 // صالح لنفس اليوم
            }
            else -> isActive && remainingUploadGB > 0
        }
    }

    // الحصول على الرصيد المتبقي كنص
    fun getRemainingQuotaText(): String {
        return when (tier) {
            SubscriptionTier.FREE -> {
                val remaining = tier.dailyUploadMB - dailyUploadUsedMB
                "${remaining.toInt()}MB متبقي اليوم"
            }
            else -> "${String.format("%.1f", remainingUploadGB)}GB متبقي"
        }
    }

    // التحقق من إمكانية رفع فيديو بحجم معين (بالMB)
    fun canUploadVideo(sizeInMB: Double): Boolean {
        return when (tier) {
            SubscriptionTier.FREE -> {
                // التحقق من الحد اليومي
                val remaining = tier.dailyUploadMB - dailyUploadUsedMB
                sizeInMB <= remaining
            }
            else -> {
                // التحقق من الرصيد المتبقي
                val sizeInGB = sizeInMB / 1024.0
                sizeInGB <= remainingUploadGB
            }
        }
    }

    // التحقق من إمكانية إنشاء مجموعة بعدد معين من الأعضاء
    fun canCreateGroupWithMembers(memberCount: Int): Boolean {
        return memberCount <= tier.maxMembers
    }

    // الحصول على الحد الأقصى للأعضاء
    fun getMaxMembers(): Int {
        return tier.maxMembers
    }

    // التحقق من انتهاء الاشتراك
    fun isExpired(): Boolean {
        return when (tier) {
            SubscriptionTier.FREE -> false // المجاني لا ينتهي
            else -> !isActive || remainingUploadGB <= 0
        }
    }

    // الحصول على نسبة الاستهلاك (0-100)
    fun getUsagePercentage(): Int {
        return when (tier) {
            SubscriptionTier.FREE -> {
                val used = (dailyUploadUsedMB / tier.dailyUploadMB * 100).toInt()
                maxOf(0, minOf(100, used))
            }
            else -> {
                val used = ((tier.uploadLimitGB - remainingUploadGB) / tier.uploadLimitGB * 100).toInt()
                maxOf(0, minOf(100, used))
            }
        }
    }
}

// خيارات إنشاء المجموعة
data class GroupCreationOption(
    val maxMembers: Int,
    val tier: SubscriptionTier,
    val isAvailable: Boolean,
    val requiresUpgrade: Boolean = false
) {
    fun getDisplayText(): String {
        return when {
            maxMembers == Int.MAX_VALUE -> "غير محدود"
            else -> "$maxMembers أعضاء"
        }
    }

    fun getSubtitle(): String {
        return when {
            !isAvailable && requiresUpgrade -> "يتطلب ${tier.getDisplayName()} - ${tier.getPrice()}"
            tier == SubscriptionTier.FREE -> "مجاني"
            else -> "${tier.getDisplayName()} - ${tier.getPrice()}"
        }
    }
}

// معلومات الدفع
data class PaymentInfo(
    val id: String = "",
    val userId: String = "",
    val tier: SubscriptionTier = SubscriptionTier.FREE,
    val amount: Double = 0.0,
    val currency: String = "USD",
    val status: PaymentStatus = PaymentStatus.PENDING,
    val paymentMethod: String = "", // "google_play", "paypal", etc.
    val transactionId: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val completedAt: Long = 0L
)

enum class PaymentStatus {
    PENDING,
    COMPLETED,
    FAILED,
    CANCELLED,
    REFUNDED
}

// إحصائيات الاستخدام
data class UsageStats(
    val userId: String = "",
    val groupsCreated: Int = 0,
    val totalMembers: Int = 0,
    val videosUploaded: Int = 0,
    val totalWatchTime: Long = 0L, // بالدقائق
    val messagesCount: Int = 0,
    val lastActivity: Long = System.currentTimeMillis()
)
