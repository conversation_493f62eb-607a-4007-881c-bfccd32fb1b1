package com.newt.anime.data.models

import java.util.Date

// مستويات الاشتراك
enum class SubscriptionTier(
    val id: String,
    val nameAr: String,
    val nameEn: String,
    val maxMembers: Int,
    val priceUSD: Double,
    val features: List<String>
) {
    FREE(
        id = "free",
        nameAr = "مجاني",
        nameEn = "Free",
        maxMembers = 4,
        priceUSD = 0.0,
        features = listOf(
            "مجموعة واحدة",
            "حتى 4 أعضاء",
            "مشاهدة الفيديوهات",
            "الدردشة الأساسية"
        )
    ),
    PREMIUM(
        id = "premium",
        nameAr = "بريميوم",
        nameEn = "Premium",
        maxMembers = 10,
        priceUSD = 4.99,
        features = listOf(
            "مجموعات غير محدودة",
            "حتى 10 أعضاء لكل مجموعة",
            "رفع فيديوهات أكبر (500MB)",
            "دردشة متقدمة مع الإيموجي",
            "إحصائيات المشاهدة",
            "أولوية في الدعم"
        )
    ),
    UNLIMITED(
        id = "unlimited",
        nameAr = "غير محدود",
        nameEn = "Unlimited",
        maxMembers = Int.MAX_VALUE,
        priceUSD = 9.99,
        features = listOf(
            "مجموعات غير محدودة",
            "أعضاء غير محدودين",
            "رفع فيديوهات كبيرة (2GB)",
            "جودة فيديو عالية",
            "دردشة متقدمة مع ملفات",
            "إحصائيات تفصيلية",
            "دعم أولوية 24/7",
            "ميزات حصرية"
        )
    );

    fun getDisplayName(): String = nameAr
    fun getPrice(): String = if (priceUSD == 0.0) "مجاني" else "$${priceUSD}"
    fun isUnlimited(): Boolean = this == UNLIMITED
}

// بيانات اشتراك المستخدم
data class UserSubscription(
    val userId: String = "",
    val tier: SubscriptionTier = SubscriptionTier.FREE,
    val isActive: Boolean = false,
    val startDate: Long = System.currentTimeMillis(),
    val endDate: Long = 0L, // 0 = غير محدود للمجاني
    val paymentId: String = "", // معرف الدفع
    val autoRenew: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    // التحقق من صلاحية الاشتراك
    fun isValid(): Boolean {
        return when (tier) {
            SubscriptionTier.FREE -> true // المجاني دائماً صالح
            else -> isActive && (endDate == 0L || System.currentTimeMillis() < endDate)
        }
    }

    // الحصول على الأيام المتبقية
    fun getDaysRemaining(): Int {
        return if (tier == SubscriptionTier.FREE || endDate == 0L) {
            -1 // غير محدود
        } else {
            val remaining = (endDate - System.currentTimeMillis()) / (24 * 60 * 60 * 1000)
            maxOf(0, remaining.toInt())
        }
    }

    // التحقق من إمكانية إنشاء مجموعة بعدد معين من الأعضاء
    fun canCreateGroupWithMembers(memberCount: Int): Boolean {
        return if (!isValid()) {
            memberCount <= SubscriptionTier.FREE.maxMembers
        } else {
            memberCount <= tier.maxMembers
        }
    }

    // الحصول على الحد الأقصى للأعضاء
    fun getMaxMembers(): Int {
        return if (isValid()) tier.maxMembers else SubscriptionTier.FREE.maxMembers
    }
}

// خيارات إنشاء المجموعة
data class GroupCreationOption(
    val maxMembers: Int,
    val tier: SubscriptionTier,
    val isAvailable: Boolean,
    val requiresUpgrade: Boolean = false
) {
    fun getDisplayText(): String {
        return when {
            maxMembers == Int.MAX_VALUE -> "غير محدود"
            else -> "$maxMembers أعضاء"
        }
    }

    fun getSubtitle(): String {
        return when {
            !isAvailable && requiresUpgrade -> "يتطلب ${tier.getDisplayName()} - ${tier.getPrice()}"
            tier == SubscriptionTier.FREE -> "مجاني"
            else -> "${tier.getDisplayName()} - ${tier.getPrice()}"
        }
    }
}

// معلومات الدفع
data class PaymentInfo(
    val id: String = "",
    val userId: String = "",
    val tier: SubscriptionTier = SubscriptionTier.FREE,
    val amount: Double = 0.0,
    val currency: String = "USD",
    val status: PaymentStatus = PaymentStatus.PENDING,
    val paymentMethod: String = "", // "google_play", "paypal", etc.
    val transactionId: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val completedAt: Long = 0L
)

enum class PaymentStatus {
    PENDING,
    COMPLETED,
    FAILED,
    CANCELLED,
    REFUNDED
}

// إحصائيات الاستخدام
data class UsageStats(
    val userId: String = "",
    val groupsCreated: Int = 0,
    val totalMembers: Int = 0,
    val videosUploaded: Int = 0,
    val totalWatchTime: Long = 0L, // بالدقائق
    val messagesCount: Int = 0,
    val lastActivity: Long = System.currentTimeMillis()
)
