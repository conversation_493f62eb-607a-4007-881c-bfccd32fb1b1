package com.newt.anime.ui.components

import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.newt.anime.data.models.VideoSession

@Composable
fun VideoPlayer(
    videoSession: VideoSession,
    isOwner: Boolean,
    onPlayPause: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var webView by remember { mutableStateOf<WebView?>(null) }
    
    Column(modifier = modifier) {
        // Video title - بسيط
        if (videoSession.title.isNotEmpty()) {
            Text(
                text = videoSession.title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }
        
        // Video player - بسيط جداً بدون أي تعقيدات
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp) // ارتفاع ثابت
        ) {
            if (videoSession.videoUrl.isNotEmpty()) {
                Box(modifier = Modifier.fillMaxSize()) {
                    AndroidView(
                        factory = { context ->
                            WebView(context).apply {
                                webViewClient = object : WebViewClient() {
                                    override fun onPageFinished(view: WebView?, url: String?) {
                                        super.onPageFinished(view, url)
                                        // تطبيق حالة التشغيل الحالية بعد تحميل الصفحة
                                        val jsCode = if (videoSession.isPlaying) {
                                            """
                                            javascript:
                                            setTimeout(function(){
                                                var video = document.querySelector('video');
                                                if(video) {
                                                    video.play().catch(function(error){
                                                        console.log('Play failed: ' + error);
                                                    });
                                                }
                                                // إخفاء عناصر التحكم في Google Drive
                                                var controls = document.querySelectorAll('[data-tooltip="تشغيل"], [data-tooltip="إيقاف مؤقت"], .ytp-play-button');
                                                controls.forEach(function(control) {
                                                    control.style.display = 'none';
                                                });
                                            }, 3000);
                                            """.trimIndent()
                                        } else {
                                            """
                                            javascript:
                                            setTimeout(function(){
                                                var video = document.querySelector('video');
                                                if(video) {
                                                    video.pause();
                                                }
                                                // إخفاء عناصر التحكم في Google Drive
                                                var controls = document.querySelectorAll('[data-tooltip="تشغيل"], [data-tooltip="إيقاف مؤقت"], .ytp-play-button');
                                                controls.forEach(function(control) {
                                                    control.style.display = 'none';
                                                });
                                            }, 3000);
                                            """.trimIndent()
                                        }
                                        view?.evaluateJavascript(jsCode, null)
                                    }
                                }

                                // إعدادات محسنة للتشغيل الفوري مثل YouTube
                                settings.javaScriptEnabled = true
                                settings.domStorageEnabled = true
                                settings.databaseEnabled = true
                                settings.mediaPlaybackRequiresUserGesture = false
                                settings.setSupportZoom(false)
                                settings.builtInZoomControls = false
                                settings.displayZoomControls = false
                                settings.allowFileAccess = true
                                settings.allowContentAccess = true
                                settings.allowUniversalAccessFromFileURLs = true
                                settings.allowFileAccessFromFileURLs = true
                                settings.mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                                settings.loadWithOverviewMode = true
                                settings.useWideViewPort = true

                                // إعدادات التخزين المؤقت للتشغيل السريع
                                settings.cacheMode = android.webkit.WebSettings.LOAD_CACHE_ELSE_NETWORK

                                // User Agent محسن لـ YouTube-style playback
                                settings.userAgentString = "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36"

                                // حفظ مرجع WebView للتحكم
                                webView = this

                                // WebViewClient محسن لـ Firebase Storage مع تشخيص
                                webViewClient = object : android.webkit.WebViewClient() {
                                    override fun shouldOverrideUrlLoading(view: android.webkit.WebView?, request: android.webkit.WebResourceRequest?): Boolean {
                                        return false // السماح بتحميل جميع الروابط
                                    }

                                    override fun onPageFinished(view: android.webkit.WebView?, url: String?) {
                                        super.onPageFinished(view, url)

                                        // تشخيص Firebase
                                        val diagnosticJs = """
                                            console.log('🔍 Firebase Diagnostic:');
                                            console.log('Page loaded: ' + window.location.href);

                                            var video = document.querySelector('video');
                                            if (video) {
                                                console.log('✅ Video element found');
                                                console.log('Video src: ' + video.src);
                                                console.log('Video readyState: ' + video.readyState);
                                                console.log('Video networkState: ' + video.networkState);

                                                // اختبار تحميل الفيديو
                                                video.addEventListener('loadstart', function() {
                                                    console.log('📥 Video load started');
                                                });

                                                video.addEventListener('progress', function() {
                                                    console.log('📊 Video loading progress...');
                                                });

                                                video.addEventListener('suspend', function() {
                                                    console.log('⏸️ Video loading suspended');
                                                });

                                                video.addEventListener('stalled', function() {
                                                    console.log('⚠️ Video loading stalled');
                                                });
                                            } else {
                                                console.log('❌ No video element found');
                                            }
                                        """.trimIndent()
                                        view?.evaluateJavascript(diagnosticJs, null)

                                        // تشغيل تلقائي إذا كان مطلوب
                                        if (videoSession.isPlaying) {
                                            val autoPlayJs = """
                                                setTimeout(function() {
                                                    var video = document.querySelector('video');
                                                    if (video && video.paused) {
                                                        console.log('🎬 Attempting auto-play...');
                                                        video.play().then(function() {
                                                            console.log('✅ Auto-play successful');
                                                        }).catch(function(e) {
                                                            console.log('❌ Auto-play failed:', e);
                                                        });
                                                    }
                                                }, 2000);
                                            """.trimIndent()
                                            view?.evaluateJavascript(autoPlayJs, null)
                                        }
                                    }

                                    override fun onReceivedError(view: android.webkit.WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                                        super.onReceivedError(view, errorCode, description, failingUrl)
                                        android.util.Log.e("VideoPlayer", "WebView error: $errorCode - $description for URL: $failingUrl")
                                    }
                                }

                                // تحويل رابط الفيديو (Firebase Storage أو Google Drive)
                                val embedUrl = convertToEmbedUrl(videoSession.videoUrl)

                                // معالجة خاصة لـ Firebase Storage مع إصلاح مشاكل CORS
                                if (videoSession.videoUrl.contains("firebasestorage.googleapis.com") ||
                                    videoSession.videoUrl.contains("appspot.com") ||
                                    videoSession.videoUrl.endsWith(".mp4") ||
                                    videoSession.videoUrl.endsWith(".webm")) {

                                    val controlsAttribute = if (isOwner) "controls" else ""

                                    // إضافة token للوصول المباشر
                                    val directUrl = if (videoSession.videoUrl.contains("?")) {
                                        "${videoSession.videoUrl}&alt=media"
                                    } else {
                                        "${videoSession.videoUrl}?alt=media"
                                    }

                                    val htmlContent = """
                                        <!DOCTYPE html>
                                        <html>
                                        <head>
                                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                            <meta charset="UTF-8">
                                            <style>
                                                * {
                                                    margin: 0;
                                                    padding: 0;
                                                    box-sizing: border-box;
                                                }

                                                body {
                                                    background: #000;
                                                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
                                                    overflow: hidden;
                                                    height: 100vh;
                                                    width: 100vw;
                                                }

                                                .youtube-player {
                                                    position: relative;
                                                    width: 100%;
                                                    height: 100%;
                                                    background: #000;
                                                    display: flex;
                                                    align-items: center;
                                                    justify-content: center;
                                                }

                                                video {
                                                    width: 100% !important;
                                                    height: 100% !important;
                                                    object-fit: contain;
                                                    outline: none;
                                                    background: #000;
                                                    cursor: pointer;
                                                    display: block !important;
                                                    visibility: visible !important;
                                                    opacity: 1 !important;
                                                    z-index: 1;
                                                }

                                                /* تحكم مثل YouTube للمالك فقط */
                                                video::-webkit-media-controls {
                                                    display: ${if (isOwner) "flex" else "none"} !important;
                                                }

                                                video::-webkit-media-controls-panel {
                                                    background: linear-gradient(transparent, rgba(0,0,0,0.8));
                                                    border-radius: 0;
                                                }

                                                video::-webkit-media-controls-play-button,
                                                video::-webkit-media-controls-pause-button {
                                                    background: rgba(255,255,255,0.9);
                                                    border-radius: 50%;
                                                    margin: 0 8px;
                                                }

                                                video::-webkit-media-controls-timeline {
                                                    background: rgba(255,255,255,0.3);
                                                    border-radius: 2px;
                                                    margin: 0 8px;
                                                }

                                                video::-webkit-media-controls-current-time-display,
                                                video::-webkit-media-controls-time-remaining-display {
                                                    color: white;
                                                    font-size: 12px;
                                                    text-shadow: 1px 1px 1px rgba(0,0,0,0.8);
                                                }

                                                /* منع التحكم للمشاهدين */
                                                .viewer-overlay {
                                                    position: absolute;
                                                    top: 0;
                                                    left: 0;
                                                    width: 100%;
                                                    height: 100%;
                                                    background: transparent;
                                                    z-index: ${if (isOwner) "-1" else "999"};
                                                    pointer-events: ${if (isOwner) "none" else "auto"};
                                                }

                                                /* مؤشر التحميل */
                                                .loading-overlay {
                                                    position: absolute;
                                                    top: 0;
                                                    left: 0;
                                                    width: 100%;
                                                    height: 100%;
                                                    background: rgba(0,0,0,0.8);
                                                    display: flex;
                                                    flex-direction: column;
                                                    align-items: center;
                                                    justify-content: center;
                                                    z-index: 1000;
                                                }

                                                .loading-spinner {
                                                    width: 60px;
                                                    height: 60px;
                                                    border: 4px solid rgba(255,255,255,0.3);
                                                    border-top: 4px solid #ff0000;
                                                    border-radius: 50%;
                                                    animation: spin 1s linear infinite;
                                                    margin-bottom: 20px;
                                                }

                                                @keyframes spin {
                                                    0% { transform: rotate(0deg); }
                                                    100% { transform: rotate(360deg); }
                                                }

                                                .loading-text {
                                                    color: white;
                                                    font-size: 16px;
                                                    font-weight: 500;
                                                    text-align: center;
                                                }

                                                .viewer-indicator {
                                                    position: absolute;
                                                    top: 20px;
                                                    right: 20px;
                                                    background: rgba(0,0,0,0.8);
                                                    color: white;
                                                    padding: 8px 16px;
                                                    border-radius: 20px;
                                                    font-size: 14px;
                                                    z-index: 100;
                                                    display: ${if (isOwner) "none" else "block"};
                                                }

                                                .hidden {
                                                    display: none !important;
                                                }
                                            </style>
                                        </head>
                                        <body>
                                            <div class="youtube-player">
                                                <video
                                                    id="mainVideo"
                                                    controls="${if (isOwner) "true" else "false"}"
                                                    preload="auto"
                                                    playsinline
                                                    webkit-playsinline
                                                    loop
                                                    crossorigin="anonymous">
                                                    <source src="$directUrl" type="video/mp4">
                                                    <source src="$embedUrl" type="video/mp4">
                                                    متصفحك لا يدعم تشغيل الفيديو
                                                </video>

                                                <div id="loadingOverlay" class="loading-overlay">
                                                    <div class="loading-spinner"></div>
                                                    <div class="loading-text">جاري تحميل الفيديو...</div>
                                                </div>

                                                <div class="viewer-overlay"></div>
                                                <div class="viewer-indicator">👁️ وضع المشاهدة</div>
                                            </div>

                                            <script>
                                                const video = document.getElementById('mainVideo');
                                                const loadingOverlay = document.getElementById('loadingOverlay');
                                                const isOwner = ${isOwner};

                                                console.log('🎬 YouTube-style player initializing...');
                                                console.log('Original URL: $embedUrl');
                                                console.log('Direct URL: $directUrl');
                                                console.log('Is Owner: ' + isOwner);
                                                console.log('Should Play: ${videoSession.isPlaying}');

                                                // تشخيص شامل للفيديو
                                                function diagnoseVideo() {
                                                    console.log('🔍 VIDEO DIAGNOSIS:');
                                                    console.log('- Video element exists:', !!video);
                                                    console.log('- Video src:', video.src);
                                                    console.log('- Video readyState:', video.readyState);
                                                    console.log('- Video networkState:', video.networkState);
                                                    console.log('- Video paused:', video.paused);
                                                    console.log('- Video ended:', video.ended);
                                                    console.log('- Video duration:', video.duration);
                                                    console.log('- Video currentTime:', video.currentTime);
                                                    console.log('- Video videoWidth:', video.videoWidth);
                                                    console.log('- Video videoHeight:', video.videoHeight);
                                                    console.log('- Video style.display:', video.style.display);
                                                    console.log('- Video style.visibility:', video.style.visibility);
                                                    console.log('- Video style.opacity:', video.style.opacity);
                                                    console.log('- Loading overlay hidden:', loadingOverlay.classList.contains('hidden'));
                                                }

                                                // تشخيص كل 3 ثواني
                                                setInterval(diagnoseVideo, 3000);

                                                let hasStartedPlaying = false;
                                                let isVideoReady = false;

                                                // إظهار الفيديو فور تحميل البيانات - إصلاح مشكلة العرض
                                                video.addEventListener('loadstart', function() {
                                                    console.log('📥 Video loading started');
                                                    loadingOverlay.classList.remove('hidden');
                                                });

                                                video.addEventListener('loadedmetadata', function() {
                                                    console.log('✅ Video metadata loaded - showing video');
                                                    isVideoReady = true;
                                                    loadingOverlay.classList.add('hidden');

                                                    // إجبار إظهار الفيديو
                                                    video.style.display = 'block';
                                                    video.style.visibility = 'visible';
                                                    video.style.opacity = '1';

                                                    // تشغيل تلقائي عند الرفع
                                                    if (${videoSession.isPlaying} && !hasStartedPlaying) {
                                                        setTimeout(function() {
                                                            console.log('🎬 Attempting auto-play...');
                                                            video.muted = false;
                                                            video.play().then(() => {
                                                                console.log('▶️ Auto-play successful');
                                                                hasStartedPlaying = true;
                                                            }).catch(e => {
                                                                console.log('❌ Auto-play failed, trying muted:', e);
                                                                video.muted = true;
                                                                video.play().then(() => {
                                                                    console.log('✅ Muted play successful');
                                                                    hasStartedPlaying = true;
                                                                    setTimeout(() => {
                                                                        video.muted = false;
                                                                        console.log('🔊 Unmuted video');
                                                                    }, 1000);
                                                                }).catch(e2 => {
                                                                    console.error('❌ Even muted play failed:', e2);
                                                                });
                                                            });
                                                        }, 500);
                                                    }
                                                });

                                                video.addEventListener('loadeddata', function() {
                                                    console.log('✅ Video data loaded - ready to display');
                                                    isVideoReady = true;
                                                    loadingOverlay.classList.add('hidden');

                                                    // إجبار إظهار الفيديو
                                                    video.style.display = 'block';
                                                    video.style.visibility = 'visible';
                                                    video.style.opacity = '1';
                                                });

                                                video.addEventListener('canplay', function() {
                                                    console.log('✅ Video can play - forcing display');
                                                    isVideoReady = true;
                                                    loadingOverlay.classList.add('hidden');

                                                    // إجبار إظهار الفيديو
                                                    video.style.display = 'block';
                                                    video.style.visibility = 'visible';
                                                    video.style.opacity = '1';
                                                });

                                                video.addEventListener('playing', function() {
                                                    console.log('▶️ Video is now playing');
                                                    hasStartedPlaying = true;
                                                    loadingOverlay.classList.add('hidden');

                                                    // إجبار إظهار الفيديو
                                                    video.style.display = 'block';
                                                    video.style.visibility = 'visible';
                                                    video.style.opacity = '1';
                                                });

                                                video.addEventListener('pause', function() {
                                                    console.log('⏸️ Video paused');
                                                });

                                                video.addEventListener('waiting', function() {
                                                    console.log('⏳ Video buffering...');
                                                });

                                                video.addEventListener('stalled', function() {
                                                    console.log('⚠️ Video stalled - retrying...');
                                                    setTimeout(function() {
                                                        video.load();
                                                    }, 2000);
                                                });

                                                video.addEventListener('ended', function() {
                                                    console.log('🔄 Video ended - restarting (loop)');
                                                    video.currentTime = 0;
                                                    if (${videoSession.isPlaying}) {
                                                        video.play();
                                                    }
                                                });

                                                video.addEventListener('error', function(e) {
                                                    console.error('❌ Video error:', e);
                                                    console.error('Error details:', video.error);
                                                    console.error('Error code:', video.error ? video.error.code : 'unknown');

                                                    loadingSpinner.classList.add('hidden');

                                                    // رسائل خطأ مخصصة لـ Firebase
                                                    var errorMessage = 'خطأ في تحميل الفيديو';
                                                    if (video.error) {
                                                        switch(video.error.code) {
                                                            case 1: // MEDIA_ERR_ABORTED
                                                                errorMessage = 'تم إلغاء تحميل الفيديو';
                                                                break;
                                                            case 2: // MEDIA_ERR_NETWORK
                                                                errorMessage = 'خطأ في الشبكة - تحقق من الاتصال';
                                                                break;
                                                            case 3: // MEDIA_ERR_DECODE
                                                                errorMessage = 'خطأ في فك تشفير الفيديو';
                                                                break;
                                                            case 4: // MEDIA_ERR_SRC_NOT_SUPPORTED
                                                                errorMessage = 'صيغة الفيديو غير مدعومة أو رابط Firebase خاطئ';
                                                                break;
                                                            default:
                                                                errorMessage = 'خطأ غير معروف في الفيديو';
                                                        }
                                                    }

                                                    statusText.textContent = errorMessage;
                                                    statusText.style.background = 'rgba(255,0,0,0.8)';
                                                    statusText.classList.remove('hidden');

                                                    // محاولة إعادة تحميل بعد 3 ثواني
                                                    setTimeout(function() {
                                                        console.log('🔄 Retrying video load...');
                                                        video.load();
                                                    }, 3000);
                                                });

                                                // منع التحكم للمشاهدين
                                                if (!isOwner) {
                                                    document.addEventListener('click', function(e) {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                    });

                                                    document.addEventListener('contextmenu', function(e) {
                                                        e.preventDefault();
                                                    });

                                                    document.addEventListener('keydown', function(e) {
                                                        if (e.code === 'Space' || e.code === 'ArrowLeft' || e.code === 'ArrowRight') {
                                                            e.preventDefault();
                                                        }
                                                    });
                                                }

                                                // بدء تحميل الفيديو فوراً
                                                video.load();

                                                // تحقق دوري لإجبار عرض الفيديو
                                                function forceVideoDisplay() {
                                                    console.log('🔧 Forcing video display...');
                                                    video.style.display = 'block';
                                                    video.style.visibility = 'visible';
                                                    video.style.opacity = '1';
                                                    video.style.background = 'transparent';

                                                    // إخفاء التحميل إذا كان الفيديو جاهز
                                                    if (video.readyState >= 2) { // HAVE_CURRENT_DATA
                                                        console.log('✅ Video has data - hiding loading');
                                                        loadingOverlay.classList.add('hidden');
                                                        isVideoReady = true;
                                                    }
                                                }

                                                // تحقق كل ثانية
                                                setInterval(forceVideoDisplay, 1000);

                                                // تحقق دوري من حالة الفيديو
                                                setTimeout(function() {
                                                    if (!isVideoReady) {
                                                        console.log('⚠️ Video not ready after 5 seconds, retrying...');
                                                        video.load();
                                                        forceVideoDisplay();
                                                    }
                                                }, 5000);

                                                // تحقق إضافي بعد 10 ثواني
                                                setTimeout(function() {
                                                    if (!isVideoReady) {
                                                        console.log('🔄 Final attempt to show video...');
                                                        loadingOverlay.classList.add('hidden');
                                                        forceVideoDisplay();

                                                        // محاولة تشغيل إجباري
                                                        if (${videoSession.isPlaying}) {
                                                            video.play().catch(e => {
                                                                console.log('Final play attempt failed:', e);
                                                            });
                                                        }
                                                    }
                                                }, 10000);
                                            </script>
                                        </body>
                                        </html>
                                    """.trimIndent()
                                    loadDataWithBaseURL("https://firebasestorage.googleapis.com/", htmlContent, "text/html", "UTF-8", null)
                                } else {
                                    loadUrl(embedUrl)
                                }
                            }
                        },
                        update = { view ->
                            // تحديث حالة التشغيل للجميع (مالك ومشاهدين) مع طرق متعددة
                            val jsCode = if (videoSession.isPlaying) {
                                """
                                javascript:
                                function syncPlay() {
                                    console.log('▶️ YOUTUBE-STYLE SYNC: Starting video...');

                                    var video = document.querySelector('video');
                                    if(video) {
                                        console.log('Video found - paused: ' + video.paused);
                                        if(video.paused) {
                                            // تشغيل فوري مثل YouTube
                                            video.muted = false;
                                            video.play().then(function(){
                                                console.log('✅ YouTube-style play successful');
                                                // إخفاء مؤشرات التحميل
                                                var loading = document.getElementById('loadingSpinner');
                                                var status = document.getElementById('statusText');
                                                if(loading) loading.classList.add('hidden');
                                                if(status) status.classList.add('hidden');
                                            }).catch(function(error){
                                                console.log('❌ Play failed, trying muted: ' + error);
                                                video.muted = true;
                                                video.play().then(function(){
                                                    setTimeout(function(){
                                                        video.muted = false;
                                                        console.log('✅ Muted play successful');
                                                    }, 300);
                                                });
                                            });
                                        }

                                        // تزامن الوقت
                                        var targetTime = ${videoSession.currentPosition / 1000};
                                        if(Math.abs(video.currentTime - targetTime) > 2) {
                                            video.currentTime = targetTime;
                                            console.log('🕐 Time synced to: ' + targetTime);
                                        }
                                    } else {
                                        console.log('❌ No video element found');
                                    }
                                }

                                setTimeout(syncPlay, 200);
                                """.trimIndent()
                            } else {
                                """
                                javascript:
                                function syncPause() {
                                    console.log('⏸️ YOUTUBE-STYLE SYNC: Pausing video...');

                                    var video = document.querySelector('video');
                                    if(video && !video.paused) {
                                        video.pause();
                                        console.log('✅ YouTube-style pause successful');
                                    }
                                }

                                setTimeout(syncPause, 200);
                                """.trimIndent()
                            }
                            view.evaluateJavascript(jsCode, null)
                        },
                        modifier = Modifier.fillMaxSize()
                    )

                    // طبقة شفافة لمنع اللمس للجميع
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .clickable(enabled = false) { /* منع جميع اللمسات */ },
                        contentAlignment = Alignment.Center
                    ) {
                        // مؤشر بسيط للحالة
                        if (!isOwner) {
                            Card(
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.8f)
                                ),
                                modifier = Modifier.padding(16.dp)
                            ) {
                                Text(
                                    text = "👁️ وضع المشاهدة",
                                    modifier = Modifier.padding(8.dp),
                                    fontSize = 12.sp,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                            }
                        }
                    }
                }
            } else {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "لا يوجد فيديو",
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        // أزرار تحكم للمالك فقط
        if (isOwner && videoSession.videoUrl.isNotEmpty()) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "🎮 تحكم المالك",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )

                    // زر التشغيل/الإيقاف المتقدم
                    Button(
                        onClick = {
                            // تحديث قاعدة البيانات أولاً
                            onPlayPause(!videoSession.isPlaying)

                            // تحكم مباشر فوري في WebView مع طرق متعددة
                            webView?.let { view ->
                                val jsCode = if (!videoSession.isPlaying) {
                                    """
                                    javascript:
                                    function playVideo() {
                                        console.log('Attempting to play video...');

                                        // الطريقة الأولى: البحث عن عنصر video مباشرة
                                        var video = document.querySelector('video');
                                        if(video) {
                                            console.log('Found video element, attempting play...');
                                            video.play().then(function(){
                                                console.log('Video started successfully');
                                            }).catch(function(error){
                                                console.log('Direct play failed: ' + error);
                                            });
                                        }

                                        // الطريقة الثانية: محاولة النقر على زر التشغيل في Google Drive
                                        var playButtons = document.querySelectorAll('[aria-label*="play"], [aria-label*="Play"], [data-tooltip*="تشغيل"], .ytp-play-button');
                                        playButtons.forEach(function(btn) {
                                            if(btn && btn.click) {
                                                console.log('Clicking play button');
                                                btn.click();
                                            }
                                        });

                                        // الطريقة الثالثة: إرسال مفاتيح
                                        document.dispatchEvent(new KeyboardEvent('keydown', {
                                            key: ' ',
                                            code: 'Space',
                                            keyCode: 32
                                        }));

                                        // الطريقة الرابعة: محاولة تفعيل التشغيل التلقائي
                                        if(video) {
                                            video.autoplay = true;
                                            video.muted = false;
                                        }
                                    }

                                    setTimeout(playVideo, 500);
                                    """.trimIndent()
                                } else {
                                    """
                                    javascript:
                                    function pauseVideo() {
                                        console.log('Attempting to pause video...');

                                        // الطريقة الأولى: إيقاف مباشر
                                        var video = document.querySelector('video');
                                        if(video) {
                                            video.pause();
                                            console.log('Video paused successfully');
                                        }

                                        // الطريقة الثانية: النقر على زر الإيقاف
                                        var pauseButtons = document.querySelectorAll('[aria-label*="pause"], [aria-label*="Pause"], [data-tooltip*="إيقاف"], .ytp-pause-button');
                                        pauseButtons.forEach(function(btn) {
                                            if(btn && btn.click) {
                                                console.log('Clicking pause button');
                                                btn.click();
                                            }
                                        });
                                    }

                                    setTimeout(pauseVideo, 500);
                                    """.trimIndent()
                                }
                                view.evaluateJavascript(jsCode, null)
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(64.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (videoSession.isPlaying)
                                MaterialTheme.colorScheme.error
                            else
                                MaterialTheme.colorScheme.primary
                        )
                    ) {
                        if (videoSession.isPlaying) {
                            Text("⏸", fontSize = 28.sp)
                            Spacer(modifier = Modifier.width(12.dp))
                            Text("إيقاف الفيديو", fontSize = 18.sp, fontWeight = FontWeight.Bold)
                        } else {
                            Text("▶", fontSize = 28.sp)
                            Spacer(modifier = Modifier.width(12.dp))
                            Text("تشغيل الفيديو", fontSize = 18.sp, fontWeight = FontWeight.Bold)
                        }
                    }

                    // أزرار إضافية للمالك
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 8.dp),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // زر إعادة تحميل
                        OutlinedButton(
                            onClick = {
                                webView?.let { view ->
                                    val embedUrl = convertToEmbedUrl(videoSession.videoUrl)
                                    view.loadUrl(embedUrl)
                                }
                            },
                            modifier = Modifier
                                .weight(1f)
                                .height(48.dp)
                        ) {
                            Text("🔄", fontSize = 16.sp)
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("إعادة تحميل", fontSize = 12.sp)
                        }

                        // زر اختبار التشغيل
                        OutlinedButton(
                            onClick = {
                                webView?.let { view ->
                                    val testJs = """
                                        javascript:
                                        console.log('=== TEST PLAY ===');
                                        var video = document.querySelector('video');
                                        if(video) {
                                            console.log('Video found, current state: paused=' + video.paused);
                                            video.muted = true;
                                            video.play().then(function(){
                                                console.log('TEST: Video started');
                                                video.muted = false;
                                            }).catch(function(error){
                                                console.log('TEST: Play failed - ' + error);
                                            });
                                        } else {
                                            console.log('TEST: No video element found');
                                        }
                                    """.trimIndent()
                                    view.evaluateJavascript(testJs, null)
                                }
                            },
                            modifier = Modifier
                                .weight(1f)
                                .height(48.dp)
                        ) {
                            Text("🧪", fontSize = 16.sp)
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("اختبار", fontSize = 12.sp)
                        }
                    }

                    Text(
                        text = "التغييرات ستظهر لجميع المشاهدين",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f),
                        modifier = Modifier.padding(top = 8.dp)
                    )
                }
            }
        }

        // مؤشر الحالة للجميع
        if (videoSession.videoUrl.isNotEmpty()) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = if (videoSession.isPlaying)
                        MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.5f)
                    else
                        MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = if (videoSession.isPlaying) "▶ يتم التشغيل الآن" else "⏸ الفيديو متوقف",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = if (videoSession.isPlaying)
                            MaterialTheme.colorScheme.primary
                        else
                            MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Text(
                        text = if (isOwner) "👑 أنت مالك المجموعة" else "👁️ أنت مشاهد",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f),
                        modifier = Modifier.padding(top = 4.dp)
                    )

                    if (!isOwner) {
                        Text(
                            text = "المالك يتحكم في التشغيل",
                            fontSize = 10.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                            modifier = Modifier.padding(top = 2.dp)
                        )
                    }
                }
            }
        }
    }
}

private fun convertToEmbedUrl(videoUrl: String): String {
    return when {
        // Firebase Storage URLs - تشغيل مباشر
        videoUrl.contains("firebasestorage.googleapis.com") || videoUrl.contains("appspot.com") -> {
            videoUrl // استخدام الرابط مباشرة
        }
        // Google Drive URLs
        videoUrl.contains("drive.google.com/file/d/") -> {
            val fileId = videoUrl.substringAfter("/file/d/").substringBefore("/")
            "https://drive.google.com/file/d/$fileId/preview?usp=sharing&autoplay=0"
        }
        videoUrl.contains("drive.google.com/open?id=") -> {
            val fileId = videoUrl.substringAfter("id=").substringBefore("&")
            "https://drive.google.com/file/d/$fileId/preview?usp=sharing&autoplay=0"
        }
        videoUrl.contains("/preview") -> {
            if (videoUrl.contains("autoplay")) videoUrl else "$videoUrl?autoplay=0"
        }
        // YouTube URLs
        videoUrl.contains("youtube.com") || videoUrl.contains("youtu.be") -> {
            if (videoUrl.contains("youtu.be")) {
                val videoId = videoUrl.substringAfter("youtu.be/").substringBefore("?")
                "https://www.youtube.com/embed/$videoId?autoplay=0&controls=0&disablekb=1"
            } else {
                val videoId = videoUrl.substringAfter("v=").substringBefore("&")
                "https://www.youtube.com/embed/$videoId?autoplay=0&controls=0&disablekb=1"
            }
        }
        // روابط فيديو مباشرة
        videoUrl.endsWith(".mp4") || videoUrl.endsWith(".webm") || videoUrl.endsWith(".ogg") -> {
            videoUrl
        }
        else -> videoUrl
    }
}
