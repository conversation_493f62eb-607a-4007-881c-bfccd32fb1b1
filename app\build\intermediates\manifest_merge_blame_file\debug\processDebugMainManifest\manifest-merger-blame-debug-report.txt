1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.newt.anime"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:7:5-80
13-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:7:22-77
14    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
14-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:8:5-75
14-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:8:22-72
15    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
15-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7bf79abb5eefabf570c9b9d5649778\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
15-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7bf79abb5eefabf570c9b9d5649778\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
16
17    <permission
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8f761d99cc674c08e5b049b2a7bbf06\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.newt.anime.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8f761d99cc674c08e5b049b2a7bbf06\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8f761d99cc674c08e5b049b2a7bbf06\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.newt.anime.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8f761d99cc674c08e5b049b2a7bbf06\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8f761d99cc674c08e5b049b2a7bbf06\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:10:5-32:19
24        android:allowBackup="true"
24-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:11:9-35
25        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
25-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8f761d99cc674c08e5b049b2a7bbf06\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
26        android:dataExtractionRules="@xml/data_extraction_rules"
26-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:12:9-65
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:fullBackupContent="@xml/backup_rules"
29-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:13:9-54
30        android:icon="@mipmap/ic_launcher"
30-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:14:9-43
31        android:label="@string/app_name"
31-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:15:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:16:9-54
33        android:supportsRtl="true"
33-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:17:9-35
34        android:theme="@style/Theme.Anime" >
34-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:18:9-43
35        <activity
35-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:20:9-31:20
36            android:name="com.newt.anime.MainActivity"
36-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:21:13-41
37            android:configChanges="orientation|screenSize|keyboardHidden"
37-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:24:13-74
38            android:exported="true"
38-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:22:13-36
39            android:label="@string/app_name"
39-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:23:13-45
40            android:theme="@style/Theme.Anime" >
40-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:25:13-47
41            <intent-filter>
41-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:26:13-30:29
42                <action android:name="android.intent.action.MAIN" />
42-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:27:17-69
42-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:27:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:29:17-77
44-->D:\kotlin aNIME\anime\app\src\main\AndroidManifest.xml:29:27-74
45            </intent-filter>
46        </activity>
47
48        <service
48-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f55d8baef200b16f577a7ee663617c7\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:8:9-14:19
49            android:name="com.google.firebase.components.ComponentDiscoveryService"
49-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f55d8baef200b16f577a7ee663617c7\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:9:13-84
50            android:directBootAware="true"
50-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
51            android:exported="false" >
51-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f55d8baef200b16f577a7ee663617c7\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:10:13-37
52            <meta-data
52-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f55d8baef200b16f577a7ee663617c7\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:11:13-13:85
53                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
53-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f55d8baef200b16f577a7ee663617c7\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:12:17-119
54                android:value="com.google.firebase.components.ComponentRegistrar" />
54-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f55d8baef200b16f577a7ee663617c7\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:13:17-82
55            <meta-data
55-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
56                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
56-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
57                android:value="com.google.firebase.components.ComponentRegistrar" />
57-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
58            <meta-data
58-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fecb64bf210323d6e99b23a0e30bd39\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:12:13-14:85
59                android:name="com.google.firebase.components:com.google.firebase.storage.ktx.FirebaseStorageLegacyRegistrar"
59-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fecb64bf210323d6e99b23a0e30bd39\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:13:17-125
60                android:value="com.google.firebase.components.ComponentRegistrar" />
60-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fecb64bf210323d6e99b23a0e30bd39\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:14:17-82
61            <meta-data
61-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6da2756227281b4ea2fa2f91a2327ee\transformed\firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
62                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
62-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6da2756227281b4ea2fa2f91a2327ee\transformed\firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
63                android:value="com.google.firebase.components.ComponentRegistrar" />
63-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6da2756227281b4ea2fa2f91a2327ee\transformed\firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
64            <meta-data
64-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6da2756227281b4ea2fa2f91a2327ee\transformed\firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
65                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
65-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6da2756227281b4ea2fa2f91a2327ee\transformed\firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
66                android:value="com.google.firebase.components.ComponentRegistrar" />
66-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6da2756227281b4ea2fa2f91a2327ee\transformed\firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
67            <meta-data
67-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee53e9f74af6515bc2e52704c67fcc5e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
68                android:name="com.google.firebase.components:com.google.firebase.database.ktx.FirebaseDatabaseLegacyRegistrar"
68-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee53e9f74af6515bc2e52704c67fcc5e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:13:17-127
69                android:value="com.google.firebase.components.ComponentRegistrar" />
69-->[com.google.firebase:firebase-database-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee53e9f74af6515bc2e52704c67fcc5e\transformed\firebase-database-ktx-21.0.0\AndroidManifest.xml:14:17-82
70            <meta-data
70-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\689af1533326f19d71171930c5104c1b\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
71                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
71-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\689af1533326f19d71171930c5104c1b\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
72                android:value="com.google.firebase.components.ComponentRegistrar" />
72-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\689af1533326f19d71171930c5104c1b\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
73            <meta-data
73-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\689af1533326f19d71171930c5104c1b\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
74                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
74-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\689af1533326f19d71171930c5104c1b\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
75                android:value="com.google.firebase.components.ComponentRegistrar" />
75-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\689af1533326f19d71171930c5104c1b\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
76            <meta-data
76-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43e19c78424370108c7d354cbd31dace\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
77                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
77-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43e19c78424370108c7d354cbd31dace\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
78                android:value="com.google.firebase.components.ComponentRegistrar" />
78-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43e19c78424370108c7d354cbd31dace\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
79            <meta-data
79-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43e19c78424370108c7d354cbd31dace\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
80                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
80-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43e19c78424370108c7d354cbd31dace\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
81                android:value="com.google.firebase.components.ComponentRegistrar" />
81-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43e19c78424370108c7d354cbd31dace\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
82            <meta-data
82-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8663db35fed1d8fe5a2268b4054a56a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
83                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
83-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8663db35fed1d8fe5a2268b4054a56a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
84                android:value="com.google.firebase.components.ComponentRegistrar" />
84-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8663db35fed1d8fe5a2268b4054a56a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
85            <meta-data
85-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
86                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
86-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
87                android:value="com.google.firebase.components.ComponentRegistrar" />
87-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
88        </service>
89
90        <activity
90-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
91            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
91-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
92            android:excludeFromRecents="true"
92-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
93            android:exported="true"
93-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
94            android:launchMode="singleTask"
94-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
95            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
95-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
96            <intent-filter>
96-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
97                <action android:name="android.intent.action.VIEW" />
97-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
97-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
98
99                <category android:name="android.intent.category.DEFAULT" />
99-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
99-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
100                <category android:name="android.intent.category.BROWSABLE" />
100-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
100-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
101
102                <data
102-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
103                    android:host="firebase.auth"
103-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
104                    android:path="/"
104-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
105                    android:scheme="genericidp" />
105-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
106            </intent-filter>
107        </activity>
108        <activity
108-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
109            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
109-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
110            android:excludeFromRecents="true"
110-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
111            android:exported="true"
111-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
112            android:launchMode="singleTask"
112-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
113            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
113-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
114            <intent-filter>
114-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
115                <action android:name="android.intent.action.VIEW" />
115-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
115-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
116
117                <category android:name="android.intent.category.DEFAULT" />
117-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
117-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
118                <category android:name="android.intent.category.BROWSABLE" />
118-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
118-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
119
120                <data
120-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
121                    android:host="firebase.auth"
121-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
122                    android:path="/"
122-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
123                    android:scheme="recaptcha" />
123-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd3d4a193dd78cb63d17290c3bb56fa7\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
124            </intent-filter>
125        </activity>
126
127        <service
127-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
128            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
128-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
129            android:enabled="true"
129-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
130            android:exported="false" >
130-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
131            <meta-data
131-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
132                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
132-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
133                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
133-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
134        </service>
135
136        <activity
136-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
137            android:name="androidx.credentials.playservices.HiddenActivity"
137-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
138            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
138-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
139            android:enabled="true"
139-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
140            android:exported="false"
140-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
141            android:fitsSystemWindows="true"
141-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
142            android:theme="@style/Theme.Hidden" >
142-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1713a2ee3140680f0f03dc575dc0d584\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
143        </activity>
144        <activity
144-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
145            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
145-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
146            android:excludeFromRecents="true"
146-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
147            android:exported="false"
147-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
148            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
148-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
149        <!--
150            Service handling Google Sign-In user revocation. For apps that do not integrate with
151            Google Sign-In, this service will never be started.
152        -->
153        <service
153-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
154            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
154-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
155            android:exported="true"
155-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
156            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
156-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
157            android:visibleToInstantApps="true" />
157-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a273cc01e463f207b480617c1fb5c008\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
158
159        <activity
159-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f341d0dad5e087546904e2a03fd632f3\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
160            android:name="com.google.android.gms.common.api.GoogleApiActivity"
160-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f341d0dad5e087546904e2a03fd632f3\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
161            android:exported="false"
161-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f341d0dad5e087546904e2a03fd632f3\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
162            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
162-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f341d0dad5e087546904e2a03fd632f3\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
163
164        <provider
164-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
165            android:name="com.google.firebase.provider.FirebaseInitProvider"
165-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
166            android:authorities="com.newt.anime.firebaseinitprovider"
166-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
167            android:directBootAware="true"
167-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
168            android:exported="false"
168-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
169            android:initOrder="100" />
169-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dc57017c772c36652cc489d71502edb\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
170
171        <activity
171-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6685ec2bbbe7dfadc70b3acdfd8f699\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
172            android:name="androidx.activity.ComponentActivity"
172-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6685ec2bbbe7dfadc70b3acdfd8f699\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
173            android:exported="true" />
173-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6685ec2bbbe7dfadc70b3acdfd8f699\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
174        <activity
174-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2afef4ce4ecc792ccc074d82f75509f4\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
175            android:name="androidx.compose.ui.tooling.PreviewActivity"
175-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2afef4ce4ecc792ccc074d82f75509f4\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
176            android:exported="true" />
176-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2afef4ce4ecc792ccc074d82f75509f4\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
177
178        <provider
178-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
179            android:name="androidx.startup.InitializationProvider"
179-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
180            android:authorities="com.newt.anime.androidx-startup"
180-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
181            android:exported="false" >
181-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
182            <meta-data
182-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
183                android:name="androidx.emoji2.text.EmojiCompatInitializer"
183-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
184                android:value="androidx.startup" />
184-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f0bc976ace4e55183076e160aed0c5f\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
185            <meta-data
185-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167db888705b0b33989cb385bedff820\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
186                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
186-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167db888705b0b33989cb385bedff820\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
187                android:value="androidx.startup" />
187-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167db888705b0b33989cb385bedff820\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
188            <meta-data
188-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
189                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
189-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
190                android:value="androidx.startup" />
190-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
191        </provider>
192
193        <meta-data
193-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4366d762d4e78881da860b75d1c4c59f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
194            android:name="com.google.android.gms.version"
194-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4366d762d4e78881da860b75d1c4c59f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
195            android:value="@integer/google_play_services_version" />
195-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4366d762d4e78881da860b75d1c4c59f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
196
197        <receiver
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
198            android:name="androidx.profileinstaller.ProfileInstallReceiver"
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
199            android:directBootAware="false"
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
200            android:enabled="true"
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
201            android:exported="true"
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
202            android:permission="android.permission.DUMP" >
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
203            <intent-filter>
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
204                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
205            </intent-filter>
206            <intent-filter>
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
207                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
208            </intent-filter>
209            <intent-filter>
209-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
210                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
211            </intent-filter>
212            <intent-filter>
212-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
213                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51d35009495bd962364f5846da64081\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
214            </intent-filter>
215        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
216        <activity
216-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56e7fd73ffc0fc8f87edb11391c42ff\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
217            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
217-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56e7fd73ffc0fc8f87edb11391c42ff\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
218            android:exported="false"
218-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56e7fd73ffc0fc8f87edb11391c42ff\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
219            android:stateNotNeeded="true"
219-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56e7fd73ffc0fc8f87edb11391c42ff\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
220            android:theme="@style/Theme.PlayCore.Transparent" />
220-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56e7fd73ffc0fc8f87edb11391c42ff\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
221    </application>
222
223</manifest>
