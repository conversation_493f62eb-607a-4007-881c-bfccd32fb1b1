package com.newt.anime.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.newt.anime.data.models.Group
import com.newt.anime.data.models.VideoSession
import com.newt.anime.data.repository.AuthRepository
import com.newt.anime.data.repository.GroupRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class GroupViewModel : ViewModel() {
    private val groupRepository = GroupRepository()
    private val authRepository = AuthRepository()
    
    private val _uiState = MutableStateFlow(GroupUiState())
    val uiState: StateFlow<GroupUiState> = _uiState.asStateFlow()
    
    private val _currentGroup = MutableStateFlow<Group?>(null)
    val currentGroup: StateFlow<Group?> = _currentGroup.asStateFlow()
    
    private val _userGroups = MutableStateFlow<List<Group>>(emptyList())
    val userGroups: StateFlow<List<Group>> = _userGroups.asStateFlow()

    private val _chatMessages = MutableStateFlow<List<com.newt.anime.data.models.ChatMessage>>(emptyList())
    val chatMessages: StateFlow<List<com.newt.anime.data.models.ChatMessage>> = _chatMessages.asStateFlow()

    private val _emojiReactions = MutableStateFlow<List<com.newt.anime.data.models.EmojiReaction>>(emptyList())
    val emojiReactions: StateFlow<List<com.newt.anime.data.models.EmojiReaction>> = _emojiReactions.asStateFlow()

    init {
        loadUserGroups()
    }
    
    fun createGroup(groupName: String) {
        if (groupName.isBlank()) {
            _uiState.value = _uiState.value.copy(error = "Group name cannot be empty")
            return
        }
        
        val currentUser = authRepository.currentUser
        if (currentUser == null) {
            _uiState.value = _uiState.value.copy(error = "User not authenticated")
            return
        }
        
        _uiState.value = _uiState.value.copy(isLoading = true, error = null)
        
        viewModelScope.launch {
            val userData = authRepository.getCurrentUserData()
            val displayName = userData?.displayName ?: currentUser.email ?: "Unknown"
            
            groupRepository.createGroup(groupName, currentUser.uid, displayName)
                .onSuccess { group ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        createdGroupCode = group.code
                    )
                    loadUserGroups()
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Failed to create group"
                    )
                }
        }
    }
    
    fun joinGroup(code: String) {
        if (code.isBlank()) {
            _uiState.value = _uiState.value.copy(error = "Group code cannot be empty")
            return
        }
        
        val currentUser = authRepository.currentUser
        if (currentUser == null) {
            _uiState.value = _uiState.value.copy(error = "User not authenticated")
            return
        }
        
        _uiState.value = _uiState.value.copy(isLoading = true, error = null)
        
        viewModelScope.launch {
            val userData = authRepository.getCurrentUserData()
            val displayName = userData?.displayName ?: currentUser.email ?: "Unknown"
            
            groupRepository.joinGroup(code.uppercase(), currentUser.uid, displayName)
                .onSuccess { group ->
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _currentGroup.value = group
                    loadUserGroups()
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Failed to join group"
                    )
                }
        }
    }
    
    fun selectGroup(group: Group) {
        try {
            android.util.Log.d("GroupViewModel", "Selecting group: ${group.name} (${group.id})")
            _currentGroup.value = group
            observeGroup(group.id)
        } catch (e: Exception) {
            android.util.Log.e("GroupViewModel", "Error selecting group: ${e.message}")
            _uiState.value = _uiState.value.copy(error = "خطأ في اختيار المجموعة: ${e.message}")
        }
    }
    
    private fun observeGroup(groupId: String) {
        try {
            android.util.Log.d("GroupViewModel", "Starting to observe group: $groupId")

            viewModelScope.launch {
                try {
                    groupRepository.observeGroup(groupId).collect { group ->
                        if (group != null) {
                            android.util.Log.d("GroupViewModel", "Group updated: ${group.name}")
                            _currentGroup.value = group
                        } else {
                            android.util.Log.w("GroupViewModel", "Received null group for ID: $groupId")
                        }
                    }
                } catch (e: Exception) {
                    android.util.Log.e("GroupViewModel", "Error observing group: ${e.message}")
                    _uiState.value = _uiState.value.copy(error = "خطأ في مراقبة المجموعة: ${e.message}")
                }
            }

            // مراقبة الدردشة
            viewModelScope.launch {
                try {
                    observeChatMessages(groupId)
                } catch (e: Exception) {
                    android.util.Log.e("GroupViewModel", "Error observing chat: ${e.message}")
                }
            }

            // مراقبة الإيموجي
            viewModelScope.launch {
                try {
                    observeEmojiReactions(groupId)
                } catch (e: Exception) {
                    android.util.Log.e("GroupViewModel", "Error observing emoji: ${e.message}")
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("GroupViewModel", "Error setting up group observation: ${e.message}")
            _uiState.value = _uiState.value.copy(error = "خطأ في إعداد مراقبة المجموعة: ${e.message}")
        }
    }

    private suspend fun observeChatMessages(groupId: String) {
        groupRepository.observeChatMessages(groupId).collect { messages ->
            _chatMessages.value = messages
        }
    }

    private suspend fun observeEmojiReactions(groupId: String) {
        groupRepository.observeEmojiReactions(groupId).collect { reactions ->
            _emojiReactions.value = reactions
        }
    }
    
    fun updateVideoSession(videoUrl: String, title: String, isPlaying: Boolean, position: Long = 0) {
        val group = _currentGroup.value ?: return
        val currentUser = authRepository.currentUser ?: return

        // Only group owner can control video
        if (group.ownerId != currentUser.uid) {
            _uiState.value = _uiState.value.copy(error = "Only group owner can control video")
            return
        }

        val syncCommand = try {
            when {
                isPlaying && (group.currentVideo?.isPlaying == false) -> "play"
                !isPlaying && (group.currentVideo?.isPlaying == true) -> "pause"
                position != (group.currentVideo?.currentPosition ?: 0L) -> "seek"
                else -> ""
            }
        } catch (e: Exception) {
            android.util.Log.e("GroupViewModel", "Error determining sync command: ${e.message}")
            ""
        }

        val videoSession = VideoSession(
            videoUrl = videoUrl,
            title = title,
            isPlaying = isPlaying,
            currentPosition = position,
            lastUpdated = System.currentTimeMillis(),
            syncCommand = syncCommand,
            syncTimestamp = System.currentTimeMillis(),
            ownerAction = true,
            hasStarted = try {
                isPlaying || (group.currentVideo?.hasStarted == true)
            } catch (e: Exception) {
                android.util.Log.e("GroupViewModel", "Error checking hasStarted: ${e.message}")
                false
            }
        )

        viewModelScope.launch {
            groupRepository.updateVideoSession(group.id, videoSession)
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        error = exception.message ?: "Failed to update video"
                    )
                }
        }
    }

    // دالة جديدة للتزامن السريع
    // ✅ GroupViewModel محسن - منع الإرسال الخاطئ
    fun syncVideoAction(action: String, position: Long = 0) {
        val group = _currentGroup.value ?: return
        val currentUser = authRepository.currentUser ?: return
        val currentVideo = group.currentVideo ?: run {
            android.util.Log.w("GroupViewModel", "No current video found for sync action: $action")
            return
        }

        if (group.ownerId != currentUser.uid) return

        // ✅ تحديد حالة التشغيل بدقة
        val isPlaying = when (action) {
            "play" -> true
            "pause" -> false
            "position_update" -> currentVideo.isPlaying // الاحتفاظ بالحالة الحالية
            "seek" -> currentVideo.isPlaying // الاحتفاظ بالحالة الحالية
            else -> currentVideo.isPlaying
        }

        val videoSession = VideoSession(
            videoUrl = currentVideo.videoUrl,
            title = currentVideo.title,
            isPlaying = isPlaying,
            currentPosition = position,
            lastUpdated = System.currentTimeMillis(),
            syncCommand = action,
            syncTimestamp = System.currentTimeMillis(),
            ownerAction = true,
            hasStarted = if (action == "play") true else (currentVideo.hasStarted == true)
        )

        viewModelScope.launch {
            when (action) {
                "play" -> android.util.Log.d("GroupViewModel", "▶️ OWNER PLAY: Sending PLAY command at $position")
                "pause" -> android.util.Log.d("GroupViewModel", "🛑 OWNER PAUSE: Sending PAUSE command at $position")
                "position_update" -> android.util.Log.d("GroupViewModel", "📍 POSITION UPDATE: $position (isPlaying: $isPlaying - NO STATE CHANGE)")
                else -> android.util.Log.d("GroupViewModel", "🔄 OWNER $action: at position $position")
            }

            val result = groupRepository.updateVideoSession(group.id, videoSession)
            if (result.isSuccess) {
                android.util.Log.d("GroupViewModel", "✅ SYNC SUCCESS: $action sent successfully")
            } else {
                android.util.Log.e("GroupViewModel", "❌ SYNC FAILED: $action", result.exceptionOrNull())
            }
        }
    }

    // حذف الفيديو من المجموعة
    fun removeVideoFromGroup() {
        val group = _currentGroup.value ?: return
        val currentUser = authRepository.currentUser ?: return
        val currentVideo = group.currentVideo ?: run {
            android.util.Log.w("GroupViewModel", "No current video to remove")
            return
        }

        // Only group owner can remove video
        if (group.ownerId != currentUser.uid) {
            _uiState.value = _uiState.value.copy(error = "Only group owner can remove video")
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)

            groupRepository.removeVideoFromGroup(group.id, currentVideo.videoUrl)
                .onSuccess {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        message = "Video removed successfully"
                    )
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Failed to remove video"
                    )
                }
        }
    }

    // حذف المجموعة بالكامل
    fun deleteGroup(onSuccess: () -> Unit) {
        val group = _currentGroup.value ?: return
        val currentUser = authRepository.currentUser ?: return

        // Only group owner can delete group
        if (group.ownerId != currentUser.uid) {
            _uiState.value = _uiState.value.copy(error = "Only group owner can delete group")
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)

            groupRepository.deleteGroup(group.id)
                .onSuccess {
                    // إزالة المجموعة من القائمة المحلية فوراً
                    removeGroupFromList(group.id)

                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        message = "Group deleted successfully"
                    )

                    // إعادة تحميل القائمة للتأكد
                    loadUserGroups()

                    onSuccess()
                }
                .onFailure { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "Failed to delete group"
                    )
                }
        }
    }
    
    private fun loadUserGroups() {
        val currentUser = authRepository.currentUser ?: return

        viewModelScope.launch {
            val groups = groupRepository.getUserGroups(currentUser.uid)
            _userGroups.value = groups
        }
    }

    // إعادة تحميل فورية للمجموعات
    fun refreshGroups() {
        loadUserGroups()
    }

    // حذف مجموعة من القائمة المحلية فوراً
    private fun removeGroupFromList(groupId: String) {
        val currentGroups = _userGroups.value
        val updatedGroups = currentGroups.filter { it.id != groupId }
        _userGroups.value = updatedGroups
        android.util.Log.d("GroupViewModel", "Group $groupId removed from local list. Remaining: ${updatedGroups.size}")
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun clearCreatedGroupCode() {
        _uiState.value = _uiState.value.copy(createdGroupCode = null)
    }

    // تحديث حالة الاتصال للمستخدم
    fun updateUserPresence(groupId: String, isOnline: Boolean) {
        val currentUser = authRepository.currentUser ?: return

        viewModelScope.launch {
            groupRepository.updateUserPresence(groupId, currentUser.uid, isOnline)
        }
    }

    // حظر عضو من المجموعة
    fun banMember(groupId: String, memberId: String) {
        viewModelScope.launch {
            val result = groupRepository.banMember(groupId, memberId)
            if (result.isFailure) {
                android.util.Log.e("GroupViewModel", "Error banning member", result.exceptionOrNull())
            } else {
                // إرسال رسالة إزالة للعضو المحظور
                groupRepository.sendRemovalNotification(groupId, memberId)
            }
        }
    }

    // إرسال رسالة دردشة
    fun sendChatMessage(groupId: String, message: String) {
        val currentUser = authRepository.currentUser ?: return

        viewModelScope.launch {
            // أخذ الاسم من بيانات المجموعة
            val currentGroup = _currentGroup.value
            val userName = currentGroup?.members?.get(currentUser.uid)?.name
                ?: currentUser.displayName
                ?: currentUser.email?.substringBefore("@")
                ?: "مستخدم"

            android.util.Log.d("GroupViewModel", "Sending message: '$message' from user: '$userName' (${currentUser.uid})")

            val result = groupRepository.sendChatMessage(
                groupId = groupId,
                message = message,
                userId = currentUser.uid,
                userName = userName
            )

            if (result.isFailure) {
                android.util.Log.e("GroupViewModel", "Error sending chat message", result.exceptionOrNull())
            } else {
                android.util.Log.d("GroupViewModel", "Message sent successfully")
            }
        }
    }

    // إرسال إيموجي
    fun sendEmojiReaction(groupId: String, emoji: String) {
        val currentUser = authRepository.currentUser ?: return

        viewModelScope.launch {
            // أخذ الاسم من بيانات المجموعة
            val currentGroup = _currentGroup.value
            val userName = currentGroup?.members?.get(currentUser.uid)?.name
                ?: currentUser.displayName
                ?: currentUser.email?.substringBefore("@")
                ?: "مستخدم"

            val result = groupRepository.sendEmojiReaction(
                groupId = groupId,
                emoji = emoji,
                userId = currentUser.uid,
                userName = userName
            )

            if (result.isFailure) {
                android.util.Log.e("GroupViewModel", "Error sending emoji reaction", result.exceptionOrNull())
            }
        }
    }

    // حذف الفيديو من المجموعة
    fun deleteVideo(groupId: String) {
        val currentUser = authRepository.currentUser ?: return
        val group = _currentGroup.value ?: return

        // التحقق من أن المستخدم هو مالك المجموعة
        if (group.ownerId != currentUser.uid) {
            _uiState.value = _uiState.value.copy(error = "فقط مالك المجموعة يمكنه حذف الفيديو")
            return
        }

        viewModelScope.launch {
            try {
                android.util.Log.d("GroupViewModel", "🗑️ Deleting video from group: $groupId")

                // حذف الفيديو من قاعدة البيانات
                val result = groupRepository.deleteVideo(groupId)

                if (result.isSuccess) {
                    android.util.Log.d("GroupViewModel", "✅ Video deleted successfully")
                    _uiState.value = _uiState.value.copy(message = "تم حذف الفيديو بنجاح")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "خطأ غير معروف"
                    android.util.Log.e("GroupViewModel", "❌ Failed to delete video: $error")
                    _uiState.value = _uiState.value.copy(error = "فشل في حذف الفيديو: $error")
                }
            } catch (e: Exception) {
                android.util.Log.e("GroupViewModel", "❌ Exception deleting video: ${e.message}")
                _uiState.value = _uiState.value.copy(error = "خطأ في حذف الفيديو: ${e.message}")
            }
        }
    }
}

data class GroupUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val createdGroupCode: String? = null
)
