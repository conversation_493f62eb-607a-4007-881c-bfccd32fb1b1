package com.newt.anime.data.repositories

import android.net.Uri
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.storage.FirebaseStorage
import com.google.firebase.storage.StorageReference
import kotlinx.coroutines.tasks.await
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VideoUploadRepository @Inject constructor(
    private val storage: FirebaseStorage,
    private val auth: FirebaseAuth
) {
    
    data class UploadProgress(
        val bytesTransferred: Long,
        val totalBytes: Long,
        val percentage: Int
    )
    
    data class UploadResult(
        val success: Boolean,
        val downloadUrl: String? = null,
        val error: String? = null
    )
    
    suspend fun uploadVideo(
        videoUri: Uri,
        onProgress: (UploadProgress) -> Unit
    ): UploadResult {
        return try {
            val userId = auth.currentUser?.uid ?: return UploadResult(
                success = false,
                error = "المستخدم غير مسجل الدخول"
            )
            
            // إنشاء مرجع فريد للفيديو
            val videoId = UUID.randomUUID().toString()
            val videoRef = storage.reference
                .child("videos")
                .child(userId)
                .child("$videoId.mp4")
            
            // رفع الفيديو مع تتبع التقدم
            val uploadTask = videoRef.putFile(videoUri)
            
            // تتبع التقدم
            uploadTask.addOnProgressListener { taskSnapshot ->
                val progress = UploadProgress(
                    bytesTransferred = taskSnapshot.bytesTransferred,
                    totalBytes = taskSnapshot.totalByteCount,
                    percentage = ((taskSnapshot.bytesTransferred * 100) / taskSnapshot.totalByteCount).toInt()
                )
                onProgress(progress)
            }
            
            // انتظار اكتمال الرفع
            uploadTask.await()
            
            // الحصول على رابط التحميل
            val downloadUrl = videoRef.downloadUrl.await().toString()
            
            UploadResult(
                success = true,
                downloadUrl = downloadUrl
            )
            
        } catch (e: Exception) {
            UploadResult(
                success = false,
                error = "فشل في رفع الفيديو: ${e.message}"
            )
        }
    }
    
    suspend fun deleteVideo(downloadUrl: String): Boolean {
        return try {
            val videoRef = storage.getReferenceFromUrl(downloadUrl)
            videoRef.delete().await()
            true
        } catch (e: Exception) {
            false
        }
    }
    
    fun getVideoReference(downloadUrl: String): StorageReference {
        return storage.getReferenceFromUrl(downloadUrl)
    }
}
