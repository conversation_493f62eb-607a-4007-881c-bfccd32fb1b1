{"logs": [{"outputFile": "com.newt.anime.app-mergeReleaseResources-62:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf7927868eeddad3b4319378a3201862\\transformed\\browser-1.4.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,379", "endColumns": "104,99,118,101", "endOffsets": "155,255,374,476"}, "to": {"startLines": "48,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "4200,8300,8400,8519", "endColumns": "104,99,118,101", "endOffsets": "4300,8395,8514,8616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5f82ddb128f9d8fc1f894d4feaff6b09\\transformed\\exoplayer-core-2.19.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,189,255,320,398,464,554,637", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "120,184,250,315,393,459,549,632,709"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6522,6592,6656,6722,6787,6865,6931,7021,7104", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "6587,6651,6717,6782,6860,6926,7016,7099,7176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6436519ada9dddee292e4f473475ac25\\transformed\\exoplayer-ui-2.19.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,471,643,726,810,887,978,1071,1144,1213,1309,1403,1467,1530,1595,1668,1774,1883,1988,2055,2137,2207,2278,2362,2447,2514,2577,2630,2688,2736,2797,2861,2923,2984,3050,3113,3172,3238,3302,3368,3420,3482,3558,3634", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,63,65,51,61,75,75,61", "endOffsets": "279,466,638,721,805,882,973,1066,1139,1208,1304,1398,1462,1525,1590,1663,1769,1878,1983,2050,2132,2202,2273,2357,2442,2509,2572,2625,2683,2731,2792,2856,2918,2979,3045,3108,3167,3233,3297,3363,3415,3477,3553,3629,3691"}, "to": {"startLines": "2,11,15,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,334,521,4588,4671,4755,4832,4923,5016,5089,5158,5254,5348,5412,5475,5540,5613,5719,5828,5933,6000,6082,6152,6223,6307,6392,6459,7181,7234,7292,7340,7401,7465,7527,7588,7654,7717,7776,7842,7906,7972,8024,8086,8162,8238", "endLines": "10,14,18,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,63,65,51,61,75,75,61", "endOffsets": "329,516,688,4666,4750,4827,4918,5011,5084,5153,5249,5343,5407,5470,5535,5608,5714,5823,5928,5995,6077,6147,6218,6302,6387,6454,6517,7229,7287,7335,7396,7460,7522,7583,7649,7712,7771,7837,7901,7967,8019,8081,8157,8233,8295"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9c9112f3b5828659e41d3d6d877dc27\\transformed\\credentials-1.2.0-rc01\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,118", "endOffsets": "165,284"}, "to": {"startLines": "19,20", "startColumns": "4,4", "startOffsets": "693,808", "endColumns": "114,118", "endOffsets": "803,922"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95a85adbd9a0258061c7c940036f2eec\\transformed\\ui-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,279,375,477,562,645,740,827,912,997,1083,1155,1232,1305,1378,1454,1520", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,76,72,72,75,65,119", "endOffsets": "190,274,370,472,557,640,735,822,907,992,1078,1150,1227,1300,1373,1449,1515,1635"}, "to": {"startLines": "28,29,49,50,51,106,107,165,166,167,168,169,170,171,172,174,175,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1670,1760,4305,4401,4503,8621,8704,15102,15189,15274,15359,15445,15517,15594,15667,15841,15917,15983", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,76,72,72,75,65,119", "endOffsets": "1755,1839,4396,4498,4583,8699,8794,15184,15269,15354,15440,15512,15589,15662,15735,15912,15978,16098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f341d0dad5e087546904e2a03fd632f3\\transformed\\play-services-base-18.1.0\\res\\values-ms\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,464,590,692,860,988,1104,1207,1388,1493,1664,1795,1962,2133,2196,2256", "endColumns": "101,168,125,101,167,127,115,102,180,104,170,130,166,170,62,59,78", "endOffsets": "294,463,589,691,859,987,1103,1206,1387,1492,1663,1794,1961,2132,2195,2255,2334"}, "to": {"startLines": "30,31,32,33,34,35,36,37,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1844,1950,2123,2253,2359,2531,2663,2783,3036,3221,3330,3505,3640,3811,3986,4053,4117", "endColumns": "105,172,129,105,171,131,119,106,184,108,174,134,170,174,66,63,82", "endOffsets": "1945,2118,2248,2354,2526,2658,2778,2885,3216,3325,3500,3635,3806,3981,4048,4112,4195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8f761d99cc674c08e5b049b2a7bbf06\\transformed\\core-1.16.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "21,22,23,24,25,26,27,173", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "927,1022,1124,1221,1331,1437,1555,15740", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "1017,1119,1216,1326,1432,1550,1665,15836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dc398bc18186089f1a6c4c36afb914b8\\transformed\\foundation-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,90", "endOffsets": "137,228"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "16103,16190", "endColumns": "86,90", "endOffsets": "16185,16276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\85c261ec03eee0a4101b5d8231a63563\\transformed\\material3-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,294,408,527,627,732,854,1004,1132,1280,1366,1466,1558,1656,1772,1898,2003,2141,2276,2408,2587,2712,2837,2965,3094,3187,3288,3409,3537,3638,3745,3851,3992,4138,4245,4344,4420,4518,4616,4718,4805,4894,4996,5076,5159,5258,5357,5454,5557,5644,5747,5846,5953,6075,6156,6262", "endColumns": "119,118,113,118,99,104,121,149,127,147,85,99,91,97,115,125,104,137,134,131,178,124,124,127,128,92,100,120,127,100,106,105,140,145,106,98,75,97,97,101,86,88,101,79,82,98,98,96,102,86,102,98,106,121,80,105,95", "endOffsets": "170,289,403,522,622,727,849,999,1127,1275,1361,1461,1553,1651,1767,1893,1998,2136,2271,2403,2582,2707,2832,2960,3089,3182,3283,3404,3532,3633,3740,3846,3987,4133,4240,4339,4415,4513,4611,4713,4800,4889,4991,5071,5154,5253,5352,5449,5552,5639,5742,5841,5948,6070,6151,6257,6353"}, "to": {"startLines": "108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8799,8919,9038,9152,9271,9371,9476,9598,9748,9876,10024,10110,10210,10302,10400,10516,10642,10747,10885,11020,11152,11331,11456,11581,11709,11838,11931,12032,12153,12281,12382,12489,12595,12736,12882,12989,13088,13164,13262,13360,13462,13549,13638,13740,13820,13903,14002,14101,14198,14301,14388,14491,14590,14697,14819,14900,15006", "endColumns": "119,118,113,118,99,104,121,149,127,147,85,99,91,97,115,125,104,137,134,131,178,124,124,127,128,92,100,120,127,100,106,105,140,145,106,98,75,97,97,101,86,88,101,79,82,98,98,96,102,86,102,98,106,121,80,105,95", "endOffsets": "8914,9033,9147,9266,9366,9471,9593,9743,9871,10019,10105,10205,10297,10395,10511,10637,10742,10880,11015,11147,11326,11451,11576,11704,11833,11926,12027,12148,12276,12377,12484,12590,12731,12877,12984,13083,13159,13257,13355,13457,13544,13633,13735,13815,13898,13997,14096,14193,14296,14383,14486,14585,14692,14814,14895,15001,15097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4366d762d4e78881da860b75d1c4c59f\\transformed\\play-services-basement-18.4.0\\res\\values-ms\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "38", "startColumns": "4", "startOffsets": "2890", "endColumns": "145", "endOffsets": "3031"}}]}]}