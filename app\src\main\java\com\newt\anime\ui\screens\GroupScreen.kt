package com.newt.anime.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.BorderStroke
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.newt.anime.data.models.Group
import com.newt.anime.data.models.Member
import com.newt.anime.data.models.VideoSession
import com.newt.anime.data.repository.AuthRepository
import com.newt.anime.ui.components.ExoVideoPlayer
import com.newt.anime.ui.components.VideoUploadDialog

import com.newt.anime.ui.viewmodel.GroupViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GroupScreen(
    group: Group,
    onNavigateBack: () -> Unit,
    groupViewModel: GroupViewModel = viewModel()
) {
    val authRepository = remember { AuthRepository() }
    val currentUser = authRepository.currentUser

    val currentGroup by groupViewModel.currentGroup.collectAsState()
    val groupUiState by groupViewModel.uiState.collectAsState()
    val chatMessages by groupViewModel.chatMessages.collectAsState()
    val emojiReactions by groupViewModel.emojiReactions.collectAsState()

    var videoUrl by remember { mutableStateOf("") }
    var videoTitle by remember { mutableStateOf("") }
    var showAddVideoDialog by remember { mutableStateOf(false) }
    // var showAddVideoDialog by remember { mutableStateOf(false) } // معطل مؤقت<|im_start|>
    var showDeleteGroupDialog by remember { mutableStateOf(false) }
    var showDeleteVideoDialog by remember { mutableStateOf(false) }
    var showGroupMenu by remember { mutableStateOf(false) }
    var isVideoFullscreen by remember { mutableStateOf(false) }
    var showViewersDialog by remember { mutableStateOf(false) }
    var showCopyMessage by remember { mutableStateOf(false) }
    var showBanConfirmDialog by remember { mutableStateOf(false) }
    var memberToBan by remember { mutableStateOf<Member?>(null) }

    // للنسخ
    val clipboardManager = LocalClipboardManager.current
    val context = LocalContext.current

    // Select this group when screen opens
    LaunchedEffect(group.id) {
        groupViewModel.selectGroup(group)
        // تحديث حالة الاتصال عند دخول المجموعة
        groupViewModel.updateUserPresence(group.id, true)
    }

    // مراقبة الطرد من المجموعة
    LaunchedEffect(Unit) {
        // التحقق من الطرد كل 5 ثواني
        while (true) {
            kotlinx.coroutines.delay(5000)
            val currentGroup = groupViewModel.currentGroup.value
            val currentUser = authRepository.currentUser
            if (currentGroup != null && currentUser != null) {
                if (!currentGroup.members.containsKey(currentUser.uid)) {
                    // تم طرد المستخدم
                    onNavigateBack()
                    break
                }
            }
        }
    }

    // تحديث حالة الاتصال عند الخروج + تنظيف الذاكرة
    DisposableEffect(group.id) {
        onDispose {
            try {
                groupViewModel.updateUserPresence(group.id, false)
                System.gc() // تنظيف الذاكرة
                android.util.Log.d("GroupScreen", "✅ Memory cleaned on dispose")
            } catch (e: Exception) {
                android.util.Log.e("GroupScreen", "Error in dispose: ${e.message}")
            }
        }
    }

    val displayGroup = currentGroup ?: group
    val isOwner = displayGroup.ownerId == authRepository.currentUser?.uid

    // ✅ إنشاء VideoSession آمن للمجموعات الجديدة والفارغة
    val safeVideoSession = remember(displayGroup.id, displayGroup.currentVideo) {
        try {
            val currentVideo = displayGroup.currentVideo
            if (currentVideo == null) {
                // مجموعة فارغة - إنشاء session افتراضي
                android.util.Log.d("GroupScreen", "Creating default VideoSession for empty group: ${displayGroup.id}")
                VideoSession(
                    videoUrl = "",
                    title = "لا يوجد فيديو",
                    isPlaying = false,
                    currentPosition = 0L,
                    lastUpdated = System.currentTimeMillis(),
                    syncCommand = "",
                    syncTimestamp = System.currentTimeMillis(),
                    ownerAction = false,
                    hasStarted = false
                )
            } else {
                // مجموعة بها فيديو
                android.util.Log.d("GroupScreen", "Using existing VideoSession: ${currentVideo.title}")
                currentVideo
            }
        } catch (e: Exception) {
            android.util.Log.e("GroupScreen", "Error creating safe video session: ${e.message}")
            // fallback آمن تماماً
            VideoSession(
                videoUrl = "",
                title = "خطأ في تحميل الفيديو",
                isPlaying = false,
                currentPosition = 0L,
                lastUpdated = System.currentTimeMillis(),
                syncCommand = "",
                syncTimestamp = System.currentTimeMillis(),
                ownerAction = false,
                hasStarted = false
            )
        }
    }

    // إذا كان الفيديو في وضع الشاشة الكاملة، اعرض فقط الفيديو
    if (isVideoFullscreen) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
        ) {
            ExoVideoPlayer(
                videoSession = safeVideoSession,
                isOwner = isOwner,
                onPlayPause = { isPlaying ->
                    try {
                        val currentVideo = displayGroup.currentVideo
                        if (currentVideo != null && currentVideo.videoUrl.isNotEmpty()) {
                            groupViewModel.updateVideoSession(
                                videoUrl = currentVideo.videoUrl,
                                title = currentVideo.title,
                                isPlaying = isPlaying,
                                position = currentVideo.currentPosition
                            )
                        } else {
                            android.util.Log.w("GroupScreen", "No video to play/pause - group is empty")
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("GroupScreen", "Error in onPlayPause: ${e.message}")
                    }
                },
                onSyncAction = { action, position ->
                    try {
                        if (displayGroup.currentVideo != null && displayGroup.currentVideo.videoUrl.isNotEmpty()) {
                            groupViewModel.syncVideoAction(action, position)
                        } else {
                            android.util.Log.w("GroupScreen", "Cannot sync action '$action' - no video in group")
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("GroupScreen", "Error in onSyncAction: ${e.message}")
                    }
                },
                viewersCount = displayGroup.getActiveViewersCount(),
                onViewersClick = { showViewersDialog = true },
                onSendMessage = { message ->
                    groupViewModel.sendChatMessage(group.id, message)
                },
                onSendEmoji = { emoji ->
                    groupViewModel.sendEmojiReaction(group.id, emoji)
                },
                chatMessages = chatMessages,
                emojiReactions = emojiReactions,
                currentUserId = authRepository.currentUser?.uid ?: "",
                modifier = Modifier.fillMaxSize()
            )

            // زر الخروج من الشاشة الكاملة
            Box(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(16.dp)
                    .background(
                        Color.Black.copy(alpha = 0.7f),
                        CircleShape
                    )
                    .clickable { isVideoFullscreen = false }
                    .padding(12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "خروج من الشاشة الكاملة",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        return
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top bar
        TopAppBar(
            title = { Text(displayGroup.name) },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "رجوع")
                }
            },
            actions = {
                // قائمة المالك
                if (isOwner) {
                    Box {
                        IconButton(onClick = { showGroupMenu = true }) {
                            Icon(Icons.Default.MoreVert, contentDescription = "خيارات المجموعة")
                        }

                        DropdownMenu(
                            expanded = showGroupMenu,
                            onDismissRequest = { showGroupMenu = false }
                        ) {
                            // حذف المجموعة (يشمل الفيديوهات تلقائياً)
                            DropdownMenuItem(
                                text = { Text("🗑️ حذف المجموعة") },
                                onClick = {
                                    showGroupMenu = false
                                    showDeleteGroupDialog = true
                                },
                                leadingIcon = {
                                    Icon(Icons.Default.Delete, contentDescription = null)
                                }
                            )
                        }
                    }
                }
            }
        )
        
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                // عرض مشغل الفيديو فقط إذا كان هناك فيديو أو للمجموعات الفارغة بشكل آمن
                if (displayGroup.currentVideo != null && displayGroup.currentVideo.videoUrl.isNotEmpty()) {
                    // مشغل الفيديو مع ExoPlayer - للمجموعات التي بها فيديو
                    ExoVideoPlayer(
                        videoSession = safeVideoSession,
                        isOwner = isOwner,
                        onPlayPause = { isPlaying ->
                            try {
                                val currentVideo = displayGroup.currentVideo
                                if (currentVideo != null && currentVideo.videoUrl.isNotEmpty()) {
                                    groupViewModel.updateVideoSession(
                                        videoUrl = currentVideo.videoUrl,
                                        title = currentVideo.title,
                                        isPlaying = isPlaying,
                                        position = currentVideo.currentPosition
                                    )
                                }
                            } catch (e: Exception) {
                                android.util.Log.e("GroupScreen", "Error in onPlayPause: ${e.message}")
                            }
                        },
                        onSyncAction = { action, position ->
                            try {
                                if (displayGroup.currentVideo != null && displayGroup.currentVideo.videoUrl.isNotEmpty()) {
                                    groupViewModel.syncVideoAction(action, position)
                                }
                            } catch (e: Exception) {
                                android.util.Log.e("GroupScreen", "Error in onSyncAction: ${e.message}")
                            }
                        },
                        onFullscreenToggle = { isVideoFullscreen = true },
                        viewersCount = displayGroup.getActiveViewersCount(),
                        onViewersClick = { showViewersDialog = true },
                        onSendMessage = { message ->
                            groupViewModel.sendChatMessage(group.id, message)
                        },
                        onSendEmoji = { emoji ->
                            groupViewModel.sendEmojiReaction(group.id, emoji)
                        },
                        chatMessages = chatMessages,
                        emojiReactions = emojiReactions,
                        currentUserId = authRepository.currentUser?.uid ?: "",
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(400.dp)
                    )
                } else {
                    // عرض بديل للمجموعات الفارغة - بدون ExoPlayer
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(400.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color.Black
                        )
                    ) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                Text(
                                    text = "📺",
                                    fontSize = 64.sp,
                                    color = Color.White.copy(alpha = 0.5f)
                                )
                                Text(
                                    text = "لا يوجد فيديو حالياً",
                                    color = Color.White.copy(alpha = 0.7f),
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Medium
                                )
                                if (isOwner) {
                                    Text(
                                        text = "اضغط 'رفع فيديو' لبدء المشاهدة",
                                        color = Color.White.copy(alpha = 0.5f),
                                        fontSize = 14.sp,
                                        textAlign = TextAlign.Center
                                    )
                                }
                            }
                        }
                    }
                }
            }

            // Owner controls - مبسطة مثل YouTube
            if (isOwner) {
                item {
                    // أزرار المالك (عند عدم وجود فيديو)
                    if (displayGroup.currentVideo == null || displayGroup.currentVideo.videoUrl.isEmpty()) {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            // زر رفع الفيديو (مفعل)
                            Button(
                                onClick = {
                                    showAddVideoDialog = true
                                },
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Icon(Icons.Default.Add, contentDescription = null)
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("📤 رفع فيديو جديد")
                            }


                        }
                    }
                }

                // عند وجود فيديو - عرض العنوان مع زر حذف للمالك
                if (displayGroup.currentVideo != null && displayGroup.currentVideo.videoUrl.isNotEmpty()) {
                    item {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.primaryContainer
                            )
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                Text(
                                    text = "🎬 ${displayGroup.currentVideo.title}",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                                    textAlign = TextAlign.Center
                                )

                                // زر حذف الفيديو - للمالك فقط
                                if (isOwner) {
                                    OutlinedButton(
                                        onClick = { showDeleteVideoDialog = true },
                                        colors = ButtonDefaults.outlinedButtonColors(
                                            contentColor = MaterialTheme.colorScheme.error
                                        ),
                                        border = BorderStroke(1.dp, MaterialTheme.colorScheme.error)
                                    ) {
                                        Icon(
                                            Icons.Default.Delete,
                                            contentDescription = null,
                                            modifier = Modifier.size(16.dp)
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text(
                                            text = "🗑️ حذف الفيديو",
                                            fontSize = 14.sp
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Group info
            item {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "معلومات المجموعة",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )

                        // كود المجموعة مع زر النسخ
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text("الكود: ${displayGroup.code}")
                            IconButton(
                                onClick = {
                                    clipboardManager.setText(AnnotatedString(displayGroup.code))
                                    showCopyMessage = true
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Share,
                                    contentDescription = "نسخ الكود",
                                    tint = MaterialTheme.colorScheme.primary
                                )
                            }
                        }

                        Text("المالك: ${displayGroup.ownerName}")
                        Text("عدد الأعضاء: ${displayGroup.members.size}")
                        Text("المتصلين النشطين: ${displayGroup.getActiveViewersCount()}",
                             color = MaterialTheme.colorScheme.primary)
                    }
                }
            }

            // Members list
            item {
                Text(
                    text = "الأعضاء",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
            }

            items(displayGroup.members.values.toList()) { member ->
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = if (member.uid == displayGroup.ownerId) Icons.Default.Star else Icons.Default.Person,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp),
                            tint = if (member.uid == displayGroup.ownerId) Color(0xFFFFD700) else MaterialTheme.colorScheme.onSurface
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = member.name,
                            modifier = Modifier.weight(1f)
                        )

                        if (member.uid == displayGroup.ownerId) {
                            Text(
                                text = "مالك",
                                color = MaterialTheme.colorScheme.primary,
                                fontSize = 12.sp
                            )
                        } else if (isOwner) {
                            // أزرار الحظر والإزالة للمالك فقط
                            Row {
                                IconButton(
                                    onClick = {
                                        memberToBan = member
                                        showBanConfirmDialog = true
                                    }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Delete,
                                        contentDescription = "حظر العضو",
                                        tint = Color.Red,
                                        modifier = Modifier.size(18.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    // Video Upload Dialog
    if (showAddVideoDialog) {
        VideoUploadDialog(
            groupId = group.id,
            onDismiss = { showAddVideoDialog = false },
            onVideoUploaded = { downloadUrl ->
                // الفيديو تم رفعه وإضافته للمجموعة تلقائياً
                showAddVideoDialog = false
            }
        )
    }



    // Ban Member Confirmation Dialog
    if (showBanConfirmDialog && memberToBan != null) {
        AlertDialog(
            onDismissRequest = {
                showBanConfirmDialog = false
                memberToBan = null
            },
            title = { Text("حظر العضو") },
            text = {
                Text("هل أنت متأكد من حظر ${memberToBan!!.name} من المجموعة؟ لن يتمكن من العودة مرة أخرى.")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        memberToBan?.let { member ->
                            groupViewModel.banMember(group.id, member.uid)
                        }
                        showBanConfirmDialog = false
                        memberToBan = null
                    }
                ) {
                    Text("حظر العضو", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showBanConfirmDialog = false
                        memberToBan = null
                    }
                ) {
                    Text("إلغاء")
                }
            }
        )
    }

    // Delete Group Confirmation Dialog
    if (showDeleteGroupDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteGroupDialog = false },
            title = { Text("حذف المجموعة") },
            text = {
                Text("هل أنت متأكد من حذف المجموعة بالكامل؟ سيتم حذف جميع الفيديوهات والأعضاء ولا يمكن التراجع عن هذا الإجراء.")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDeleteGroupDialog = false
                        groupViewModel.deleteGroup {
                            onNavigateBack() // العودة للشاشة الرئيسية بعد الحذف
                        }
                    }
                ) {
                    Text("حذف المجموعة", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteGroupDialog = false }) {
                    Text("إلغاء")
                }
            }
        )
    }

    // Viewers Dialog
    if (showViewersDialog) {
        AlertDialog(
            onDismissRequest = { showViewersDialog = false },
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "👁️",
                        fontSize = 20.sp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("المشاهدون النشطين (${displayGroup.getActiveViewersCount()})")
                }
            },
            text = {
                LazyColumn(
                    modifier = Modifier.heightIn(max = 300.dp)
                ) {
                    items(displayGroup.getActiveViewers()) { member ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = if (member.uid == displayGroup.ownerId) Icons.Default.Star else Icons.Default.Person,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp),
                                tint = if (member.uid == displayGroup.ownerId) Color(0xFFFFD700) else MaterialTheme.colorScheme.onSurface
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Column {
                                Text(
                                    text = member.name,
                                    fontWeight = FontWeight.Medium
                                )
                                Text(
                                    text = if (member.uid == displayGroup.ownerId) "مالك المجموعة" else "عضو",
                                    fontSize = 12.sp,
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                                )
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showViewersDialog = false }) {
                    Text("إغلاق")
                }
            }
        )
    }

    // Copy message
    if (showCopyMessage) {
        LaunchedEffect(showCopyMessage) {
            kotlinx.coroutines.delay(2000)
            showCopyMessage = false
        }

        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            contentAlignment = Alignment.BottomCenter
        ) {
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text(
                    text = "✅ تم نسخ الكود",
                    color = Color.White,
                    modifier = Modifier.padding(16.dp),
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }

    // Delete Video Dialog
    if (showDeleteVideoDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteVideoDialog = false },
            title = {
                Text(
                    text = "🗑️ حذف الفيديو",
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Column {
                    Text("هل أنت متأكد من حذف الفيديو الحالي؟")
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "• سيتم حذف الفيديو نهائياً\n• سيتمكن الأعضاء من رؤية أن الفيديو تم حذفه\n• يمكنك رفع فيديو جديد بعد الحذف",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        // حذف الفيديو من المجموعة
                        groupViewModel.deleteVideo(group.id)
                        showDeleteVideoDialog = false
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("🗑️ حذف")
                }
            },
            dismissButton = {
                OutlinedButton(
                    onClick = { showDeleteVideoDialog = false }
                ) {
                    Text("إلغاء")
                }
            }
        )
    }

    // Error display
    if (groupUiState.error != null) {
        LaunchedEffect(groupUiState.error) {
            // Show snackbar or handle error
        }
    }
}


