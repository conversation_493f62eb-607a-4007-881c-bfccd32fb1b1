rules_version = '2';

// قواعد Firebase Storage مبسطة ومصححة لتطبيق مشاهدة الفيديوهات الجماعية
service firebase.storage {
  match /b/{bucket}/o {
    
    // السماح بالقراءة العامة لجميع الملفات (مهم لعرض الفيديوهات)
    match /{allPaths=**} {
      // القراءة: مسموحة للجميع (بدون قيود)
      allow read: if true;
      
      // الكتابة: للمستخدمين المسجلين فقط
      allow write: if request.auth != null 
                   && isValidVideoFile();
      
      // الحذف: للمستخدمين المسجلين فقط
      allow delete: if request.auth != null;
    }
  }
}

// دالة مبسطة للتحقق من صحة ملف الفيديو
function isValidVideoFile() {
  return request.resource.size < 200 * 1024 * 1024  // أقل من 200 ميجابايت
         && (
           request.resource.contentType.matches('video/.*') ||  // أي نوع فيديو
           request.resource.contentType == 'video/mp4' ||       // MP4
           request.resource.contentType == 'video/webm' ||      // WebM
           request.resource.contentType == 'video/quicktime' || // MOV
           request.resource.contentType == 'application/octet-stream'  // للملفات بدون نوع محدد
         );
}
