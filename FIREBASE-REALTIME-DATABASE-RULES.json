{
  "rules": {
    // ==========================================
    // قواعد المستخدمين - Users Rules
    // ==========================================
    "users": {
      "$uid": {
        // المستخدم يمكنه قراءة وكتابة بياناته فقط
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid",
        
        // التحقق من صحة البيانات
        ".validate": "newData.hasChildren(['uid', 'email', 'displayName'])",
        
        "uid": {
          ".validate": "newData.isString() && newData.val() === auth.uid"
        },
        "email": {
          ".validate": "newData.isString() && newData.val().matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/)"
        },
        "displayName": {
          ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 50"
        },
        "createdAt": {
          ".validate": "newData.isNumber()"
        },
        "fcmToken": {
          ".validate": "newData.isString()"
        }
      }
    },

    // ==========================================
    // قواعد المجموعات - Groups Rules
    // ==========================================
    "groups": {
      // السماح بالقراءة والكتابة للمستخدمين المسجلين فقط
      ".read": "auth != null",
      ".write": "auth != null",
      
      // فهرسة للبحث السريع
      ".indexOn": ["code", "ownerId", "name", "createdAt"],
      
      "$groupId": {
        ".read": "auth != null",
        ".write": "auth != null",
        
        // التحقق من صحة بيانات المجموعة
        ".validate": "newData.exists() || (auth != null && (root.child('groups').child($groupId).child('ownerId').val() === auth.uid || !root.child('groups').child($groupId).exists()))",
        
        // معلومات المجموعة الأساسية
        "id": {
          ".validate": "newData.isString() && newData.val() === $groupId"
        },
        "name": {
          ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 50"
        },
        "code": {
          ".validate": "newData.isString() && newData.val().length === 6 && newData.val().matches(/^[A-Z0-9]{6}$/)"
        },
        "ownerId": {
          ".validate": "newData.isString() && (newData.val() === auth.uid || !newData.exists())"
        },
        "ownerName": {
          ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 50"
        },
        "createdAt": {
          ".validate": "newData.isNumber()"
        },

        // ==========================================
        // الأعضاء - Members
        // ==========================================
        "members": {
          ".read": "auth != null",
          ".write": "auth != null",
          
          "$memberId": {
            ".read": "auth != null",
            ".write": "auth != null",
            
            "uid": {
              ".validate": "newData.isString() && newData.val() === $memberId"
            },
            "name": {
              ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 50"
            },
            "joinedAt": {
              ".validate": "newData.isNumber()"
            }
          }
        },

        // ==========================================
        // جلسة الفيديو الحالية - Current Video Session
        // ==========================================
        "currentVideo": {
          ".read": "auth != null",
          ".write": "auth != null",
          ".indexOn": ["syncTimestamp", "lastUpdated", "ownerAction"],
          
          "videoUrl": {
            ".validate": "newData.isString()"
          },
          "title": {
            ".validate": "newData.isString() && newData.val().length <= 200"
          },
          "isPlaying": {
            ".validate": "newData.isBoolean()"
          },
          "currentPosition": {
            ".validate": "newData.isNumber() && newData.val() >= 0"
          },
          "lastUpdated": {
            ".validate": "newData.isNumber()"
          },
          "syncCommand": {
            ".validate": "newData.isString() && (newData.val() === 'play' || newData.val() === 'pause' || newData.val() === 'seek' || newData.val() === '')"
          },
          "syncTimestamp": {
            ".validate": "newData.isNumber()"
          },
          "ownerAction": {
            ".validate": "newData.isBoolean()"
          },
          "hasStarted": {
            ".validate": "newData.isBoolean()"
          }
        },

        // ==========================================
        // حالة الاتصال - Presence
        // ==========================================
        "presence": {
          ".read": "auth != null",
          ".write": "auth != null",
          
          "$userId": {
            ".read": "auth != null",
            ".write": "auth != null && $userId === auth.uid",
            ".validate": "newData.isNumber() || !newData.exists()"
          }
        },

        // ==========================================
        // الدردشة - Chat Messages
        // ==========================================
        "chat": {
          ".read": "auth != null",
          ".write": "auth != null",
          ".indexOn": ["timestamp"],
          
          "$messageId": {
            ".read": "auth != null",
            ".write": "auth != null",
            ".validate": "newData.hasChildren(['id', 'userId', 'userName', 'message', 'timestamp', 'type'])",
            
            "id": {
              ".validate": "newData.isString() && newData.val() === $messageId"
            },
            "userId": {
              ".validate": "newData.isString() && newData.val() === auth.uid"
            },
            "userName": {
              ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 50"
            },
            "message": {
              ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 500"
            },
            "timestamp": {
              ".validate": "newData.isNumber()"
            },
            "type": {
              ".validate": "newData.isString() && (newData.val() === 'TEXT' || newData.val() === 'EMOJI')"
            }
          }
        },

        // ==========================================
        // ردود الفعل بالإيموجي - Emoji Reactions
        // ==========================================
        "reactions": {
          ".read": "auth != null",
          ".write": "auth != null",
          ".indexOn": ["timestamp"],
          
          "$reactionId": {
            ".read": "auth != null",
            ".write": "auth != null",
            ".validate": "newData.hasChildren(['id', 'userId', 'userName', 'emoji', 'timestamp'])",
            
            "id": {
              ".validate": "newData.isString() && newData.val() === $reactionId"
            },
            "userId": {
              ".validate": "newData.isString() && newData.val() === auth.uid"
            },
            "userName": {
              ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 50"
            },
            "emoji": {
              ".validate": "newData.isString() && (newData.val() === '❤️' || newData.val() === '😂' || newData.val() === '😢' || newData.val() === '😡' || newData.val() === '😱' || newData.val() === '🛑')"
            },
            "timestamp": {
              ".validate": "newData.isNumber()"
            }
          }
        },

        // ==========================================
        // إعدادات المجموعة - Group Settings
        // ==========================================
        "settings": {
          ".read": "auth != null",
          ".write": "root.child('groups').child($groupId).child('ownerId').val() === auth.uid",
          
          "allowChat": {
            ".validate": "newData.isBoolean()"
          },
          "allowReactions": {
            ".validate": "newData.isBoolean()"
          },
          "maxMembers": {
            ".validate": "newData.isNumber() && newData.val() >= 2 && newData.val() <= 100"
          },
          "isPrivate": {
            ".validate": "newData.isBoolean()"
          }
        },

        // ==========================================
        // سجل الأنشطة - Activity Log
        // ==========================================
        "activityLog": {
          ".read": "auth != null",
          ".write": "auth != null",
          ".indexOn": ["timestamp"],
          
          "$activityId": {
            ".validate": "newData.hasChildren(['type', 'userId', 'timestamp'])",
            
            "type": {
              ".validate": "newData.isString() && (newData.val() === 'JOIN' || newData.val() === 'LEAVE' || newData.val() === 'VIDEO_CHANGE' || newData.val() === 'PLAY' || newData.val() === 'PAUSE')"
            },
            "userId": {
              ".validate": "newData.isString()"
            },
            "userName": {
              ".validate": "newData.isString()"
            },
            "timestamp": {
              ".validate": "newData.isNumber()"
            },
            "details": {
              ".validate": "newData.isString()"
            }
          }
        }
      }
    },

    // ==========================================
    // إحصائيات التطبيق - App Statistics
    // ==========================================
    "statistics": {
      ".read": "auth != null",
      ".write": false,
      
      "totalUsers": {
        ".validate": "newData.isNumber()"
      },
      "totalGroups": {
        ".validate": "newData.isNumber()"
      },
      "activeGroups": {
        ".validate": "newData.isNumber()"
      }
    }
  }
}
