/ Header Record For PersistentHashMapValueStorage1 0app/src/main/java/com/newt/anime/MainActivity.kt< ;app/src/main/java/com/newt/anime/data/model/VideoQuality.kt< ;app/src/main/java/com/newt/anime/data/models/ChatMessage.kt6 5app/src/main/java/com/newt/anime/data/models/Group.kt5 4app/src/main/java/com/newt/anime/data/models/User.ktL Kapp/src/main/java/com/newt/anime/data/repositories/VideoUploadRepository.ktC Bapp/src/main/java/com/newt/anime/data/repository/AuthRepository.ktD Capp/src/main/java/com/newt/anime/data/repository/GroupRepository.kt? >app/src/main/java/com/newt/anime/navigation/AnimeNavigation.kt> =app/src/main/java/com/newt/anime/ui/components/ChatOverlay.ktA @app/src/main/java/com/newt/anime/ui/components/ExoVideoPlayer.kt? >app/src/main/java/com/newt/anime/ui/components/GroupDialogs.ktA @app/src/main/java/com/newt/anime/ui/components/MessageOverlay.ktA @app/src/main/java/com/newt/anime/ui/components/NewVideoPlayer.kt> =app/src/main/java/com/newt/anime/ui/components/VideoPlayer.ktD Capp/src/main/java/com/newt/anime/ui/components/VideoUploadDialog.kt; :app/src/main/java/com/newt/anime/ui/screens/GroupScreen.kt: 9app/src/main/java/com/newt/anime/ui/screens/HomeScreen.kt; :app/src/main/java/com/newt/anime/ui/screens/LoginScreen.kt< ;app/src/main/java/com/newt/anime/ui/screens/SignUpScreen.kt3 2app/src/main/java/com/newt/anime/ui/theme/Color.kt3 2app/src/main/java/com/newt/anime/ui/theme/Theme.kt2 1app/src/main/java/com/newt/anime/ui/theme/Type.kt? >app/src/main/java/com/newt/anime/ui/viewmodel/AuthViewModel.kt@ ?app/src/main/java/com/newt/anime/ui/viewmodel/GroupViewModel.ktD Capp/src/main/java/com/newt/anime/ui/components/VideoUploadDialog.ktD Capp/src/main/java/com/newt/anime/data/repository/GroupRepository.kt; :app/src/main/java/com/newt/anime/ui/screens/GroupScreen.kt@ ?app/src/main/java/com/newt/anime/ui/viewmodel/GroupViewModel.kt6 5app/src/main/java/com/newt/anime/data/models/Group.kt= <app/src/main/java/com/newt/anime/data/models/Subscription.ktD Capp/src/main/java/com/newt/anime/data/repository/GroupRepository.ktK Japp/src/main/java/com/newt/anime/data/repository/SubscriptionRepository.kt? >app/src/main/java/com/newt/anime/navigation/AnimeNavigation.ktF Eapp/src/main/java/com/newt/anime/ui/components/GroupCreationDialog.kt: 9app/src/main/java/com/newt/anime/ui/screens/HomeScreen.ktB Aapp/src/main/java/com/newt/anime/ui/screens/SubscriptionScreen.kt@ ?app/src/main/java/com/newt/anime/ui/viewmodel/GroupViewModel.ktG Fapp/src/main/java/com/newt/anime/ui/viewmodel/SubscriptionViewModel.kt1 0app/src/main/java/com/newt/anime/MainActivity.kt; :app/src/main/java/com/newt/anime/ui/screens/GroupScreen.kt= <app/src/main/java/com/newt/anime/data/models/Subscription.ktK Japp/src/main/java/com/newt/anime/data/repository/SubscriptionRepository.ktF Eapp/src/main/java/com/newt/anime/ui/components/GroupCreationDialog.kt: 9app/src/main/java/com/newt/anime/ui/screens/HomeScreen.ktB Aapp/src/main/java/com/newt/anime/ui/screens/SubscriptionScreen.ktG Fapp/src/main/java/com/newt/anime/ui/viewmodel/SubscriptionViewModel.kt= <app/src/main/java/com/newt/anime/data/models/Subscription.ktK Japp/src/main/java/com/newt/anime/data/repository/SubscriptionRepository.kt? >app/src/main/java/com/newt/anime/navigation/AnimeNavigation.ktD Capp/src/main/java/com/newt/anime/ui/components/VideoUploadDialog.kt; :app/src/main/java/com/newt/anime/ui/screens/GroupScreen.kt: 9app/src/main/java/com/newt/anime/ui/screens/HomeScreen.ktG Fapp/src/main/java/com/newt/anime/ui/viewmodel/SubscriptionViewModel.ktD Capp/src/main/java/com/newt/anime/ui/components/VideoUploadDialog.kt: 9app/src/main/java/com/newt/anime/ui/screens/HomeScreen.kt