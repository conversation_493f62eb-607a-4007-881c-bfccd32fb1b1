{"logs": [{"outputFile": "com.newt.anime.app-mergeReleaseResources-62:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8f761d99cc674c08e5b049b2a7bbf06\\transformed\\core-1.16.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "21,22,23,24,25,26,27,173", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "949,1044,1146,1244,1343,1451,1556,15641", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "1039,1141,1239,1338,1446,1551,1672,15737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95a85adbd9a0258061c7c940036f2eec\\transformed\\ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,989,1079,1148,1222,1293,1363,1441,1508", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,73,70,69,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,984,1074,1143,1217,1288,1358,1436,1503,1623"}, "to": {"startLines": "28,29,49,50,51,106,107,165,166,167,168,169,170,171,172,174,175,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1677,1770,4164,4260,4359,8573,8649,15011,15100,15181,15267,15357,15426,15500,15571,15742,15820,15887", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,73,70,69,77,66,119", "endOffsets": "1765,1853,4255,4354,4442,8644,8732,15095,15176,15262,15352,15421,15495,15566,15636,15815,15882,16002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4366d762d4e78881da860b75d1c4c59f\\transformed\\play-services-basement-18.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "38", "startColumns": "4", "startOffsets": "2852", "endColumns": "147", "endOffsets": "2995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\85c261ec03eee0a4101b5d8231a63563\\transformed\\material3-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,327,434,566,682,778,891,1035,1159,1314,1399,1498,1588,1682,1796,1918,2022,2155,2282,2417,2589,2717,2835,2961,3081,3172,3270,3388,3527,3623,3731,3834,3967,4110,4216,4313,4393,4491,4583,4699,4783,4868,4969,5049,5134,5233,5333,5428,5528,5615,5719,5820,5924,6046,6126,6230", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "189,322,429,561,677,773,886,1030,1154,1309,1394,1493,1583,1677,1791,1913,2017,2150,2277,2412,2584,2712,2830,2956,3076,3167,3265,3383,3522,3618,3726,3829,3962,4105,4211,4308,4388,4486,4578,4694,4778,4863,4964,5044,5129,5228,5328,5423,5523,5610,5714,5815,5919,6041,6121,6225,6324"}, "to": {"startLines": "108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8737,8876,9009,9116,9248,9364,9460,9573,9717,9841,9996,10081,10180,10270,10364,10478,10600,10704,10837,10964,11099,11271,11399,11517,11643,11763,11854,11952,12070,12209,12305,12413,12516,12649,12792,12898,12995,13075,13173,13265,13381,13465,13550,13651,13731,13816,13915,14015,14110,14210,14297,14401,14502,14606,14728,14808,14912", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "8871,9004,9111,9243,9359,9455,9568,9712,9836,9991,10076,10175,10265,10359,10473,10595,10699,10832,10959,11094,11266,11394,11512,11638,11758,11849,11947,12065,12204,12300,12408,12511,12644,12787,12893,12990,13070,13168,13260,13376,13460,13545,13646,13726,13811,13910,14010,14105,14205,14292,14396,14497,14601,14723,14803,14907,15006"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9c9112f3b5828659e41d3d6d877dc27\\transformed\\credentials-1.2.0-rc01\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,112", "endOffsets": "162,275"}, "to": {"startLines": "19,20", "startColumns": "4,4", "startOffsets": "724,836", "endColumns": "111,112", "endOffsets": "831,944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f341d0dad5e087546904e2a03fd632f3\\transformed\\play-services-base-18.1.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "30,31,32,33,34,35,36,37,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1858,1965,2122,2249,2359,2500,2625,2748,3000,3148,3256,3418,3546,3700,3856,3922,3985", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "1960,2117,2244,2354,2495,2620,2743,2847,3143,3251,3413,3541,3695,3851,3917,3980,4059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dc398bc18186089f1a6c4c36afb914b8\\transformed\\foundation-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "16007,16097", "endColumns": "89,88", "endOffsets": "16092,16181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6436519ada9dddee292e4f473475ac25\\transformed\\exoplayer-ui-2.19.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,480,674,760,847,930,1018,1102,1170,1234,1332,1430,1495,1563,1629,1702,1822,1942,2060,2135,2217,2293,2361,2451,2542,2607,2671,2724,2784,2832,2893,2957,3028,3092,3157,3222,3281,3346,3410,3476,3528,3588,3671,3754", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,119,119,117,74,81,75,67,89,90,64,63,52,59,47,60,63,70,63,64,64,58,64,63,65,51,59,82,82,51", "endOffsets": "280,475,669,755,842,925,1013,1097,1165,1229,1327,1425,1490,1558,1624,1697,1817,1937,2055,2130,2212,2288,2356,2446,2537,2602,2666,2719,2779,2827,2888,2952,3023,3087,3152,3217,3276,3341,3405,3471,3523,3583,3666,3749,3801"}, "to": {"startLines": "2,11,15,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,530,4447,4533,4620,4703,4791,4875,4943,5007,5105,5203,5268,5336,5402,5475,5595,5715,5833,5908,5990,6066,6134,6224,6315,6380,7127,7180,7240,7288,7349,7413,7484,7548,7613,7678,7737,7802,7866,7932,7984,8044,8127,8210", "endLines": "10,14,18,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,119,119,117,74,81,75,67,89,90,64,63,52,59,47,60,63,70,63,64,64,58,64,63,65,51,59,82,82,51", "endOffsets": "330,525,719,4528,4615,4698,4786,4870,4938,5002,5100,5198,5263,5331,5397,5470,5590,5710,5828,5903,5985,6061,6129,6219,6310,6375,6439,7175,7235,7283,7344,7408,7479,7543,7608,7673,7732,7797,7861,7927,7979,8039,8122,8205,8257"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf7927868eeddad3b4319378a3201862\\transformed\\browser-1.4.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "48,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "4064,8262,8362,8475", "endColumns": "99,99,112,97", "endOffsets": "4159,8357,8470,8568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5f82ddb128f9d8fc1f894d4feaff6b09\\transformed\\exoplayer-core-2.19.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,255,330,411,485,579,665", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "123,186,250,325,406,480,574,660,733"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6444,6517,6580,6644,6719,6800,6874,6968,7054", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "6512,6575,6639,6714,6795,6869,6963,7049,7122"}}]}]}