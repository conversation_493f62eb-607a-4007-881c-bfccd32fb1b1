# 💎 دليل نظام الاشتراكات الشامل

## 📱 **anime-app-SUBSCRIPTION-SYSTEM-debug.apk**

### **✨ نظام اشتراكات متكامل مع ثلاث مستويات**

---

## 🎯 **المستويات المتاحة:**

### 1. **🆓 المستوى المجاني (FREE)**
- **السعر**: مجاني
- **الحد الأقصى**: 4 أعضاء لكل مجموعة
- **الميزات**:
  - مجموعة واحدة
  - حتى 4 أعضاء
  - مشاهدة الفيديوهات
  - الدردشة الأساسية

### 2. **⭐ المستوى المدفوع (PREMIUM)**
- **السعر**: $4.99 شهرياً
- **الحد الأقصى**: 10 أعضاء لكل مجموعة
- **الميزات**:
  - مجموعات غير محدودة
  - حتى 10 أعضاء لكل مجموعة
  - رفع فيديوهات أكبر (500MB)
  - دردشة متقدمة مع الإيموجي
  - إحصائيات المشاهدة
  - أولوية في الدعم

### 3. **🚀 المستوى غير المحدود (UNLIMITED)**
- **السعر**: $9.99 شهرياً
- **الحد الأقصى**: أعضاء غير محدودين
- **الميزات**:
  - مجموعات غير محدودة
  - أعضاء غير محدودين
  - رفع فيديوهات كبيرة (2GB)
  - جودة فيديو عالية
  - دردشة متقدمة مع ملفات
  - إحصائيات تفصيلية
  - دعم أولوية 24/7
  - ميزات حصرية

---

## 🔧 **كيفية عمل النظام:**

### **1. إنشاء المجموعات**
عند إنشاء مجموعة جديدة، يظهر حوار اختيار عدد الأعضاء:

```
┌─────────────────────────────────────┐
│        🎬 إنشاء مجموعة جديدة        │
├─────────────────────────────────────┤
│ اسم المجموعة: [_______________]    │
│                                     │
│ 👥 اختر الحد الأقصى للأعضاء:       │
│                                     │
│ ┌─ 4 أعضاء ────────────────────┐   │
│ │ مجاني ✅                      │   │
│ └───────────────────────────────┘   │
│                                     │
│ ┌─ 10 أعضاء ───────────────────┐   │
│ │ بريميوم - $4.99 🔒           │   │
│ └───────────────────────────────┘   │
│                                     │
│ ┌─ غير محدود ──────────────────┐   │
│ │ غير محدود - $9.99 🔒         │   │
│ └───────────────────────────────┘   │
│                                     │
│ [إلغاء]              [🚀 إنشاء]   │
└─────────────────────────────────────┘
```

### **2. التحقق من الحدود**
- **عند الانضمام**: يتم التحقق من عدد الأعضاء الحالي
- **رسالة الخطأ**: "المجموعة مكتملة (4/4)" إذا كانت ممتلئة
- **منع الانضمام**: تلقائياً إذا تم الوصول للحد الأقصى

### **3. عرض حالة المجموعة**
في قائمة المجموعات:
- **"4/4 أعضاء"** - مجموعة مكتملة (أحمر)
- **"2/10 أعضاء"** - مجموعة بريميوم (عادي)
- **"أعضاء غير محدودين"** - مجموعة غير محدودة

---

## 💳 **شاشة الاشتراكات:**

### **الوصول للشاشة**
- **زر النجمة الذهبية** ⭐ في الشاشة الرئيسية
- **عند محاولة إنشاء مجموعة بحد أعلى** من المسموح

### **محتويات الشاشة**
```
┌─────────────────────────────────────┐
│        💎 الاشتراكات               │
├─────────────────────────────────────┤
│ 📊 اشتراكك الحالي                  │
│ ⭐ مجاني                           │
│ ✅ غير محدود                       │
│ الحد الأقصى: 4 أعضاء               │
├─────────────────────────────────────┤
│ 🎯 اختر خطتك                       │
│                                     │
│ ┌─ 🆓 مجاني ──────────────────┐    │
│ │ مجاني                        │    │
│ │ ✅ مجموعة واحدة              │    │
│ │ ✅ حتى 4 أعضاء               │    │
│ │ [✅ مفعل حالياً]              │    │
│ └───────────────────────────────┘    │
│                                     │
│ ┌─ ⭐ بريميوم ─────────────────┐    │
│ │ $4.99 شهرياً                 │    │
│ │ ✅ مجموعات غير محدودة        │    │
│ │ ✅ حتى 10 أعضاء              │    │
│ │ ✅ رفع فيديوهات أكبر         │    │
│ │ [اشترك الآن]                 │    │
│ └───────────────────────────────┘    │
│                                     │
│ ┌─ 🚀 غير محدود ──────────────┐    │
│ │ $9.99 شهرياً                 │    │
│ │ ✅ أعضاء غير محدودين         │    │
│ │ ✅ رفع فيديوهات كبيرة        │    │
│ │ ✅ دعم أولوية 24/7           │    │
│ │ [اشترك الآن]                 │    │
│ └───────────────────────────────┘    │
└─────────────────────────────────────┘
```

---

## 🔒 **حوار ترقية الاشتراك:**

عند محاولة إنشاء مجموعة تتطلب ترقية:

```
┌─────────────────────────────────────┐
│        🔒 ترقية مطلوبة             │
├─────────────────────────────────────┤
│ لإنشاء مجموعة بهذا العدد من        │
│ الأعضاء، تحتاج إلى ترقية اشتراكك   │
│ إلى:                               │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 💎 بريميوم                     │ │
│ │ $4.99 شهرياً                   │ │
│ │ • حتى 10 أعضاء                 │ │
│ │ • مجموعات غير محدودة           │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [لاحقاً]              [🚀 ترقية الآن] │
└─────────────────────────────────────┘
```

---

## 🛠️ **التفاصيل التقنية:**

### **نماذج البيانات الجديدة:**

#### 1. **SubscriptionTier (enum)**
```kotlin
enum class SubscriptionTier(
    val maxMembers: Int,
    val priceUSD: Double,
    val features: List<String>
) {
    FREE(4, 0.0, listOf("مجموعة واحدة", "حتى 4 أعضاء")),
    PREMIUM(10, 4.99, listOf("مجموعات غير محدودة", "حتى 10 أعضاء")),
    UNLIMITED(Int.MAX_VALUE, 9.99, listOf("أعضاء غير محدودين"))
}
```

#### 2. **UserSubscription**
```kotlin
data class UserSubscription(
    val userId: String,
    val tier: SubscriptionTier,
    val isActive: Boolean,
    val endDate: Long
)
```

#### 3. **Group (محدث)**
```kotlin
data class Group(
    // ... الحقول الموجودة
    val maxMembers: Int = 4 // جديد
) {
    fun canAddNewMember(): Boolean = members.size < maxMembers
    fun isFull(): Boolean = members.size >= maxMembers
    fun getMembershipStatus(): String = "${members.size}/${maxMembers} أعضاء"
}
```

### **المكونات الجديدة:**

#### 1. **SubscriptionRepository**
- إدارة اشتراكات المستخدمين
- التحقق من الحدود
- ترقية الاشتراكات

#### 2. **SubscriptionViewModel**
- منطق الاشتراكات
- التحقق من الصلاحيات
- محاكاة الدفع

#### 3. **SubscriptionScreen**
- واجهة الاشتراكات
- عرض الخطط
- أزرار الترقية

#### 4. **GroupCreationDialog**
- اختيار عدد الأعضاء
- عرض الخيارات المتاحة
- حوار ترقية الاشتراك

---

## 📊 **سيناريوهات الاستخدام:**

### **1. مستخدم مجاني يريد إنشاء مجموعة كبيرة:**
1. يضغط "إنشاء مجموعة"
2. يختار "10 أعضاء" (مقفل 🔒)
3. يظهر حوار "ترقية مطلوبة"
4. يضغط "ترقية الآن"
5. ينتقل لشاشة الاشتراكات
6. يختار خطة بريميوم
7. يتم ترقية الاشتراك
8. يمكنه الآن إنشاء مجموعات بـ 10 أعضاء

### **2. مستخدم بريميوم ينشئ مجموعة:**
1. يضغط "إنشاء مجموعة"
2. يرى خيارات: 4 أعضاء ✅، 10 أعضاء ✅، غير محدود 🔒
3. يختار "10 أعضاء"
4. ينشئ المجموعة بنجاح

### **3. انضمام لمجموعة مكتملة:**
1. مستخدم يدخل كود مجموعة مكتملة (4/4)
2. يظهر خطأ: "المجموعة مكتملة (4/4)"
3. لا يمكن الانضمام

---

## 🎮 **كيفية الاختبار:**

### **1. اختبار المستوى المجاني:**
- أنشئ مجموعة بـ 4 أعضاء ✅
- حاول إنشاء مجموعة بـ 10 أعضاء ❌ (يطلب ترقية)

### **2. اختبار الترقية:**
- اضغط زر النجمة ⭐ في الشاشة الرئيسية
- اختر خطة بريميوم
- اضغط "اشترك الآن" (محاكاة)
- تحقق من تغيير الاشتراك

### **3. اختبار الحدود:**
- أنشئ مجموعة بـ 4 أعضاء
- ادع 4 أشخاص للانضمام
- حاول إضافة الخامس ❌ (يجب أن يفشل)

---

## 📁 **الملفات الجديدة:**

### **النماذج:**
- `Subscription.kt` - نماذج الاشتراكات
- `Group.kt` (محدث) - دعم maxMembers

### **المستودعات:**
- `SubscriptionRepository.kt` - إدارة الاشتراكات

### **ViewModels:**
- `SubscriptionViewModel.kt` - منطق الاشتراكات

### **الشاشات:**
- `SubscriptionScreen.kt` - واجهة الاشتراكات
- `GroupCreationDialog.kt` - حوار إنشاء المجموعة

### **التنقل:**
- `AnimeNavigation.kt` (محدث) - إضافة شاشة الاشتراكات

---

## 🎉 **النتيجة النهائية:**

✅ **نظام اشتراكات متكامل** مع 3 مستويات  
✅ **تحديد عدد الأعضاء** عند إنشاء المجموعة  
✅ **حوارات ترقية** عند الحاجة  
✅ **واجهة اشتراكات** جميلة ومفصلة  
✅ **تحقق تلقائي** من الحدود  
✅ **محاكاة دفع** للاختبار  
✅ **عرض حالة المجموعات** (مكتملة/متاحة)  

**التطبيق الآن يدعم نظام اشتراكات احترافي مثل YouTube Premium!** 🚀

## 📱 **ملف APK الجديد:**
**`anime-app-SUBSCRIPTION-SYSTEM-debug.apk`** - جاهز للتثبيت والاختبار!
