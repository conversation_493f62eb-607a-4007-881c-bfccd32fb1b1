package com.newt.anime.data.models

data class ChatMessage(
    val id: String = "",
    val userId: String = "",
    val userName: String = "",
    val message: String = "",
    val timestamp: Long = System.currentTimeMillis(),
    val type: MessageType = MessageType.TEXT
)

enum class MessageType {
    TEXT,
    EMOJI
}

data class EmojiReaction(
    val id: String = "",
    val userId: String = "",
    val userName: String = "",
    val emoji: String = "",
    val timestamp: Long = System.currentTimeMillis()
)

// الإيموجي المتاحة
object AvailableEmojis {
    const val HEART = "❤️"
    const val LAUGH = "😂"
    const val SAD = "😢"
    const val ANGRY = "😡"
    const val SHOCKED = "😱"
    const val STOP = "🛑"
    
    val ALL = listOf(HEART, LAUGH, SAD, ANGRY, SHOCKED, STOP)
}
