#Mon Jun 23 04:23:15 PDT 2025
path.4=15/classes.dex
path.3=14/classes.dex
path.2=13/classes.dex
renamed.9=classes10.dex
path.1=0/classes.dex
renamed.8=classes9.dex
path.8=9/classes.dex
path.7=5/classes.dex
path.6=4/classes.dex
path.5=3/classes.dex
renamed.10=classes11.dex
path.0=classes.dex
base.4=D\:\\kotlin aNIME\\anime\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\15\\classes.dex
base.3=D\:\\kotlin aNIME\\anime\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\14\\classes.dex
base.2=D\:\\kotlin aNIME\\anime\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\13\\classes.dex
base.1=D\:\\kotlin aNIME\\anime\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.0=D\:\\kotlin aNIME\\anime\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
renamed.3=classes4.dex
renamed.2=classes3.dex
renamed.1=classes2.dex
path.10=classes3.dex
renamed.0=classes.dex
base.9=D\:\\kotlin aNIME\\anime\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
path.9=classes2.dex
renamed.7=classes8.dex
base.8=D\:\\kotlin aNIME\\anime\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\9\\classes.dex
renamed.6=classes7.dex
base.7=D\:\\kotlin aNIME\\anime\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\5\\classes.dex
renamed.5=classes6.dex
base.6=D\:\\kotlin aNIME\\anime\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\4\\classes.dex
renamed.4=classes5.dex
base.5=D\:\\kotlin aNIME\\anime\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\3\\classes.dex
base.10=D\:\\kotlin aNIME\\anime\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes3.dex
