package com.newt.anime.data.repository

import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseError
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.database.ValueEventListener
import com.google.firebase.storage.FirebaseStorage
import com.newt.anime.data.models.Group
import com.newt.anime.data.models.Member
import com.newt.anime.data.models.VideoSession
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import kotlin.random.Random

class GroupRepository {
    private val database = FirebaseDatabase.getInstance()
    private val groupsRef = database.reference.child("groups")
    private val storage = FirebaseStorage.getInstance()
    
    suspend fun createGroup(
        groupName: String,
        ownerId: String,
        ownerName: String
    ): Result<Group> {
        return try {
            // التحقق من الاتصال بالإنترنت
            val groupId = groupsRef.push().key ?: throw Exception("فشل في إنشاء معرف المجموعة")
            val code = generateGroupCode()

            val group = Group(
                id = groupId,
                name = groupName,
                code = code,
                ownerId = ownerId,
                ownerName = ownerName,
                members = mapOf(ownerId to Member(ownerId, ownerName))
            )

            // محاولة حفظ البيانات
            groupsRef.child(groupId).setValue(group).await()
            Result.success(group)
        } catch (e: Exception) {
            val errorMessage = when {
                e.message?.contains("permission", ignoreCase = true) == true ->
                    "ليس لديك صلاحية للوصول لقاعدة البيانات"
                e.message?.contains("network", ignoreCase = true) == true ->
                    "تحقق من اتصال الإنترنت"
                e.message?.contains("auth", ignoreCase = true) == true ->
                    "يجب تسجيل الدخول أولاً"
                else -> "خطأ في إنشاء المجموعة: ${e.message}"
            }
            Result.failure(Exception(errorMessage))
        }
    }
    
    suspend fun joinGroup(code: String, userId: String, userName: String): Result<Group> {
        return try {
            // التحقق من صحة الكود
            if (code.isBlank() || code.length != 6) {
                return Result.failure(Exception("كود المجموعة يجب أن يكون 6 أحرف"))
            }

            // طريقة بديلة للبحث - جلب جميع المجموعات والبحث محلياً
            val snapshot = groupsRef.get().await()
            var foundGroup: Group? = null

            // البحث في جميع المجموعات
            for (groupSnapshot in snapshot.children) {
                val group = groupSnapshot.getValue(Group::class.java)
                if (group != null && group.code.equals(code.uppercase(), ignoreCase = true)) {
                    foundGroup = group
                    break
                }
            }

            if (foundGroup != null) {
                // التحقق من الحظر أولاً
                val bannedSnapshot = database.reference.child("groups").child(foundGroup.id).child("bannedMembers").child(userId).get().await()
                if (bannedSnapshot.exists()) {
                    return Result.failure(Exception("أنت محظور من هذه المجموعة"))
                }

                // التحقق من أن المستخدم ليس عضو بالفعل
                if (foundGroup.members.containsKey(userId)) {
                    return Result.failure(Exception("أنت عضو في هذه المجموعة بالفعل"))
                }

                // إضافة العضو الجديد
                val updatedMembers = foundGroup.members.toMutableMap()
                updatedMembers[userId] = Member(userId, userName)

                // تحديث قاعدة البيانات
                groupsRef.child(foundGroup.id).child("members").setValue(updatedMembers).await()

                val updatedGroup = foundGroup.copy(members = updatedMembers)
                Result.success(updatedGroup)
            } else {
                Result.failure(Exception("كود المجموعة غير صحيح"))
            }
        } catch (e: Exception) {
            val errorMessage = when {
                e.message?.contains("permission", ignoreCase = true) == true ->
                    "ليس لديك صلاحية للانضمام للمجموعة"
                e.message?.contains("network", ignoreCase = true) == true ->
                    "تحقق من اتصال الإنترنت"
                e.message?.contains("auth", ignoreCase = true) == true ->
                    "يجب تسجيل الدخول أولاً"
                e.message?.contains("index", ignoreCase = true) == true ->
                    "خطأ في إعدادات قاعدة البيانات - تحقق من إعدادات Firebase"
                else -> e.message ?: "خطأ في الانضمام للمجموعة"
            }
            Result.failure(Exception(errorMessage))
        }
    }
    
    fun observeGroup(groupId: String): Flow<Group?> = callbackFlow {
        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                try {
                    if (!snapshot.exists()) {
                        trySend(null)
                        return
                    }

                    // معالجة آمنة للبيانات
                    val group = snapshot.getValue(Group::class.java)
                    android.util.Log.d("GroupRepository", "Group data received: ${group?.name}")
                    trySend(group)
                } catch (e: Exception) {
                    android.util.Log.e("GroupRepository", "Error parsing group data: ${e.message}", e)
                    trySend(null)
                }
            }

            override fun onCancelled(error: DatabaseError) {
                android.util.Log.e("GroupRepository", "Group observation cancelled: ${error.message}")
                close(error.toException())
            }
        }

        groupsRef.child(groupId).addValueEventListener(listener)

        awaitClose {
            groupsRef.child(groupId).removeEventListener(listener)
        }
    }
    
    suspend fun updateVideoSession(groupId: String, videoSession: VideoSession): Result<Unit> {
        return try {
            // تحويل VideoSession إلى Map آمن
            val videoMap = mapOf(
                "videoUrl" to videoSession.videoUrl,
                "title" to videoSession.title,
                "isPlaying" to videoSession.isPlaying,
                "currentPosition" to videoSession.currentPosition,
                "lastUpdated" to videoSession.lastUpdated,
                "syncCommand" to videoSession.syncCommand,
                "syncTimestamp" to videoSession.syncTimestamp,
                "ownerAction" to videoSession.ownerAction,
                "hasStarted" to videoSession.hasStarted,

            )

            groupsRef.child(groupId).child("currentVideo").setValue(videoMap).await()
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("GroupRepository", "Error updating video session: ${e.message}", e)
            Result.failure(e)
        }
    }

    // حذف الفيديو من المجموعة مع حذفه من Storage
    suspend fun removeVideoFromGroup(groupId: String, videoUrl: String): Result<Unit> {
        return try {
            // حذف الفيديو من قاعدة البيانات
            groupsRef.child(groupId).child("currentVideo").removeValue().await()

            // حذف الفيديو من Storage إذا كان من Firebase Storage
            if (videoUrl.contains("firebasestorage.googleapis.com") || videoUrl.contains("appspot.com")) {
                try {
                    val videoRef = storage.getReferenceFromUrl(videoUrl)
                    videoRef.delete().await()
                    android.util.Log.d("GroupRepository", "Video deleted from Storage: $videoUrl")
                } catch (e: Exception) {
                    android.util.Log.w("GroupRepository", "Failed to delete video from Storage: ${e.message}")
                    // لا نفشل العملية إذا فشل حذف الملف من Storage
                }
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // حذف المجموعة بالكامل مع جميع الفيديوهات
    suspend fun deleteGroup(groupId: String): Result<Unit> {
        return try {
            // الحصول على بيانات المجموعة أولاً
            val groupSnapshot = groupsRef.child(groupId).get().await()
            val group = groupSnapshot.getValue(Group::class.java)

            // حذف الفيديو الحالي من Storage إذا وجد
            group?.currentVideo?.let { video ->
                if (video.videoUrl.isNotEmpty() &&
                    (video.videoUrl.contains("firebasestorage.googleapis.com") ||
                     video.videoUrl.contains("appspot.com"))) {
                    try {
                        val videoRef = storage.getReferenceFromUrl(video.videoUrl)
                        videoRef.delete().await()
                        android.util.Log.d("GroupRepository", "Group video deleted from Storage: ${video.videoUrl}")
                    } catch (e: Exception) {
                        android.util.Log.w("GroupRepository", "Failed to delete group video from Storage: ${e.message}")
                    }
                }
            }

            // حذف المجموعة من قاعدة البيانات
            groupsRef.child(groupId).removeValue().await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getUserGroups(userId: String): List<Group> {
        return try {
            android.util.Log.d("GroupRepository", "Loading groups for user: $userId")
            val snapshot = groupsRef.get().await()
            val groups = mutableListOf<Group>()

            for (groupSnapshot in snapshot.children) {
                val group = groupSnapshot.getValue(Group::class.java)
                if (group != null && group.members.containsKey(userId)) {
                    groups.add(group)
                    android.util.Log.d("GroupRepository", "Found group: ${group.name} (${group.id})")
                }
            }

            android.util.Log.d("GroupRepository", "Total groups found: ${groups.size}")
            groups
        } catch (e: Exception) {
            android.util.Log.e("GroupRepository", "Error loading groups: ${e.message}")
            emptyList()
        }
    }
    
    private fun generateGroupCode(): String {
        val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return (1..6)
            .map { chars[Random.nextInt(chars.length)] }
            .joinToString("")
    }

    // تحديث حالة الاتصال للمستخدم
    suspend fun updateUserPresence(groupId: String, userId: String, isOnline: Boolean) {
        try {
            val presenceRef = database.reference.child("groups").child(groupId).child("presence").child(userId)
            if (isOnline) {
                presenceRef.setValue(System.currentTimeMillis()).await()
            } else {
                presenceRef.removeValue().await()
            }
        } catch (e: Exception) {
            android.util.Log.e("GroupRepository", "Error updating user presence", e)
        }
    }

    // حظر عضو من المجموعة
    suspend fun banMember(groupId: String, memberId: String): Result<Unit> {
        return try {
            val updates = mapOf<String, Any?>(
                "groups/$groupId/members/$memberId" to null,
                "groups/$groupId/presence/$memberId" to null,
                "groups/$groupId/bannedMembers/$memberId" to System.currentTimeMillis()
            )
            database.reference.updateChildren(updates).await()
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("GroupRepository", "Error banning member", e)
            Result.failure(e)
        }
    }

    // إرسال إشعار الإزالة
    suspend fun sendRemovalNotification(groupId: String, memberId: String) {
        try {
            val notificationData = mapOf(
                "type" to "REMOVED",
                "message" to "تم إزالتك من المجموعة",
                "timestamp" to System.currentTimeMillis()
            )
            database.reference.child("users").child(memberId).child("notifications").push().setValue(notificationData).await()
        } catch (e: Exception) {
            android.util.Log.e("GroupRepository", "Error sending removal notification", e)
        }
    }

    // إرسال رسالة دردشة
    suspend fun sendChatMessage(groupId: String, message: String, userId: String, userName: String): Result<Unit> {
        return try {
            val messageId = database.reference.child("groups").child(groupId).child("chat").push().key ?: return Result.failure(Exception("Failed to generate message ID"))
            val chatMessage = mapOf(
                "id" to messageId,
                "userId" to userId,
                "userName" to userName,
                "message" to message,
                "timestamp" to System.currentTimeMillis(),
                "type" to "TEXT"
            )
            database.reference.child("groups").child(groupId).child("chat").child(messageId).setValue(chatMessage).await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // إرسال إيموجي
    suspend fun sendEmojiReaction(groupId: String, emoji: String, userId: String, userName: String): Result<Unit> {
        return try {
            val reactionId = database.reference.child("groups").child(groupId).child("reactions").push().key ?: return Result.failure(Exception("Failed to generate reaction ID"))
            val emojiReaction = mapOf(
                "id" to reactionId,
                "userId" to userId,
                "userName" to userName,
                "emoji" to emoji,
                "timestamp" to System.currentTimeMillis()
            )
            database.reference.child("groups").child(groupId).child("reactions").child(reactionId).setValue(emojiReaction).await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // حذف الفيديو من المجموعة - دالة مبسطة
    suspend fun deleteVideo(groupId: String): Result<Unit> {
        return try {
            android.util.Log.d("GroupRepository", "🗑️ Deleting video from group: $groupId")

            // الحصول على بيانات الفيديو الحالي أولاً
            val groupSnapshot = groupsRef.child(groupId).get().await()
            val group = groupSnapshot.getValue(Group::class.java)
            val currentVideo = group?.currentVideo

            if (currentVideo != null && currentVideo.videoUrl.isNotEmpty()) {
                // حذف الفيديو باستخدام الدالة الموجودة
                removeVideoFromGroup(groupId, currentVideo.videoUrl)
            } else {
                // حذف currentVideo فقط من قاعدة البيانات
                groupsRef.child(groupId).child("currentVideo").removeValue().await()
            }

            android.util.Log.d("GroupRepository", "✅ Video deleted successfully from group: $groupId")
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("GroupRepository", "❌ Error deleting video: ${e.message}")
            Result.failure(e)
        }
    }

    // مراقبة رسائل الدردشة
    fun observeChatMessages(groupId: String): Flow<List<com.newt.anime.data.models.ChatMessage>> = callbackFlow {
        val chatRef = database.reference.child("groups").child(groupId).child("chat")
            .orderByChild("timestamp")
            .limitToLast(50) // آخر 50 رسالة

        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val messages = mutableListOf<com.newt.anime.data.models.ChatMessage>()
                for (messageSnapshot in snapshot.children) {
                    try {
                        val messageData = messageSnapshot.value as? Map<String, Any> ?: continue
                        val message = com.newt.anime.data.models.ChatMessage(
                            id = messageData["id"] as? String ?: "",
                            userId = messageData["userId"] as? String ?: "",
                            userName = messageData["userName"] as? String ?: "",
                            message = messageData["message"] as? String ?: "",
                            timestamp = (messageData["timestamp"] as? Number)?.toLong() ?: 0L,
                            type = com.newt.anime.data.models.MessageType.TEXT
                        )
                        android.util.Log.d("GroupRepository", "Received message: '${message.message}' from '${message.userName}' (${message.userId})")
                        messages.add(message)
                    } catch (e: Exception) {
                        android.util.Log.e("GroupRepository", "Error parsing chat message", e)
                    }
                }
                trySend(messages.sortedBy { it.timestamp })
            }

            override fun onCancelled(error: DatabaseError) {
                android.util.Log.e("GroupRepository", "Error observing chat messages", error.toException())
            }
        }

        chatRef.addValueEventListener(listener)
        awaitClose { chatRef.removeEventListener(listener) }
    }

    // مراقبة الإيموجي
    fun observeEmojiReactions(groupId: String): Flow<List<com.newt.anime.data.models.EmojiReaction>> = callbackFlow {
        val reactionsRef = database.reference.child("groups").child(groupId).child("reactions")
            .orderByChild("timestamp")
            .limitToLast(20) // آخر 20 إيموجي

        val listener = object : ValueEventListener {
            override fun onDataChange(snapshot: DataSnapshot) {
                val reactions = mutableListOf<com.newt.anime.data.models.EmojiReaction>()
                for (reactionSnapshot in snapshot.children) {
                    try {
                        val reactionData = reactionSnapshot.value as? Map<String, Any> ?: continue
                        val reaction = com.newt.anime.data.models.EmojiReaction(
                            id = reactionData["id"] as? String ?: "",
                            userId = reactionData["userId"] as? String ?: "",
                            userName = reactionData["userName"] as? String ?: "",
                            emoji = reactionData["emoji"] as? String ?: "",
                            timestamp = (reactionData["timestamp"] as? Number)?.toLong() ?: 0L
                        )
                        reactions.add(reaction)
                    } catch (e: Exception) {
                        android.util.Log.e("GroupRepository", "Error parsing emoji reaction", e)
                    }
                }
                trySend(reactions.sortedBy { it.timestamp })
            }

            override fun onCancelled(error: DatabaseError) {
                android.util.Log.e("GroupRepository", "Error observing emoji reactions", error.toException())
            }
        }

        reactionsRef.addValueEventListener(listener)
        awaitClose { reactionsRef.removeEventListener(listener) }
    }
}
