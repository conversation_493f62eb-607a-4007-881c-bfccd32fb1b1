{"logs": [{"outputFile": "com.newt.anime.app-mergeDebugResources-66:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f366428297d6790f37bf7bc7eaa62251\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "315,336", "startColumns": "4,4", "startOffsets": "18497,19598", "endColumns": "41,59", "endOffsets": "18534,19653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5f82ddb128f9d8fc1f894d4feaff6b09\\transformed\\exoplayer-core-2.19.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "416,417,418,419,420,421,422,423,424", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "26633,26703,26765,26830,26894,26971,27036,27126,27210", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "26698,26760,26825,26889,26966,27031,27121,27205,27274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4366d762d4e78881da860b75d1c4c59f\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "343,375", "startColumns": "4,4", "startOffsets": "20031,22771", "endColumns": "67,166", "endOffsets": "20094,22933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1713a2ee3140680f0f03dc575dc0d584\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "732", "startColumns": "4", "startOffsets": "46494", "endLines": "735", "endColumns": "12", "endOffsets": "46712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4dd815db6310af649afb2ff952f21303\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "335", "startColumns": "4", "startOffsets": "19555", "endColumns": "42", "endOffsets": "19593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8f761d99cc674c08e5b049b2a7bbf06\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "32,80,81,87,88,105,106,118,119,120,121,122,123,124,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,225,226,227,310,311,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,345,358,359,360,361,362,363,364,524,715,716,721,724,729,744,745,756,762,788,823,844,877", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1300,3472,3544,3923,3988,5212,5281,5993,6063,6131,6203,6273,6334,6408,8946,9007,9068,9130,9194,9256,9317,9385,9485,9545,9611,9684,9753,9810,9862,13764,13836,13912,18245,18280,18539,18594,18657,18712,18770,18826,18884,18945,19008,19065,19116,19174,19224,19285,19342,19408,19442,19477,20172,21101,21168,21240,21309,21378,21452,21524,34462,45272,45389,45656,45949,46216,47127,47199,47678,47881,48758,50564,51245,51927", "endLines": "32,80,81,87,88,105,106,118,119,120,121,122,123,124,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,225,226,227,310,311,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,345,358,359,360,361,362,363,364,524,715,719,721,727,729,744,745,761,771,822,843,876,882", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1355,3539,3627,3983,4049,5276,5339,6058,6126,6198,6268,6329,6403,6476,9002,9063,9125,9189,9251,9312,9380,9480,9540,9606,9679,9748,9805,9857,9919,13831,13907,13972,18275,18310,18589,18652,18707,18765,18821,18879,18940,19003,19060,19111,19169,19219,19280,19337,19403,19437,19472,19507,20237,21163,21235,21304,21373,21447,21519,21607,34528,45384,45585,45761,46145,46340,47194,47261,47876,48177,50559,51240,51922,52089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95a85adbd9a0258061c7c940036f2eec\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,306,307,339,365,366,386,387,389,452,453,515,516,518,519,520,521,522,523,525,526,527,530,708,711", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13977,14036,14095,14155,14215,14275,14335,14395,14455,14515,14575,14635,14695,14754,14814,14874,14934,14994,15054,15114,15174,15234,15294,15354,15413,15473,15533,15592,15651,15710,15769,15828,15887,15961,16019,18019,18070,19762,21612,21677,24132,24198,24443,29445,29497,33998,34060,34184,34234,34288,34334,34380,34422,34533,34580,34616,34817,44966,45077", "endLines": "228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,306,307,339,365,366,386,387,389,452,453,515,516,518,519,520,521,522,523,525,526,527,532,710,714", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "14031,14090,14150,14210,14270,14330,14390,14450,14510,14570,14630,14690,14749,14809,14869,14929,14989,15049,15109,15169,15229,15289,15349,15408,15468,15528,15587,15646,15705,15764,15823,15882,15956,16014,16069,18065,18120,19810,21672,21726,24193,24294,24496,29492,29552,34055,34109,34229,34283,34329,34375,34417,34457,34575,34611,34701,34924,45072,45267"}}, {"source": "D:\\kotlin aNIME\\anime\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "82", "endOffsets": "134"}, "to": {"startLines": "731", "startColumns": "4", "startOffsets": "46412", "endColumns": "81", "endOffsets": "46489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6436519ada9dddee292e4f473475ac25\\transformed\\exoplayer-ui-2.19.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,35,40,47,48,49,50,51,52,57,58,59,60,61,62,63,64,65,66,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,213,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,266,270,274,278,282,286,290,294,295,301,312,316,320,324,328,332,336,340,344,348,352,356,367,372,377,382,393,401,411,415,419,423,426,442,468,503,532", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1140,1190,1255,1312,1359,1414,1562,1800,1849,1910,1970,2026,2086,2256,2316,2369,2426,2481,2537,2594,2643,2694,2753,3040,3105,3163,3212,3260,3311,3368,3425,3487,3554,3625,3697,3741,3798,3854,3917,3990,4060,4119,4176,4223,4278,4323,4372,4427,4481,4531,4582,4636,4695,4745,4803,4859,4912,4975,5040,5103,5155,5215,5279,5345,5403,5475,5536,5606,5676,5741,5806,5877,5965,6063,6159,6233,6309,6383,6465,6551,6637,6723,6801,6889,6975,7045,7137,7215,7295,7373,7459,7541,7634,7712,7803,7884,7973,8076,8177,8261,8357,8454,8549,8642,8734,8827,8920,9013,9096,9183,9278,9371,9452,9547,9640,9717,9761,9802,9847,9895,9939,9982,10031,10078,10122,10178,10231,10273,10320,10368,10428,10466,10516,10560,10610,10662,10700,10747,10794,10835,10874,10912,10956,11004,11046,11084,11126,11180,11227,11264,11313,11355,11396,11437,11479,11522,11560,11596,11674,11752,12049,12319,12401,12483,12625,12703,12790,12875,12942,13005,13097,13189,13254,13317,13379,13450,13560,13671,13781,13848,13928,13999,14066,14151,14236,14299,14387,14451,14593,14693,14741,14884,14947,15009,15074,15145,15203,15261,15327,15391,15457,15509,15571,15647,15723,15777,16056,16280,16483,16689,16892,17107,17316,17513,17551,17905,18692,18933,19173,19430,19683,19936,20171,20418,20657,20901,21122,21317,21892,22183,22479,22782,23351,23885,24359,24570,24770,24946,25054,25630,26569,27745,28801", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,34,39,46,47,48,49,50,51,56,57,58,59,60,61,62,63,64,65,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,212,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,265,269,273,277,281,285,289,293,294,300,311,315,319,323,327,331,335,339,343,347,351,355,366,371,376,381,392,400,410,414,418,422,425,441,467,502,531,571", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,87,97,95,73,75,73,81,85,85,85,77,87,85,69,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1135,1185,1250,1307,1354,1409,1557,1795,1844,1905,1965,2021,2081,2251,2311,2364,2421,2476,2532,2589,2638,2689,2748,3035,3100,3158,3207,3255,3306,3363,3420,3482,3549,3620,3692,3736,3793,3849,3912,3985,4055,4114,4171,4218,4273,4318,4367,4422,4476,4526,4577,4631,4690,4740,4798,4854,4907,4970,5035,5098,5150,5210,5274,5340,5398,5470,5531,5601,5671,5736,5801,5872,5960,6058,6154,6228,6304,6378,6460,6546,6632,6718,6796,6884,6970,7040,7132,7210,7290,7368,7454,7536,7629,7707,7798,7879,7968,8071,8172,8256,8352,8449,8544,8637,8729,8822,8915,9008,9091,9178,9273,9366,9447,9542,9635,9712,9756,9797,9842,9890,9934,9977,10026,10073,10117,10173,10226,10268,10315,10363,10423,10461,10511,10555,10605,10657,10695,10742,10789,10830,10869,10907,10951,10999,11041,11079,11121,11175,11222,11259,11308,11350,11391,11432,11474,11517,11555,11591,11669,11747,12044,12314,12396,12478,12620,12698,12785,12870,12937,13000,13092,13184,13249,13312,13374,13445,13555,13666,13776,13843,13923,13994,14061,14146,14231,14294,14382,14446,14588,14688,14736,14879,14942,15004,15069,15140,15198,15256,15322,15386,15452,15504,15566,15642,15718,15772,16051,16275,16478,16684,16887,17102,17311,17508,17546,17900,18687,18928,19168,19425,19678,19931,20166,20413,20652,20896,21117,21312,21887,22178,22474,22777,23346,23880,24354,24565,24765,24941,25049,25625,26564,27740,28796,30160"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,33,34,35,37,42,49,50,51,52,53,54,59,60,61,62,63,64,65,66,67,68,75,76,77,78,79,97,98,99,100,101,102,103,104,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,341,342,346,350,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,546,551,555,559,563,567,571,575,579,580,586,597,601,605,609,613,617,621,625,629,633,637,641,652,657,662,667,678,686,696,700,704,753,772,935,961,1036,1065", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1133,1185,1235,1360,1417,1464,1575,1723,1961,2010,2071,2131,2187,2247,2417,2477,2530,2587,2642,2698,2755,2804,2855,2914,3201,3266,3324,3373,3421,4725,4782,4839,4901,4968,5039,5111,5155,6481,6537,6600,6673,6743,6802,6859,6906,6961,7006,7055,7110,7164,7214,7265,7319,7378,7428,7486,7542,7595,7658,7723,7786,7838,7898,7962,8028,8086,8158,8219,8289,8359,8424,8489,9924,10012,10110,10206,10280,10356,10430,10512,10598,10684,10770,10848,10936,11022,11092,11184,11262,11342,11420,11506,11588,11681,11759,11850,11931,12020,12123,12224,12308,12404,12501,12596,12689,12781,12874,12967,13060,13143,13230,13325,13418,13499,13594,13687,16074,16118,16159,16204,16252,16296,16339,16388,16435,16479,16535,16588,16630,16677,16725,16785,16823,16873,16917,16967,17019,17057,17104,17151,17192,17231,17269,17313,17361,17403,17441,17483,17537,17584,17621,17670,17712,17753,17794,17836,17879,17917,19875,19953,20242,20539,24501,24583,24665,24807,24885,24972,25057,25124,25187,25279,25371,25436,25499,25561,25632,25742,25853,25963,26030,26110,26181,26248,26333,26418,26481,26569,27279,27421,27521,27569,27712,27775,27837,27902,27973,28031,28089,28155,28219,28285,28337,28399,28475,28551,35797,36076,36300,36503,36709,36912,37127,37336,37533,37571,37925,38712,38953,39193,39450,39703,39956,40191,40438,40677,40921,41142,41337,41912,42203,42499,42802,43371,43905,44379,44590,44790,47570,48182,54084,55023,57456,58512", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,33,34,35,41,48,49,50,51,52,53,58,59,60,61,62,63,64,65,66,67,74,75,76,77,78,79,97,98,99,100,101,102,103,104,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,341,342,349,353,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,550,554,558,562,566,570,574,578,579,585,596,600,604,608,612,616,620,624,628,632,636,640,651,656,661,666,677,685,695,699,703,707,755,787,960,995,1064,1104", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,87,97,95,73,75,73,81,85,85,85,77,87,85,69,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1180,1230,1295,1412,1459,1514,1718,1956,2005,2066,2126,2182,2242,2412,2472,2525,2582,2637,2693,2750,2799,2850,2909,3196,3261,3319,3368,3416,3467,4777,4834,4896,4963,5034,5106,5150,5207,6532,6595,6668,6738,6797,6854,6901,6956,7001,7050,7105,7159,7209,7260,7314,7373,7423,7481,7537,7590,7653,7718,7781,7833,7893,7957,8023,8081,8153,8214,8284,8354,8419,8484,8555,10007,10105,10201,10275,10351,10425,10507,10593,10679,10765,10843,10931,11017,11087,11179,11257,11337,11415,11501,11583,11676,11754,11845,11926,12015,12118,12219,12303,12399,12496,12591,12684,12776,12869,12962,13055,13138,13225,13320,13413,13494,13589,13682,13759,16113,16154,16199,16247,16291,16334,16383,16430,16474,16530,16583,16625,16672,16720,16780,16818,16868,16912,16962,17014,17052,17099,17146,17187,17226,17264,17308,17356,17398,17436,17478,17532,17579,17616,17665,17707,17748,17789,17831,17874,17912,17948,19948,20026,20534,20804,24578,24660,24802,24880,24967,25052,25119,25182,25274,25366,25431,25494,25556,25627,25737,25848,25958,26025,26105,26176,26243,26328,26413,26476,26564,26628,27416,27516,27564,27707,27770,27832,27897,27968,28026,28084,28150,28214,28280,28332,28394,28470,28546,28600,36071,36295,36498,36704,36907,37122,37331,37528,37566,37920,38707,38948,39188,39445,39698,39951,40186,40433,40672,40916,41137,41332,41907,42198,42494,42797,43366,43900,44374,44585,44785,44961,47673,48753,55018,56194,58507,59871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\be1c57ad0dd6c538fab9e27928b0a2a5\\transformed\\navigation-runtime-2.8.5\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "313,746,924,927", "startColumns": "4,4,4,4", "startOffsets": "18377,47266,53672,53787", "endLines": "313,752,926,929", "endColumns": "52,24,24,24", "endOffsets": "18425,47565,53782,53897"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf7927868eeddad3b4319378a3201862\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "83,84,85,86,116,117,385,443,444,445", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3674,3732,3798,3861,5850,5921,24064,28605,28672,28751", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3727,3793,3856,3918,5916,5988,24127,28667,28746,28815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9c9112f3b5828659e41d3d6d877dc27\\transformed\\credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "354,355", "startColumns": "4,4", "startOffsets": "20809,20891", "endColumns": "81,83", "endOffsets": "20886,20970"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\662367f54ee70f4720b44cd5f319fb3e\\transformed\\navigation-common-2.8.5\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "896,909,915,921,930", "startColumns": "4,4,4,4,4", "startOffsets": "52409,53048,53292,53539,53902", "endLines": "908,914,920,923,934", "endColumns": "24,24,24,24,24", "endOffsets": "53043,53287,53534,53667,54079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dc398bc18186089f1a6c4c36afb914b8\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "528,529", "startColumns": "4,4", "startOffsets": "34706,34762", "endColumns": "55,54", "endOffsets": "34757,34812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b39babffc9fa4ce88aa2b7b7536d6226\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "334", "startColumns": "4", "startOffsets": "19512", "endColumns": "42", "endOffsets": "19550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5880c817a785d37566aa982dff3e853e\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "36,160,161,162,163,164,165,309,996", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1519,8560,8619,8667,8723,8798,8874,18179,56199", "endLines": "36,160,161,162,163,164,165,309,1016", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "1570,8614,8662,8718,8793,8869,8941,18240,57034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1e5d223334a1b96c36e444cf4f5a481f\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "308,314", "startColumns": "4,4", "startOffsets": "18125,18430", "endColumns": "53,66", "endOffsets": "18174,18492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7758ab181a0d6d3580cc8a396c9eba31\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "337", "startColumns": "4", "startOffsets": "19658", "endColumns": "53", "endOffsets": "19707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6297678ed5d1623d302efa148585f3f0\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "305", "startColumns": "4", "startOffsets": "17953", "endColumns": "65", "endOffsets": "18014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56e7fd73ffc0fc8f87edb11391c42ff\\transformed\\core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "736", "startColumns": "4", "startOffsets": "46717", "endLines": "743", "endColumns": "8", "endOffsets": "47122"}}, {"source": "D:\\kotlin aNIME\\anime\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "82,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3632,5504,5551,5598,5718,5763,5808", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "3669,5546,5593,5640,5758,5803,5845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bdbcd839d85dfcbf7948b7043369c71a\\transformed\\media-1.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,350,410,476,598,659,725", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "139,210,283,345,405,471,593,654,720,787"}, "to": {"startLines": "107,108,112,312,340,720,722,723,728,730", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "5344,5433,5645,18315,19815,45590,45766,45888,46150,46345", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "5428,5499,5713,18372,19870,45651,45883,45944,46211,46407"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\85c261ec03eee0a4101b5d8231a63563\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "344,454,455,456,457,458,459,460,461,462,463,466,467,468,469,470,471,472,473,474,475,476,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,533,543", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20099,29557,29645,29731,29812,29896,29965,30030,30113,30219,30305,30425,30479,30548,30609,30678,30767,30862,30936,31033,31126,31224,31373,31464,31552,31648,31746,31810,31878,31965,32059,32126,32198,32270,32371,32480,32556,32625,32673,32739,32803,32877,32934,32991,33063,33113,33167,33238,33309,33379,33448,33506,33582,33653,33727,33813,33863,33933,34929,35644", "endLines": "344,454,455,456,457,458,459,460,461,462,465,466,467,468,469,470,471,472,473,474,475,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,542,545", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "20167,29640,29726,29807,29891,29960,30025,30108,30214,30300,30420,30474,30543,30604,30673,30762,30857,30931,31028,31121,31219,31368,31459,31547,31643,31741,31805,31873,31960,32054,32121,32193,32265,32366,32475,32551,32620,32668,32734,32798,32872,32929,32986,33058,33108,33162,33233,33304,33374,33443,33501,33577,33648,33722,33808,33858,33928,33993,35639,35792"}}, {"source": "D:\\kotlin aNIME\\anime\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,199,316,398,502,611,731,824", "endColumns": "143,116,81,103,108,119,92,69", "endOffsets": "194,311,393,497,606,726,819,889"}, "to": {"startLines": "388,446,447,448,449,450,451,517", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "24299,28820,28937,29019,29123,29232,29352,34114", "endColumns": "143,116,81,103,108,119,92,69", "endOffsets": "24438,28932,29014,29118,29227,29347,29440,34179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2bf9eebc1cc6fbbf34f7738396719e0f\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "356", "startColumns": "4", "startOffsets": "20975", "endColumns": "82", "endOffsets": "21053"}}, {"source": "D:\\kotlin aNIME\\anime\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "42", "endOffsets": "54"}, "to": {"startLines": "357", "startColumns": "4", "startOffsets": "21058", "endColumns": "42", "endOffsets": "21096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f341d0dad5e087546904e2a03fd632f3\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "89,90,91,92,93,94,95,96,367,368,369,370,371,372,373,374,376,377,378,379,380,381,382,383,384,883,1017", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4054,4144,4224,4314,4404,4484,4565,4645,21731,21836,22017,22142,22249,22429,22552,22668,22938,23126,23231,23412,23537,23712,23860,23923,23985,52094,57039", "endLines": "89,90,91,92,93,94,95,96,367,368,369,370,371,372,373,374,376,377,378,379,380,381,382,383,384,895,1035", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4139,4219,4309,4399,4479,4560,4640,4720,21831,22012,22137,22244,22424,22547,22663,22766,23121,23226,23407,23532,23707,23855,23918,23980,24059,52404,57451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dec234157bbe815d84cb7d19d03d7a27\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "338", "startColumns": "4", "startOffsets": "19712", "endColumns": "49", "endOffsets": "19757"}}]}]}