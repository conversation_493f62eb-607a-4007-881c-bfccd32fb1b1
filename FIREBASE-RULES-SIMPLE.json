{
  "rules": {
    // ==========================================
    // قواعد مبسطة لتطبيق الأنمي
    // Simple Rules for Anime App
    // ==========================================
    
    // المستخدمين - Users
    "users": {
      "$uid": {
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid"
      }
    },
    
    // المجموعات - Groups
    "groups": {
      ".read": "auth != null",
      ".write": "auth != null",
      ".indexOn": ["code", "ownerId", "name"],
      
      "$groupId": {
        ".read": "auth != null",
        ".write": "auth != null",
        
        // معلومات المجموعة
        "name": {
          ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 50"
        },
        "code": {
          ".validate": "newData.isString() && newData.val().length === 6"
        },
        "ownerId": {
          ".validate": "newData.isString()"
        },
        "ownerName": {
          ".validate": "newData.isString()"
        },
        "createdAt": {
          ".validate": "newData.isNumber()"
        },
        
        // الأعضاء
        "members": {
          ".read": "auth != null",
          ".write": "auth != null"
        },
        
        // الفيديو الحالي
        "currentVideo": {
          ".read": "auth != null",
          ".write": "auth != null",
          ".indexOn": ["syncTimestamp", "lastUpdated"]
        },
        
        // حالة الاتصال
        "presence": {
          ".read": "auth != null",
          ".write": "auth != null"
        },
        
        // الدردشة
        "chat": {
          ".read": "auth != null",
          ".write": "auth != null",
          ".indexOn": ["timestamp"],
          
          "$messageId": {
            "userId": {
              ".validate": "newData.val() === auth.uid"
            },
            "message": {
              ".validate": "newData.isString() && newData.val().length <= 500"
            }
          }
        },
        
        // ردود الفعل
        "reactions": {
          ".read": "auth != null",
          ".write": "auth != null",
          ".indexOn": ["timestamp"]
        }
      }
    }
  }
}
