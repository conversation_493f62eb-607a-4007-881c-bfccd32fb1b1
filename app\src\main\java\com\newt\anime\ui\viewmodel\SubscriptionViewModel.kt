package com.newt.anime.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.newt.anime.data.models.*
import com.newt.anime.data.repository.AuthRepository
import com.newt.anime.data.repository.SubscriptionRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class SubscriptionViewModel : ViewModel() {
    private val subscriptionRepository = SubscriptionRepository()
    private val authRepository = AuthRepository()

    private val _uiState = MutableStateFlow(SubscriptionUiState())
    val uiState: StateFlow<SubscriptionUiState> = _uiState.asStateFlow()

    private val _userSubscription = MutableStateFlow<UserSubscription?>(null)
    val userSubscription: StateFlow<UserSubscription?> = _userSubscription.asStateFlow()

    private val _groupCreationOptions = MutableStateFlow<List<GroupCreationOption>>(emptyList())
    val groupCreationOptions: StateFlow<List<GroupCreationOption>> = _groupCreationOptions.asStateFlow()

    init {
        loadUserSubscription()
    }

    private fun loadUserSubscription() {
        val currentUser = authRepository.currentUser
        if (currentUser != null) {
            viewModelScope.launch {
                try {
                    // مراقبة الاشتراك في الوقت الفعلي
                    subscriptionRepository.observeUserSubscription(currentUser.uid)
                        .collect { subscription ->
                            _userSubscription.value = subscription
                            loadGroupCreationOptions()
                            android.util.Log.d("SubscriptionViewModel", "Subscription loaded: ${subscription.tier.nameAr}")
                        }
                } catch (e: Exception) {
                    android.util.Log.e("SubscriptionViewModel", "Error loading subscription: ${e.message}")
                    _uiState.value = _uiState.value.copy(error = "خطأ في تحميل بيانات الاشتراك")
                }
            }
        }
    }

    private fun loadGroupCreationOptions() {
        val currentUser = authRepository.currentUser ?: return
        
        viewModelScope.launch {
            try {
                val options = subscriptionRepository.getGroupCreationOptions(currentUser.uid)
                _groupCreationOptions.value = options
                android.util.Log.d("SubscriptionViewModel", "Group creation options loaded: ${options.size}")
            } catch (e: Exception) {
                android.util.Log.e("SubscriptionViewModel", "Error loading group creation options: ${e.message}")
            }
        }
    }

    // ترقية الاشتراك
    fun upgradeSubscription(tier: SubscriptionTier, durationMonths: Int = 1) {
        val currentUser = authRepository.currentUser ?: return

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                val result = subscriptionRepository.upgradeSubscription(currentUser.uid, tier, durationMonths)
                
                if (result.isSuccess) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        message = "تم ترقية الاشتراك إلى ${tier.nameAr} بنجاح!"
                    )
                    android.util.Log.d("SubscriptionViewModel", "Subscription upgraded to: ${tier.nameAr}")
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "فشل في ترقية الاشتراك: ${result.exceptionOrNull()?.message}"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "خطأ في ترقية الاشتراك: ${e.message}"
                )
            }
        }
    }

    // إلغاء الاشتراك
    fun cancelSubscription() {
        val currentUser = authRepository.currentUser ?: return

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                val result = subscriptionRepository.cancelSubscription(currentUser.uid)
                
                if (result.isSuccess) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        message = "تم إلغاء الاشتراك بنجاح"
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "فشل في إلغاء الاشتراك"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "خطأ في إلغاء الاشتراك: ${e.message}"
                )
            }
        }
    }

    // التحقق من إمكانية إنشاء مجموعة بعدد معين من الأعضاء
    fun canCreateGroupWithMembers(memberCount: Int): Boolean {
        val subscription = _userSubscription.value ?: return memberCount <= SubscriptionTier.FREE.maxMembers
        return subscription.canCreateGroupWithMembers(memberCount)
    }

    // التحقق من إمكانية انضمام عضو جديد للمجموعة
    suspend fun canAddMemberToGroup(groupId: String, currentMemberCount: Int, groupOwnerId: String): Boolean {
        return subscriptionRepository.canAddMemberToGroup(groupId, currentMemberCount, groupOwnerId)
    }

    // الحصول على الحد الأقصى للأعضاء للمستخدم الحالي
    fun getCurrentMaxMembers(): Int {
        val subscription = _userSubscription.value ?: return SubscriptionTier.FREE.maxMembers
        return subscription.getMaxMembers()
    }

    // التحقق من صلاحية الاشتراك
    fun isSubscriptionValid(): Boolean {
        val subscription = _userSubscription.value ?: return true // المجاني دائماً صالح
        return subscription.isValid()
    }

    // الحصول على الأيام المتبقية في الاشتراك
    fun getDaysRemaining(): Int {
        val subscription = _userSubscription.value ?: return -1
        return subscription.getDaysRemaining()
    }

    // الحصول على المستوى الحالي
    fun getCurrentTier(): SubscriptionTier {
        val subscription = _userSubscription.value ?: return SubscriptionTier.FREE
        return if (subscription.isValid()) subscription.tier else SubscriptionTier.FREE
    }

    // محاكاة عملية الدفع (للاختبار)
    fun simulatePayment(tier: SubscriptionTier) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, message = "جاري معالجة الدفع...")
                
                // محاكاة تأخير الدفع
                kotlinx.coroutines.delay(2000)
                
                // محاكاة نجاح الدفع
                val paymentInfo = PaymentInfo(
                    id = "payment_${System.currentTimeMillis()}",
                    userId = authRepository.currentUser?.uid ?: "",
                    tier = tier,
                    amount = tier.priceUSD,
                    status = PaymentStatus.COMPLETED,
                    paymentMethod = "simulation",
                    transactionId = "sim_${System.currentTimeMillis()}",
                    completedAt = System.currentTimeMillis()
                )

                subscriptionRepository.savePaymentInfo(paymentInfo)
                upgradeSubscription(tier)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "فشل في معالجة الدفع: ${e.message}"
                )
            }
        }
    }

    // مسح الرسائل والأخطاء
    fun clearMessages() {
        _uiState.value = _uiState.value.copy(error = null, message = null)
    }

    // إعادة تحميل البيانات
    fun refresh() {
        loadUserSubscription()
    }
}

data class SubscriptionUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null
)
