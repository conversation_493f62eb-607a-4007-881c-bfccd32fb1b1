package com.newt.anime.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.newt.anime.data.models.ChatMessage
import com.newt.anime.data.models.EmojiReaction
import kotlinx.coroutines.delay

@Composable
fun MessageOverlay(
    chatMessages: List<ChatMessage>,
    emojiReactions: List<EmojiReaction>,
    currentUserId: String = "",
    modifier: Modifier = Modifier
) {
    val currentTime = remember { System.currentTimeMillis() }

    // فلترة الرسائل والإيموجي الحديثة فقط (آخر 30 ثانية)
    val recentMessages = chatMessages.filter {
        it.timestamp > currentTime - 30000 // آخر 30 ثانية فقط
    }
    val recentReactions = emojiReactions.filter {
        it.timestamp > currentTime - 30000 // آخر 30 ثانية فقط
    }
    Box(modifier = modifier) {
        // عرض الرسائل النصية الحديثة فقط - في وسط الشاشة
        recentMessages.takeLast(3).forEachIndexed { index, message ->
            key(message.id) {
                AnimatedChatMessage(
                    message = message,
                    currentUserId = currentUserId,
                    modifier = Modifier
                        .align(Alignment.CenterStart)
                        .padding(
                            start = 16.dp,
                            top = (index * 60).dp
                        )
                )
            }
        }

        // عرض الإيموجي الحديثة فقط - في وسط الشاشة
        recentReactions.takeLast(5).forEachIndexed { index, reaction ->
            key(reaction.id) {
                AnimatedEmojiReaction(
                    reaction = reaction,
                    currentUserId = currentUserId,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .padding(
                            top = (index * 50).dp
                        )
                )
            }
        }
    }
}

@Composable
private fun AnimatedChatMessage(
    message: ChatMessage,
    currentUserId: String = "",
    modifier: Modifier = Modifier
) {
    var isVisible by remember { mutableStateOf(false) }

    LaunchedEffect(message.id) {
        isVisible = true
        delay(3000) // عرض لمدة 3 ثواني
        isVisible = false
    }

    AnimatedVisibility(
        visible = isVisible,
        modifier = modifier,
        enter = slideInHorizontally(initialOffsetX = { -it }) + fadeIn(),
        exit = slideOutHorizontally(targetOffsetX = { -it }) + fadeOut()
    ) {
        Card(
            modifier = Modifier.widthIn(max = 280.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.8f)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                // عرض الاسم مع الرسالة في سطر واحد
                Text(
                    text = buildAnnotatedString {
                        withStyle(
                            style = SpanStyle(
                                color = getUserColor(message.userId, currentUserId),
                                fontWeight = FontWeight.Bold
                            )
                        ) {
                            append(message.userName)
                        }
                        withStyle(
                            style = SpanStyle(color = Color.White)
                        ) {
                            append(": ${message.message}")
                        }
                    },
                    fontSize = 14.sp,
                    lineHeight = 18.sp
                )
            }
        }
    }
}

@Composable
private fun AnimatedEmojiReaction(
    reaction: EmojiReaction,
    currentUserId: String = "",
    modifier: Modifier = Modifier
) {
    var isVisible by remember { mutableStateOf(false) }
    var scale by remember { mutableStateOf(0.5f) }

    LaunchedEffect(reaction.id) {
        isVisible = true
        // تأثير تكبير
        androidx.compose.animation.core.animate(
            initialValue = 0.5f,
            targetValue = 1.2f,
            animationSpec = androidx.compose.animation.core.tween(300)
        ) { value, _ ->
            scale = value
        }
        
        // تأثير تصغير
        androidx.compose.animation.core.animate(
            initialValue = 1.2f,
            targetValue = 1.0f,
            animationSpec = androidx.compose.animation.core.tween(200)
        ) { value, _ ->
            scale = value
        }
        
        delay(3000) // عرض لمدة 3 ثواني إضافية
        isVisible = false
    }

    AnimatedVisibility(
        visible = isVisible,
        modifier = modifier,
        enter = scaleIn() + fadeIn(),
        exit = scaleOut() + fadeOut()
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // الإيموجي بدون إطار
            Text(
                text = reaction.emoji,
                fontSize = (40 * scale).sp,
                textAlign = TextAlign.Center
            )

            // اسم المرسل مع إطار
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = Color.Black.copy(alpha = 0.7f)
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = reaction.userName,
                    color = getUserColor(reaction.userId, currentUserId),
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                )
            }
        }
    }
}

// مكون لعرض الرسائل في نافذة الدردشة
@Composable
fun ChatMessagesList(
    messages: List<ChatMessage>,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        reverseLayout = true, // الرسائل الجديدة في الأسفل
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(messages.takeLast(50)) { message -> // آخر 50 رسالة
            ChatMessageItem(message = message)
        }
    }
}

@Composable
private fun ChatMessageItem(
    message: ChatMessage
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            // اسم المرسل والوقت
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = message.userName,
                    color = MaterialTheme.colorScheme.primary,
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Text(
                    text = formatTime(message.timestamp),
                    color = Color.White.copy(alpha = 0.7f),
                    fontSize = 9.sp
                )
            }
            
            Spacer(modifier = Modifier.height(2.dp))
            
            // نص الرسالة
            Text(
                text = message.message,
                color = Color.White,
                fontSize = 12.sp
            )
        }
    }
}

private fun formatTime(timestamp: Long): String {
    val now = System.currentTimeMillis()
    val diff = now - timestamp

    return when {
        diff < 60000 -> "الآن"
        diff < 3600000 -> "${diff / 60000}د"
        else -> "${diff / 3600000}س"
    }
}

// تحديد لون المستخدم
private fun getUserColor(userId: String, currentUserId: String): Color {
    return when {
        userId == currentUserId -> Color(0xFFFFD700) // ذهبي للمالك
        else -> {
            // ألوان مختلفة للمستخدمين الآخرين
            val colors = listOf(
                Color(0xFF00BCD4), // سماوي
                Color(0xFF4CAF50), // أخضر
                Color(0xFFFF9800), // برتقالي
                Color(0xFF9C27B0), // بنفسجي
                Color(0xFFE91E63), // وردي
                Color(0xFF2196F3), // أزرق
                Color(0xFFFF5722), // أحمر برتقالي
                Color(0xFF607D8B)  // رمادي مزرق
            )
            val index = userId.hashCode().let { if (it < 0) -it else it } % colors.size
            colors[index]
        }
    }
}
