@echo off
echo ========================================
echo        Anime App APK Builder
echo ========================================
echo.

echo [1/4] تنظيف المشروع...
call gradlew clean
if %errorlevel% neq 0 (
    echo خطأ في تنظيف المشروع!
    pause
    exit /b 1
)

echo.
echo [2/4] بناء إصدار التطوير...
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo خطأ في بناء إصدار التطوير!
    pause
    exit /b 1
)

echo.
echo [3/4] بناء إصدار الإنتاج...
call gradlew assembleRelease
if %errorlevel% neq 0 (
    echo خطأ في بناء إصدار الإنتاج!
    pause
    exit /b 1
)

echo.
echo [4/4] نسخ ملفات APK...

:: إنشاء مجلد للإصدارات إذا لم يكن موجوداً
if not exist "releases" mkdir releases

:: نسخ ملفات APK مع أسماء واضحة وتاريخ
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%"

copy "app\build\outputs\apk\debug\app-debug.apk" "releases\anime-app-debug-%timestamp%.apk"
copy "app\build\outputs\apk\release\app-release-unsigned.apk" "releases\anime-app-release-unsigned-%timestamp%.apk"

:: نسخ أحدث إصدار للمجلد الرئيسي
copy "app\build\outputs\apk\debug\app-debug.apk" "anime-app-debug-latest.apk"
copy "app\build\outputs\apk\release\app-release-unsigned.apk" "anime-app-release-unsigned-latest.apk"

echo.
echo ========================================
echo           البناء مكتمل بنجاح!
echo ========================================
echo.
echo ملفات APK المُنشأة:
echo - anime-app-debug-latest.apk (للتطوير والاختبار)
echo - anime-app-release-unsigned-latest.apk (للإنتاج - غير موقع)
echo.
echo ملفات مؤرشفة في مجلد releases:
echo - anime-app-debug-%timestamp%.apk
echo - anime-app-release-unsigned-%timestamp%.apk
echo.
echo ملاحظة: إصدار الإنتاج غير موقع ويحتاج توقيع للنشر
echo.
pause
