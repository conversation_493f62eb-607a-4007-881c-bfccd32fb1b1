package com.newt.anime.data.repository

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.database.FirebaseDatabase
import com.newt.anime.data.models.User
import kotlinx.coroutines.tasks.await

class AuthRepository {
    private val auth = FirebaseAuth.getInstance()
    private val database = FirebaseDatabase.getInstance()
    
    val currentUser: FirebaseUser? get() = auth.currentUser
    
    suspend fun signUp(email: String, password: String, displayName: String): Result<FirebaseUser> {
        return try {
            val result = auth.createUserWithEmailAndPassword(email, password).await()
            val user = result.user
            if (user != null) {
                // Save user data to Realtime Database
                val userData = User(
                    uid = user.uid,
                    email = email,
                    displayName = displayName
                )
                database.reference.child("users").child(user.uid).setValue(userData).await()
                Result.success(user)
            } else {
                Result.failure(Exception("فشل في إنشاء المستخدم"))
            }
        } catch (e: Exception) {
            val errorMessage = when {
                e.message?.contains("email", ignoreCase = true) == true ->
                    "البريد الإلكتروني غير صحيح أو مستخدم بالفعل"
                e.message?.contains("password", ignoreCase = true) == true ->
                    "كلمة المرور ضعيفة جداً"
                e.message?.contains("network", ignoreCase = true) == true ->
                    "تحقق من اتصال الإنترنت"
                else -> "خطأ في إنشاء الحساب: ${e.message}"
            }
            Result.failure(Exception(errorMessage))
        }
    }
    
    suspend fun signIn(email: String, password: String): Result<FirebaseUser> {
        return try {
            val result = auth.signInWithEmailAndPassword(email, password).await()
            val user = result.user
            if (user != null) {
                Result.success(user)
            } else {
                Result.failure(Exception("Failed to sign in"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    fun signOut() {
        auth.signOut()
    }
    
    suspend fun getCurrentUserData(): User? {
        return try {
            val uid = currentUser?.uid ?: return null
            val snapshot = database.reference.child("users").child(uid).get().await()
            snapshot.getValue(User::class.java)
        } catch (e: Exception) {
            null
        }
    }
}
