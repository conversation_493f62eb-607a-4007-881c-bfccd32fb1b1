{"logs": [{"outputFile": "com.newt.anime.app-mergeReleaseResources-62:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6436519ada9dddee292e4f473475ac25\\transformed\\exoplayer-ui-2.19.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,496,672,758,847,931,1023,1114,1190,1255,1344,1437,1508,1576,1637,1705,1860,2018,2172,2239,2321,2392,2472,2563,2657,2723,2788,2841,2899,2947,3008,3070,3146,3208,3272,3333,3394,3458,3523,3589,3641,3705,3783,3861", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,154,157,153,66,81,70,79,90,93,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57", "endOffsets": "280,491,667,753,842,926,1018,1109,1185,1250,1339,1432,1503,1571,1632,1700,1855,2013,2167,2234,2316,2387,2467,2558,2652,2718,2783,2836,2894,2942,3003,3065,3141,3203,3267,3328,3389,3453,3518,3584,3636,3700,3778,3856,3914"}, "to": {"startLines": "2,11,15,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,546,4451,4537,4626,4710,4802,4893,4969,5034,5123,5216,5287,5355,5416,5484,5639,5797,5951,6018,6100,6171,6251,6342,6436,6502,7228,7281,7339,7387,7448,7510,7586,7648,7712,7773,7834,7898,7963,8029,8081,8145,8223,8301", "endLines": "10,14,18,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,154,157,153,66,81,70,79,90,93,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57", "endOffsets": "330,541,717,4532,4621,4705,4797,4888,4964,5029,5118,5211,5282,5350,5411,5479,5634,5792,5946,6013,6095,6166,6246,6337,6431,6497,6562,7276,7334,7382,7443,7505,7581,7643,7707,7768,7829,7893,7958,8024,8076,8140,8218,8296,8354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4366d762d4e78881da860b75d1c4c59f\\transformed\\play-services-basement-18.4.0\\res\\values-mn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "38", "startColumns": "4", "startOffsets": "2868", "endColumns": "149", "endOffsets": "3013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9c9112f3b5828659e41d3d6d877dc27\\transformed\\credentials-1.2.0-rc01\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,113", "endOffsets": "161,275"}, "to": {"startLines": "19,20", "startColumns": "4,4", "startOffsets": "722,833", "endColumns": "110,113", "endOffsets": "828,942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\85c261ec03eee0a4101b5d8231a63563\\transformed\\material3-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,290,402,519,618,715,829,971,1089,1228,1313,1415,1507,1605,1723,1845,1952,2094,2238,2370,2546,2672,2793,2913,3032,3125,3225,3348,3486,3585,3691,3797,3941,4086,4193,4292,4375,4470,4564,4675,4760,4844,4945,5025,5108,5207,5307,5402,5504,5591,5695,5794,5899,6030,6110,6214", "endColumns": "118,115,111,116,98,96,113,141,117,138,84,101,91,97,117,121,106,141,143,131,175,125,120,119,118,92,99,122,137,98,105,105,143,144,106,98,82,94,93,110,84,83,100,79,82,98,99,94,101,86,103,98,104,130,79,103,94", "endOffsets": "169,285,397,514,613,710,824,966,1084,1223,1308,1410,1502,1600,1718,1840,1947,2089,2233,2365,2541,2667,2788,2908,3027,3120,3220,3343,3481,3580,3686,3792,3936,4081,4188,4287,4370,4465,4559,4670,4755,4839,4940,5020,5103,5202,5302,5397,5499,5586,5690,5789,5894,6025,6105,6209,6304"}, "to": {"startLines": "108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8856,8975,9091,9203,9320,9419,9516,9630,9772,9890,10029,10114,10216,10308,10406,10524,10646,10753,10895,11039,11171,11347,11473,11594,11714,11833,11926,12026,12149,12287,12386,12492,12598,12742,12887,12994,13093,13176,13271,13365,13476,13561,13645,13746,13826,13909,14008,14108,14203,14305,14392,14496,14595,14700,14831,14911,15015", "endColumns": "118,115,111,116,98,96,113,141,117,138,84,101,91,97,117,121,106,141,143,131,175,125,120,119,118,92,99,122,137,98,105,105,143,144,106,98,82,94,93,110,84,83,100,79,82,98,99,94,101,86,103,98,104,130,79,103,94", "endOffsets": "8970,9086,9198,9315,9414,9511,9625,9767,9885,10024,10109,10211,10303,10401,10519,10641,10748,10890,11034,11166,11342,11468,11589,11709,11828,11921,12021,12144,12282,12381,12487,12593,12737,12882,12989,13088,13171,13266,13360,13471,13556,13640,13741,13821,13904,14003,14103,14198,14300,14387,14491,14590,14695,14826,14906,15010,15105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95a85adbd9a0258061c7c940036f2eec\\transformed\\ui-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,283,375,471,554,640,734,821,902,985,1068,1141,1218,1298,1375,1452,1518", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,76,79,76,76,65,116", "endOffsets": "192,278,370,466,549,635,729,816,897,980,1063,1136,1213,1293,1370,1447,1513,1630"}, "to": {"startLines": "28,29,49,50,51,106,107,165,166,167,168,169,170,171,172,174,175,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1682,1774,4180,4272,4368,8676,8762,15110,15197,15278,15361,15444,15517,15594,15674,15852,15929,15995", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,76,79,76,76,65,116", "endOffsets": "1769,1855,4267,4363,4446,8757,8851,15192,15273,15356,15439,15512,15589,15669,15746,15924,15990,16107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f341d0dad5e087546904e2a03fd632f3\\transformed\\play-services-base-18.1.0\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,452,580,683,816,938,1063,1169,1309,1412,1576,1701,1838,2002,2059,2117", "endColumns": "105,152,127,102,132,121,124,105,139,102,163,124,136,163,56,57,72", "endOffsets": "298,451,579,682,815,937,1062,1168,1308,1411,1575,1700,1837,2001,2058,2116,2189"}, "to": {"startLines": "30,31,32,33,34,35,36,37,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1860,1970,2127,2259,2366,2503,2629,2758,3018,3162,3269,3437,3566,3707,3875,3936,3998", "endColumns": "109,156,131,106,136,125,128,109,143,106,167,128,140,167,60,61,76", "endOffsets": "1965,2122,2254,2361,2498,2624,2753,2863,3157,3264,3432,3561,3702,3870,3931,3993,4070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf7927868eeddad3b4319378a3201862\\transformed\\browser-1.4.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,264,369", "endColumns": "104,103,104,107", "endOffsets": "155,259,364,472"}, "to": {"startLines": "48,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "4075,8359,8463,8568", "endColumns": "104,103,104,107", "endOffsets": "4175,8458,8563,8671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5f82ddb128f9d8fc1f894d4feaff6b09\\transformed\\exoplayer-core-2.19.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,244,308,382,456,547,635", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "115,174,239,303,377,451,542,630,711"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6567,6632,6691,6756,6820,6894,6968,7059,7147", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "6627,6686,6751,6815,6889,6963,7054,7142,7223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8f761d99cc674c08e5b049b2a7bbf06\\transformed\\core-1.16.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "21,22,23,24,25,26,27,173", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "947,1045,1147,1248,1346,1451,1563,15751", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "1040,1142,1243,1341,1446,1558,1677,15847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dc398bc18186089f1a6c4c36afb914b8\\transformed\\foundation-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "16112,16198", "endColumns": "85,88", "endOffsets": "16193,16282"}}]}]}