package com.newt.anime.ui.components

import android.content.Context
import android.net.Uri
import android.provider.MediaStore
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.storage.FirebaseStorage
import com.newt.anime.data.models.VideoSession
import com.newt.anime.data.repository.GroupRepository
import com.newt.anime.ui.viewmodel.SubscriptionViewModel
import com.newt.anime.data.models.SubscriptionTier
import androidx.lifecycle.viewmodel.compose.viewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.UUID

@Composable
fun VideoUploadDialog(
    groupId: String,
    onDismiss: () -> Unit,
    onVideoUploaded: (String) -> Unit,
    subscriptionViewModel: SubscriptionViewModel = viewModel(),
    onNavigateToSubscription: () -> Unit = {}
) {
    val context = LocalContext.current
    var selectedVideoUri by remember { mutableStateOf<Uri?>(null) }
    var videoTitle by remember { mutableStateOf("") }
    var videoFileName by remember { mutableStateOf("") }
    var videoFileSize by remember { mutableStateOf("") }
    var videoFileSizeInMB by remember { mutableStateOf(0.0) }
    var uploadProgress by remember { mutableStateOf(0f) }
    var isUploading by remember { mutableStateOf(false) }
    var uploadSuccess by remember { mutableStateOf(false) }
    var uploadError by remember { mutableStateOf<String?>(null) }
    var quotaError by remember { mutableStateOf<String?>(null) }

    val userSubscription by subscriptionViewModel.userSubscription.collectAsState()

    // Firebase instances
    val storage = remember { FirebaseStorage.getInstance() }
    val auth = remember { FirebaseAuth.getInstance() }
    val groupRepository = remember { GroupRepository() }
    
    // مشغل اختيار الفيديو - MP4 فقط
    val videoPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            selectedVideoUri = it
            // الحصول على معلومات الملف
            val fileInfo = getVideoFileInfo(context, it)
            videoFileName = fileInfo.first
            videoFileSize = fileInfo.second
            videoFileSizeInMB = fileInfo.third

            // فحص الرصيد فوراً
            quotaError = if (subscriptionViewModel.canUploadVideo(videoFileSizeInMB)) {
                null
            } else {
                subscriptionViewModel.getQuotaErrorMessage(videoFileSizeInMB)
            }

            // تعيين عنوان افتراضي من اسم الملف
            if (videoTitle.isEmpty()) {
                videoTitle = videoFileName.substringBeforeLast(".")
            }
        }
    }
    
    Dialog(onDismissRequest = { if (!isUploading) onDismiss() }) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "🎬 رفع فيديو MP4",
                        fontSize = 22.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                    if (!isUploading) {
                        IconButton(onClick = onDismiss) {
                            Icon(
                                Icons.Default.Close, 
                                contentDescription = "إغلاق",
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
                
                when {
                    uploadSuccess -> {
                        // Success State
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Icon(
                                Icons.Default.CheckCircle,
                                contentDescription = null,
                                modifier = Modifier.size(64.dp),
                                tint = Color(0xFF4CAF50)
                            )
                            Text(
                                text = "🎉 تم رفع الفيديو بنجاح!",
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF4CAF50),
                                textAlign = TextAlign.Center
                            )

                            // عرض تفاصيل الاستهلاك
                            Card(
                                colors = CardDefaults.cardColors(
                                    containerColor = Color(0xFF4CAF50).copy(alpha = 0.1f)
                                ),
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Column(
                                    modifier = Modifier.padding(12.dp),
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Text(
                                        text = "📊 تفاصيل الاستهلاك",
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight.Bold,
                                        color = Color(0xFF4CAF50)
                                    )

                                    Spacer(modifier = Modifier.height(8.dp))

                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween
                                    ) {
                                        Text(
                                            text = "⚡ تم استهلاك:",
                                            fontSize = 12.sp,
                                            fontWeight = FontWeight.Medium
                                        )
                                        Text(
                                            text = if (videoFileSizeInMB >= 1024) {
                                                "${String.format("%.2f", videoFileSizeInMB / 1024)}GB"
                                            } else {
                                                "${String.format("%.1f", videoFileSizeInMB)}MB"
                                            },
                                            fontSize = 12.sp,
                                            fontWeight = FontWeight.Bold,
                                            color = Color(0xFF4CAF50)
                                        )
                                    }

                                    userSubscription?.let { subscription ->
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {
                                            Text(
                                                text = "💾 الرصيد الحالي:",
                                                fontSize = 12.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                            Text(
                                                text = subscription.getRemainingQuotaText(),
                                                fontSize = 12.sp,
                                                fontWeight = FontWeight.Bold,
                                                color = when (subscription.tier) {
                                                    SubscriptionTier.FREE -> Color.Gray
                                                    SubscriptionTier.PREMIUM -> Color(0xFFFFD700)
                                                    SubscriptionTier.UNLIMITED -> Color(0xFFFF6B35)
                                                }
                                            )
                                        }
                                    }
                                }
                            }

                            Text(
                                text = "الفيديو متاح الآن لجميع أعضاء المجموعة",
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                textAlign = TextAlign.Center
                            )
                            
                            LaunchedEffect(Unit) {
                                kotlinx.coroutines.delay(2000)
                                onDismiss()
                            }
                        }
                    }
                    
                    uploadError != null -> {
                        // Error State
                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Card(
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.errorContainer
                                ),
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Column(
                                    modifier = Modifier.padding(16.dp)
                                ) {
                                    Text(
                                        text = "❌ فشل في رفع الفيديو",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Bold,
                                        color = MaterialTheme.colorScheme.onErrorContainer
                                    )
                                    Spacer(modifier = Modifier.height(8.dp))
                                    Text(
                                        text = uploadError!!,
                                        fontSize = 14.sp,
                                        color = MaterialTheme.colorScheme.onErrorContainer
                                    )
                                }
                            }
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                OutlinedButton(
                                    onClick = onDismiss,
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text("إلغاء")
                                }
                                Button(
                                    onClick = {
                                        uploadError = null
                                        uploadSuccess = false
                                        isUploading = false
                                        uploadProgress = 0f
                                    },
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text("🔄 إعادة المحاولة")
                                }
                            }
                        }
                    }
                    
                    isUploading -> {
                        // Uploading State
                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "📤 جاري رفع الفيديو...",
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Medium,
                                color = MaterialTheme.colorScheme.primary,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )
                            
                            // Progress Bar
                            Column(
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                LinearProgressIndicator(
                                    progress = { uploadProgress },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(12.dp)
                                        .clip(RoundedCornerShape(6.dp)),
                                    color = MaterialTheme.colorScheme.primary,
                                    trackColor = MaterialTheme.colorScheme.surfaceVariant,
                                )
                                
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "${(uploadProgress * 100).toInt()}% مكتمل",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Bold,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                    Text(
                                        text = if (videoFileSizeInMB >= 1024) {
                                            "${String.format("%.2f", videoFileSizeInMB / 1024)}GB"
                                        } else {
                                            "${String.format("%.1f", videoFileSizeInMB)}MB"
                                        },
                                        fontSize = 12.sp,
                                        fontWeight = FontWeight.Bold,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }

                                // عرض تفاصيل الاستهلاك أثناء الرفع
                                Card(
                                    colors = CardDefaults.cardColors(
                                        containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                                    ),
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Column(
                                        modifier = Modifier.padding(8.dp)
                                    ) {
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.SpaceBetween
                                        ) {
                                            Text(
                                                text = "⚡ سيتم استهلاك:",
                                                fontSize = 11.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                            Text(
                                                text = if (videoFileSizeInMB >= 1024) {
                                                    "${String.format("%.2f", videoFileSizeInMB / 1024)}GB من رصيدك"
                                                } else {
                                                    "${String.format("%.1f", videoFileSizeInMB)}MB من رصيدك"
                                                },
                                                fontSize = 11.sp,
                                                fontWeight = FontWeight.Bold,
                                                color = MaterialTheme.colorScheme.primary
                                            )
                                        }

                                        userSubscription?.let { subscription ->
                                            Row(
                                                modifier = Modifier.fillMaxWidth(),
                                                horizontalArrangement = Arrangement.SpaceBetween
                                            ) {
                                                Text(
                                                    text = "💾 الرصيد الحالي:",
                                                    fontSize = 11.sp,
                                                    fontWeight = FontWeight.Medium
                                                )
                                                Text(
                                                    text = subscription.getRemainingQuotaText(),
                                                    fontSize = 11.sp,
                                                    fontWeight = FontWeight.Bold,
                                                    color = when (subscription.tier) {
                                                        SubscriptionTier.FREE -> Color.Gray
                                                        SubscriptionTier.PREMIUM -> Color(0xFFFFD700)
                                                        SubscriptionTier.UNLIMITED -> Color(0xFFFF6B35)
                                                    }
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                            
                            Text(
                                text = "يرجى عدم إغلاق التطبيق أثناء الرفع",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                    
                    else -> {
                        // Input State
                        Column(
                            verticalArrangement = Arrangement.spacedBy(20.dp)
                        ) {
                            // Video Title Input
                            OutlinedTextField(
                                value = videoTitle,
                                onValueChange = { videoTitle = it },
                                label = { Text("عنوان الفيديو") },
                                placeholder = { Text("أدخل عنوان الفيديو...") },
                                modifier = Modifier.fillMaxWidth(),
                                singleLine = true
                            )
                            
                            // File Picker Area
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable { videoPickerLauncher.launch("video/mp4") }
                                    .border(
                                        2.dp,
                                        if (selectedVideoUri != null) 
                                            MaterialTheme.colorScheme.primary 
                                        else 
                                            MaterialTheme.colorScheme.outline,
                                        RoundedCornerShape(12.dp)
                                    ),
                                colors = CardDefaults.cardColors(
                                    containerColor = if (selectedVideoUri != null)
                                        MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.1f)
                                    else
                                        MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                                )
                            ) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(24.dp),
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    verticalArrangement = Arrangement.spacedBy(12.dp)
                                ) {
                                    Icon(
                                        if (selectedVideoUri != null) Icons.Default.PlayArrow else Icons.Default.Add,
                                        contentDescription = null,
                                        modifier = Modifier.size(48.dp),
                                        tint = if (selectedVideoUri != null)
                                            MaterialTheme.colorScheme.primary
                                        else
                                            MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                    
                                    if (selectedVideoUri != null) {
                                        Text(
                                            text = "✅ تم اختيار الفيديو",
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.Bold,
                                            color = MaterialTheme.colorScheme.primary
                                        )
                                        Text(
                                            text = videoFileName,
                                            fontSize = 14.sp,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                        Text(
                                            text = videoFileSize,
                                            fontSize = 12.sp,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )

                                        // عرض تفاصيل الرصيد والاستهلاك
                                        userSubscription?.let { subscription ->
                                            Spacer(modifier = Modifier.height(8.dp))

                                            Card(
                                                colors = CardDefaults.cardColors(
                                                    containerColor = when {
                                                        quotaError != null -> MaterialTheme.colorScheme.errorContainer
                                                        subscription.tier == SubscriptionTier.FREE -> Color(0xFFF5F5F5)
                                                        subscription.tier == SubscriptionTier.PREMIUM -> Color(0xFFFFD700).copy(alpha = 0.1f)
                                                        else -> Color(0xFFFF6B35).copy(alpha = 0.1f)
                                                    }
                                                ),
                                                modifier = Modifier.fillMaxWidth()
                                            ) {
                                                Column(
                                                    modifier = Modifier.padding(12.dp),
                                                    horizontalAlignment = Alignment.CenterHorizontally
                                                ) {
                                                    // عرض الرصيد الحالي
                                                    Row(
                                                        modifier = Modifier.fillMaxWidth(),
                                                        horizontalArrangement = Arrangement.SpaceBetween,
                                                        verticalAlignment = Alignment.CenterVertically
                                                    ) {
                                                        Text(
                                                            text = "💾 الرصيد الحالي:",
                                                            fontSize = 12.sp,
                                                            fontWeight = FontWeight.Medium
                                                        )
                                                        Text(
                                                            text = subscription.getRemainingQuotaText(),
                                                            fontSize = 12.sp,
                                                            fontWeight = FontWeight.Bold,
                                                            color = when (subscription.tier) {
                                                                SubscriptionTier.FREE -> Color.Gray
                                                                SubscriptionTier.PREMIUM -> Color(0xFFFFD700)
                                                                SubscriptionTier.UNLIMITED -> Color(0xFFFF6B35)
                                                            }
                                                        )
                                                    }

                                                    // عرض وزن الفيديو والاستهلاك المتوقع
                                                    if (videoFileSizeInMB > 0) {
                                                        Spacer(modifier = Modifier.height(8.dp))

                                                        Row(
                                                            modifier = Modifier.fillMaxWidth(),
                                                            horizontalArrangement = Arrangement.SpaceBetween,
                                                            verticalAlignment = Alignment.CenterVertically
                                                        ) {
                                                            Text(
                                                                text = "📹 وزن الفيديو:",
                                                                fontSize = 12.sp,
                                                                fontWeight = FontWeight.Medium
                                                            )
                                                            Text(
                                                                text = if (videoFileSizeInMB >= 1024) {
                                                                    "${String.format("%.2f", videoFileSizeInMB / 1024)}GB"
                                                                } else {
                                                                    "${String.format("%.1f", videoFileSizeInMB)}MB"
                                                                },
                                                                fontSize = 12.sp,
                                                                fontWeight = FontWeight.Bold,
                                                                color = MaterialTheme.colorScheme.primary
                                                            )
                                                        }

                                                        Row(
                                                            modifier = Modifier.fillMaxWidth(),
                                                            horizontalArrangement = Arrangement.SpaceBetween,
                                                            verticalAlignment = Alignment.CenterVertically
                                                        ) {
                                                            Text(
                                                                text = "⚡ سيتم استهلاك:",
                                                                fontSize = 12.sp,
                                                                fontWeight = FontWeight.Medium
                                                            )
                                                            Text(
                                                                text = if (videoFileSizeInMB >= 1024) {
                                                                    "${String.format("%.2f", videoFileSizeInMB / 1024)}GB من رصيدك"
                                                                } else {
                                                                    "${String.format("%.1f", videoFileSizeInMB)}MB من رصيدك"
                                                                },
                                                                fontSize = 12.sp,
                                                                fontWeight = FontWeight.Bold,
                                                                color = if (quotaError != null) MaterialTheme.colorScheme.error else Color(0xFF4CAF50)
                                                            )
                                                        }

                                                        // عرض الرصيد المتبقي بعد الرفع
                                                        if (quotaError == null) {
                                                            val remainingAfterUpload = when (subscription.tier) {
                                                                SubscriptionTier.FREE -> {
                                                                    val dailyRemaining = subscription.tier.dailyUploadMB - subscription.dailyUploadUsedMB
                                                                    dailyRemaining - videoFileSizeInMB
                                                                }
                                                                else -> {
                                                                    (subscription.remainingUploadGB * 1024) - videoFileSizeInMB
                                                                }
                                                            }

                                                            Row(
                                                                modifier = Modifier.fillMaxWidth(),
                                                                horizontalArrangement = Arrangement.SpaceBetween,
                                                                verticalAlignment = Alignment.CenterVertically
                                                            ) {
                                                                Text(
                                                                    text = "📊 سيتبقى:",
                                                                    fontSize = 12.sp,
                                                                    fontWeight = FontWeight.Medium
                                                                )
                                                                Text(
                                                                    text = if (subscription.tier == SubscriptionTier.FREE) {
                                                                        "${maxOf(0, remainingAfterUpload.toInt())}MB اليوم"
                                                                    } else {
                                                                        if (remainingAfterUpload >= 1024) {
                                                                            "${String.format("%.2f", remainingAfterUpload / 1024)}GB"
                                                                        } else {
                                                                            "${String.format("%.1f", remainingAfterUpload)}MB"
                                                                        }
                                                                    },
                                                                    fontSize = 12.sp,
                                                                    fontWeight = FontWeight.Bold,
                                                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                                                )
                                                            }
                                                        }
                                                    }

                                                    // عرض رسالة الخطأ إذا وجدت
                                                    quotaError?.let { error ->
                                                        Spacer(modifier = Modifier.height(8.dp))

                                                        Column(
                                                            horizontalAlignment = Alignment.CenterHorizontally
                                                        ) {
                                                            Text(
                                                                text = "⚠️ $error",
                                                                fontSize = 11.sp,
                                                                color = MaterialTheme.colorScheme.error,
                                                                textAlign = TextAlign.Center,
                                                                modifier = Modifier.fillMaxWidth()
                                                            )

                                                            // إذا كان المستخدم مجاني، أظهر زر الاشتراك
                                                            if (userSubscription?.tier == SubscriptionTier.FREE) {
                                                                Spacer(modifier = Modifier.height(8.dp))
                                                                Button(
                                                                    onClick = {
                                                                        onDismiss()
                                                                        onNavigateToSubscription()
                                                                    },
                                                                    colors = ButtonDefaults.buttonColors(
                                                                        containerColor = Color(0xFFFFD700)
                                                                    ),
                                                                    modifier = Modifier.fillMaxWidth()
                                                                ) {
                                                                    Icon(
                                                                        Icons.Default.Star,
                                                                        contentDescription = null,
                                                                        tint = Color.Black,
                                                                        modifier = Modifier.size(16.dp)
                                                                    )
                                                                    Spacer(modifier = Modifier.width(8.dp))
                                                                    Text(
                                                                        text = "⭐ اشترك الآن",
                                                                        color = Color.Black,
                                                                        fontWeight = FontWeight.Bold
                                                                    )
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    } else {
                                        Text(
                                            text = "📁 اختر ملف فيديو MP4",
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.Medium
                                        )
                                        Text(
                                            text = "اضغط لاختيار ملف من جهازك",
                                            fontSize = 12.sp,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                }
                            }
                            
                            // Action Buttons
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                OutlinedButton(
                                    onClick = onDismiss,
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text("إلغاء")
                                }
                                
                                Button(
                                    onClick = {
                                        selectedVideoUri?.let { uri ->
                                            // فحص الرصيد قبل الرفع
                                            if (!subscriptionViewModel.canUploadVideo(videoFileSizeInMB)) {
                                                quotaError = subscriptionViewModel.getQuotaErrorMessage(videoFileSizeInMB)
                                                return@let
                                            }

                                            uploadVideoToFirebase(
                                                uri = uri,
                                                title = videoTitle.ifEmpty { videoFileName.substringBeforeLast(".") },
                                                groupId = groupId,
                                                storage = storage,
                                                auth = auth,
                                                groupRepository = groupRepository,
                                                subscriptionViewModel = subscriptionViewModel,
                                                videoSizeInMB = videoFileSizeInMB,
                                                onProgress = { progress -> uploadProgress = progress },
                                                onSuccess = { downloadUrl ->
                                                    uploadSuccess = true
                                                    onVideoUploaded(downloadUrl)
                                                },
                                                onError = { error -> uploadError = error },
                                                onStart = { isUploading = true }
                                            )
                                        }
                                    },
                                    enabled = selectedVideoUri != null && videoTitle.isNotEmpty() && quotaError == null,
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Icon(Icons.Default.Add, contentDescription = null)
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("🚀 رفع الفيديو")
                                }
                            }
                        }
                    }
                }

                // Tips Card
                if (!isUploading && !uploadSuccess && uploadError == null) {
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            Text(
                                text = "💡 نصائح مهمة:",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.primary
                            )
                            Text(
                                text = "• ملفات MP4 فقط مدعومة\n" +
                                        "• حجم أقل من 100 ميجابايت\n" +
                                        "• سيظهر الفيديو لجميع الأعضاء\n" +
                                        "• المالك فقط يتحكم في التشغيل",
                                fontSize = 11.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                lineHeight = 14.sp
                            )
                        }
                    }
                }
            }
        }
    }
}

// Helper function to get video file info - returns (name, sizeString, sizeInMB)
private fun getVideoFileInfo(context: Context, uri: Uri): Triple<String, String, Double> {
    return try {
        val cursor = context.contentResolver.query(uri, null, null, null, null)
        cursor?.use {
            val nameIndex = it.getColumnIndex(MediaStore.Video.Media.DISPLAY_NAME)
            val sizeIndex = it.getColumnIndex(MediaStore.Video.Media.SIZE)

            if (it.moveToFirst()) {
                val name = if (nameIndex >= 0) it.getString(nameIndex) else "فيديو.mp4"
                val size = if (sizeIndex >= 0) it.getLong(sizeIndex) else 0L
                val sizeStr = formatFileSize(size)
                val sizeInMB = size / (1024.0 * 1024.0) // تحويل إلى MB
                Triple(name, sizeStr, sizeInMB)
            } else {
                Triple("فيديو.mp4", "غير معروف", 0.0)
            }
        } ?: Triple("فيديو.mp4", "غير معروف", 0.0)
    } catch (e: Exception) {
        Triple("فيديو.mp4", "غير معروف", 0.0)
    }
}

// Helper function to format file size
private fun formatFileSize(bytes: Long): String {
    return when {
        bytes < 1024 -> "$bytes B"
        bytes < 1024 * 1024 -> "${bytes / 1024} KB"
        bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)} MB"
        else -> "${bytes / (1024 * 1024 * 1024)} GB"
    }
}

// Main upload function
private fun uploadVideoToFirebase(
    uri: Uri,
    title: String,
    groupId: String,
    storage: FirebaseStorage,
    auth: FirebaseAuth,
    groupRepository: GroupRepository,
    subscriptionViewModel: SubscriptionViewModel,
    videoSizeInMB: Double,
    onProgress: (Float) -> Unit,
    onSuccess: (String) -> Unit,
    onError: (String) -> Unit,
    onStart: () -> Unit
) {
    val currentUser = auth.currentUser
    if (currentUser == null) {
        onError("المستخدم غير مسجل الدخول")
        return
    }

    try {
        onStart()
        android.util.Log.d("VideoUpload", "Starting upload for user: ${currentUser.uid}")

        // إنشاء مرجع فريد للفيديو
        val videoId = UUID.randomUUID().toString()
        val videoRef = storage.reference
            .child("videos")
            .child(currentUser.uid)
            .child("$videoId.mp4")

        android.util.Log.d("VideoUpload", "Upload path: ${videoRef.path}")

        // بدء رفع الفيديو
        val uploadTask = videoRef.putFile(uri)

        // تتبع التقدم
        uploadTask.addOnProgressListener { taskSnapshot ->
            val progress = taskSnapshot.bytesTransferred.toFloat() / taskSnapshot.totalByteCount.toFloat()
            onProgress(progress)
            android.util.Log.d("VideoUpload", "Progress: ${(progress * 100).toInt()}%")
        }

        // عند النجاح
        uploadTask.addOnSuccessListener {
            android.util.Log.d("VideoUpload", "Upload successful, getting download URL...")
            videoRef.downloadUrl.addOnSuccessListener { downloadUri ->
                val downloadUrl = downloadUri.toString()
                android.util.Log.d("VideoUpload", "Download URL: $downloadUrl")

                // حفظ الفيديو في المجموعة
                val videoSession = VideoSession(
                    videoUrl = downloadUrl,
                    title = title,
                    isPlaying = false,
                    currentPosition = 0L,
                    lastUpdated = System.currentTimeMillis(),
                    syncCommand = "",
                    syncTimestamp = System.currentTimeMillis(),
                    ownerAction = false,
                    hasStarted = false
                )

                // حفظ في قاعدة البيانات
                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        android.util.Log.d("VideoUpload", "Saving video to group: $groupId")
                        val result = groupRepository.updateVideoSession(groupId, videoSession)
                        if (result.isSuccess) {
                            android.util.Log.d("VideoUpload", "✅ Video saved successfully")

                            // استهلاك الرصيد بعد نجاح الرفع
                            subscriptionViewModel.consumeUploadQuota(
                                sizeInMB = videoSizeInMB,
                                onSuccess = {
                                    android.util.Log.d("VideoUpload", "✅ Quota consumed: ${videoSizeInMB}MB")
                                    onSuccess(downloadUrl)
                                },
                                onError = { quotaError ->
                                    android.util.Log.e("VideoUpload", "❌ Failed to consume quota: $quotaError")
                                    // الفيديو تم رفعه لكن فشل في استهلاك الرصيد
                                    onSuccess(downloadUrl) // نكمل العملية
                                }
                            )
                        } else {
                            val error = result.exceptionOrNull()?.message ?: "خطأ غير معروف"
                            android.util.Log.e("VideoUpload", "❌ Failed to save video: $error")
                            onError("فشل في حفظ الفيديو: $error")
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("VideoUpload", "❌ Exception saving video: ${e.message}")
                        onError("فشل في حفظ الفيديو: ${e.message}")
                    }
                }
            }.addOnFailureListener { exception ->
                android.util.Log.e("VideoUpload", "❌ Failed to get download URL: ${exception.message}")
                onError("فشل في الحصول على رابط التحميل: ${exception.message}")
            }
        }

        // عند الفشل
        uploadTask.addOnFailureListener { exception ->
            android.util.Log.e("VideoUpload", "❌ Upload failed: ${exception.message}")
            onError("فشل في رفع الفيديو: ${exception.message}")
        }

    } catch (e: Exception) {
        android.util.Log.e("VideoUpload", "❌ Unexpected error: ${e.message}")
        onError("خطأ غير متوقع: ${e.message}")
    }
}
