package com.newt.anime.ui.components

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.storage.FirebaseStorage
import com.newt.anime.data.models.VideoSession
import com.newt.anime.data.repository.GroupRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.util.UUID

@Composable
fun VideoUploadDialog(
    groupId: String,
    onDismiss: () -> Unit,
    onVideoUploaded: (String) -> Unit
) {
    val context = LocalContext.current
    var selectedVideoUri by remember { mutableStateOf<Uri?>(null) }
    var videoTitle by remember { mutableStateOf("") }
    var uploadProgress by remember { mutableStateOf(0) }
    var isUploading by remember { mutableStateOf(false) }
    var uploadSuccess by remember { mutableStateOf(false) }
    var uploadError by remember { mutableStateOf<String?>(null) }


    // Firebase instances
    val storage = remember { FirebaseStorage.getInstance() }
    val auth = remember { FirebaseAuth.getInstance() }
    val groupRepository = remember { GroupRepository() }
    
    // مشغل اختيار الفيديو
    val videoPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        selectedVideoUri = uri
    }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // عنوان الحوار
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "📤 رفع فيديو جديد",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                    IconButton(onClick = onDismiss) {
                        Icon(Icons.Default.Close, contentDescription = "إغلاق")
                    }
                }
                
                // عنوان الفيديو
                OutlinedTextField(
                    value = videoTitle,
                    onValueChange = { videoTitle = it },
                    label = { Text("عنوان الفيديو") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !isUploading
                )

                
                // اختيار الفيديو
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    onClick = { 
                        if (!isUploading) {
                            videoPickerLauncher.launch("video/mp4")
                        }
                    },
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(20.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            Icons.Default.PlayArrow,
                            contentDescription = null,
                            modifier = Modifier.size(48.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = if (selectedVideoUri != null) "✅ تم اختيار الفيديو" else "📁 اختر فيديو MP4",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        if (selectedVideoUri != null) {
                            Text(
                                text = "فيديو جاهز للرفع",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        } else {
                            Text(
                                text = "اضغط لاختيار ملف MP4",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
                
                // حالة الرفع
                when {
                    !isUploading && !uploadSuccess -> {
                        // زر الرفع
                        Button(
                            onClick = {
                                selectedVideoUri?.let { uri ->
                                    isUploading = true
                                    uploadProgress = 0
                                    uploadError = null

                                    // رفع حقيقي لـ Firebase Storage
                                    uploadToFirebase(
                                        uri = uri,
                                        title = videoTitle,
                                        groupId = groupId,
                                        storage = storage,
                                        auth = auth,
                                        groupRepository = groupRepository,
                                        onProgress = { progress ->
                                            uploadProgress = progress
                                        },
                                        onSuccess = { downloadUrl ->
                                            isUploading = false
                                            uploadSuccess = true
                                            onVideoUploaded(downloadUrl)
                                        },
                                        onError = { error ->
                                            isUploading = false
                                            uploadError = error
                                        }
                                    )
                                }
                            },
                            modifier = Modifier.fillMaxWidth(),
                            enabled = selectedVideoUri != null && videoTitle.isNotEmpty()
                        ) {
                            Icon(Icons.Default.Add, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("🚀 رفع الفيديو", fontSize = 16.sp, fontWeight = FontWeight.Bold)
                        }
                    }
                    
                    isUploading -> {
                        // شريط التقدم
                        Column {
                            Text(
                                text = "📤 جاري رفع الفيديو...",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                color = MaterialTheme.colorScheme.primary
                            )
                            Spacer(modifier = Modifier.height(12.dp))
                            
                            // شريط التقدم
                            LinearProgressIndicator(
                                progress = uploadProgress / 100f,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(8.dp),
                                color = MaterialTheme.colorScheme.primary
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "$uploadProgress%",
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                Text(
                                    text = "رفع الفيديو...",
                                    fontSize = 12.sp,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                    
                    uploadSuccess -> {
                        LaunchedEffect(Unit) {
                            kotlinx.coroutines.delay(1000)
                            onDismiss()
                        }

                        Card(
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.primaryContainer
                            )
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Text(
                                    text = "🎉 تم رفع الفيديو بنجاح!",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = "تم حفظ الفيديو في Firebase Storage",
                                    fontSize = 14.sp,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer
                                )
                            }
                        }
                    }

                    uploadError != null -> {
                        Card(
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.errorContainer
                            )
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp)
                            ) {
                                Text(
                                    text = "❌ فشل في رفع الفيديو",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = MaterialTheme.colorScheme.onErrorContainer
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = uploadError!!,
                                    fontSize = 14.sp,
                                    color = MaterialTheme.colorScheme.onErrorContainer
                                )
                            }
                        }

                        Button(
                            onClick = {
                                uploadError = null
                                uploadSuccess = false
                            },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text("🔄 إعادة المحاولة")
                        }
                    }
                }
                
                // معلومات إضافية
                if (!isUploading && !uploadSuccess) {
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp)
                        ) {
                            Text(
                                text = "💡 نصائح:",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = "• استخدم ملفات MP4 فقط\n" +
                                        "• حجم أقل من 100 ميجابايت\n" +
                                        "• سيظهر للجميع بعد الرفع\n" +
                                        "• المالك فقط يتحكم في التشغيل",
                                fontSize = 11.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}

// رفع حقيقي لـ Firebase Storage
private fun uploadToFirebase(
    uri: Uri,
    title: String,
    groupId: String,
    storage: FirebaseStorage,
    auth: FirebaseAuth,
    groupRepository: GroupRepository,
    onProgress: (Int) -> Unit,
    onSuccess: (String) -> Unit,
    onError: (String) -> Unit
) {
    val currentUser = auth.currentUser
    if (currentUser == null) {
        onError("المستخدم غير مسجل الدخول")
        return
    }

    try {
        android.util.Log.d("VideoUpload", "Starting upload for user: ${currentUser.uid}")

        // إنشاء مرجع فريد للفيديو
        val videoId = UUID.randomUUID().toString()
        val videoRef = storage.reference
            .child("videos")
            .child(currentUser.uid)
            .child("$videoId.mp4")

        android.util.Log.d("VideoUpload", "Upload path: ${videoRef.path}")

        // بدء رفع الفيديو
        val uploadTask = videoRef.putFile(uri)

        // تتبع التقدم
        uploadTask.addOnProgressListener { taskSnapshot ->
            val progress = ((taskSnapshot.bytesTransferred * 100) / taskSnapshot.totalByteCount).toInt()
            onProgress(progress)
        }

        // عند النجاح
        uploadTask.addOnSuccessListener {
            videoRef.downloadUrl.addOnSuccessListener { downloadUri ->
                val downloadUrl = downloadUri.toString()

                // حفظ الفيديو في المجموعة بدون تشغيل تلقائي وبدون جودة
                val videoSession = try {
                    VideoSession(
                        videoUrl = downloadUrl,
                        title = title.ifEmpty { "فيديو جديد" },
                        isPlaying = false, // لا تشغيل تلقائي - انتظار أمر المالك
                        currentPosition = 0L,
                        lastUpdated = System.currentTimeMillis(),
                        syncCommand = "", // لا أمر تشغيل
                        syncTimestamp = System.currentTimeMillis(),
                        ownerAction = false,
                        hasStarted = false // لم يبدأ التشغيل بعد
                    )
                } catch (e: Exception) {
                    android.util.Log.e("VideoUpload", "Error creating VideoSession: ${e.message}")
                    onError("خطأ في إنشاء جلسة الفيديو: ${e.message}")
                    return@addOnSuccessListener
                }

                // حفظ في قاعدة البيانات مع حماية إضافية
                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        android.util.Log.d("VideoUpload", "Saving video to group: $groupId")
                        val result = groupRepository.updateVideoSession(groupId, videoSession)
                        if (result.isSuccess) {
                            android.util.Log.d("VideoUpload", "✅ Video saved successfully")
                            onSuccess(downloadUrl)
                        } else {
                            val error = result.exceptionOrNull()?.message ?: "Unknown error"
                            android.util.Log.e("VideoUpload", "❌ Failed to save video: $error")
                            onError("فشل في حفظ الفيديو في المجموعة: $error")
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("VideoUpload", "❌ Exception saving video: ${e.message}")
                        onError("فشل في حفظ الفيديو في المجموعة: ${e.message}")
                    }
                }
            }.addOnFailureListener { exception ->
                onError("فشل في الحصول على رابط التحميل: ${exception.message}")
            }
        }

        // عند الفشل
        uploadTask.addOnFailureListener { exception ->
            onError("فشل في رفع الفيديو: ${exception.message}")
        }

    } catch (e: Exception) {
        onError("خطأ غير متوقع: ${e.message}")
    }
}
