package com.newt.anime.ui.components

import android.content.Context
import android.net.Uri
import android.provider.MediaStore
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.storage.FirebaseStorage
import com.newt.anime.data.models.VideoSession
import com.newt.anime.data.repository.GroupRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.UUID

@Composable
fun VideoUploadDialog(
    groupId: String,
    onDismiss: () -> Unit,
    onVideoUploaded: (String) -> Unit
) {
    val context = LocalContext.current
    var selectedVideoUri by remember { mutableStateOf<Uri?>(null) }
    var videoTitle by remember { mutableStateOf("") }
    var videoFileName by remember { mutableStateOf("") }
    var videoFileSize by remember { mutableStateOf("") }
    var uploadProgress by remember { mutableStateOf(0f) }
    var isUploading by remember { mutableStateOf(false) }
    var uploadSuccess by remember { mutableStateOf(false) }
    var uploadError by remember { mutableStateOf<String?>(null) }

    // Firebase instances
    val storage = remember { FirebaseStorage.getInstance() }
    val auth = remember { FirebaseAuth.getInstance() }
    val groupRepository = remember { GroupRepository() }
    
    // مشغل اختيار الفيديو - MP4 فقط
    val videoPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            selectedVideoUri = it
            // الحصول على معلومات الملف
            val fileInfo = getVideoFileInfo(context, it)
            videoFileName = fileInfo.first
            videoFileSize = fileInfo.second
            
            // تعيين عنوان افتراضي من اسم الملف
            if (videoTitle.isEmpty()) {
                videoTitle = videoFileName.substringBeforeLast(".")
            }
        }
    }
    
    Dialog(onDismissRequest = { if (!isUploading) onDismiss() }) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "🎬 رفع فيديو MP4",
                        fontSize = 22.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                    if (!isUploading) {
                        IconButton(onClick = onDismiss) {
                            Icon(
                                Icons.Default.Close, 
                                contentDescription = "إغلاق",
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
                
                when {
                    uploadSuccess -> {
                        // Success State
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Icon(
                                Icons.Default.CheckCircle,
                                contentDescription = null,
                                modifier = Modifier.size(64.dp),
                                tint = Color(0xFF4CAF50)
                            )
                            Text(
                                text = "🎉 تم رفع الفيديو بنجاح!",
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF4CAF50),
                                textAlign = TextAlign.Center
                            )
                            Text(
                                text = "الفيديو متاح الآن لجميع أعضاء المجموعة",
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                textAlign = TextAlign.Center
                            )
                            
                            LaunchedEffect(Unit) {
                                kotlinx.coroutines.delay(2000)
                                onDismiss()
                            }
                        }
                    }
                    
                    uploadError != null -> {
                        // Error State
                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Card(
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.errorContainer
                                ),
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Column(
                                    modifier = Modifier.padding(16.dp)
                                ) {
                                    Text(
                                        text = "❌ فشل في رفع الفيديو",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Bold,
                                        color = MaterialTheme.colorScheme.onErrorContainer
                                    )
                                    Spacer(modifier = Modifier.height(8.dp))
                                    Text(
                                        text = uploadError!!,
                                        fontSize = 14.sp,
                                        color = MaterialTheme.colorScheme.onErrorContainer
                                    )
                                }
                            }
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                OutlinedButton(
                                    onClick = onDismiss,
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text("إلغاء")
                                }
                                Button(
                                    onClick = {
                                        uploadError = null
                                        uploadSuccess = false
                                        isUploading = false
                                        uploadProgress = 0f
                                    },
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text("🔄 إعادة المحاولة")
                                }
                            }
                        }
                    }
                    
                    isUploading -> {
                        // Uploading State
                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "📤 جاري رفع الفيديو...",
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Medium,
                                color = MaterialTheme.colorScheme.primary,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )
                            
                            // Progress Bar
                            Column(
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                LinearProgressIndicator(
                                    progress = { uploadProgress },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(12.dp)
                                        .clip(RoundedCornerShape(6.dp)),
                                    color = MaterialTheme.colorScheme.primary,
                                    trackColor = MaterialTheme.colorScheme.surfaceVariant,
                                )
                                
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "${(uploadProgress * 100).toInt()}%",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Bold,
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                    Text(
                                        text = videoFileName,
                                        fontSize = 12.sp,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                            
                            Text(
                                text = "يرجى عدم إغلاق التطبيق أثناء الرفع",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                    
                    else -> {
                        // Input State
                        Column(
                            verticalArrangement = Arrangement.spacedBy(20.dp)
                        ) {
                            // Video Title Input
                            OutlinedTextField(
                                value = videoTitle,
                                onValueChange = { videoTitle = it },
                                label = { Text("عنوان الفيديو") },
                                placeholder = { Text("أدخل عنوان الفيديو...") },
                                modifier = Modifier.fillMaxWidth(),
                                singleLine = true
                            )
                            
                            // File Picker Area
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable { videoPickerLauncher.launch("video/mp4") }
                                    .border(
                                        2.dp,
                                        if (selectedVideoUri != null) 
                                            MaterialTheme.colorScheme.primary 
                                        else 
                                            MaterialTheme.colorScheme.outline,
                                        RoundedCornerShape(12.dp)
                                    ),
                                colors = CardDefaults.cardColors(
                                    containerColor = if (selectedVideoUri != null)
                                        MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.1f)
                                    else
                                        MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                                )
                            ) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(24.dp),
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    verticalArrangement = Arrangement.spacedBy(12.dp)
                                ) {
                                    Icon(
                                        if (selectedVideoUri != null) Icons.Default.PlayArrow else Icons.Default.Add,
                                        contentDescription = null,
                                        modifier = Modifier.size(48.dp),
                                        tint = if (selectedVideoUri != null)
                                            MaterialTheme.colorScheme.primary
                                        else
                                            MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                    
                                    if (selectedVideoUri != null) {
                                        Text(
                                            text = "✅ تم اختيار الفيديو",
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.Bold,
                                            color = MaterialTheme.colorScheme.primary
                                        )
                                        Text(
                                            text = videoFileName,
                                            fontSize = 14.sp,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                        Text(
                                            text = videoFileSize,
                                            fontSize = 12.sp,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    } else {
                                        Text(
                                            text = "📁 اختر ملف فيديو MP4",
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.Medium
                                        )
                                        Text(
                                            text = "اضغط لاختيار ملف من جهازك",
                                            fontSize = 12.sp,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                }
                            }
                            
                            // Action Buttons
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                OutlinedButton(
                                    onClick = onDismiss,
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text("إلغاء")
                                }
                                
                                Button(
                                    onClick = {
                                        selectedVideoUri?.let { uri ->
                                            uploadVideoToFirebase(
                                                uri = uri,
                                                title = videoTitle.ifEmpty { videoFileName.substringBeforeLast(".") },
                                                groupId = groupId,
                                                storage = storage,
                                                auth = auth,
                                                groupRepository = groupRepository,
                                                onProgress = { progress -> uploadProgress = progress },
                                                onSuccess = { downloadUrl ->
                                                    uploadSuccess = true
                                                    onVideoUploaded(downloadUrl)
                                                },
                                                onError = { error -> uploadError = error },
                                                onStart = { isUploading = true }
                                            )
                                        }
                                    },
                                    enabled = selectedVideoUri != null && videoTitle.isNotEmpty(),
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Icon(Icons.Default.Add, contentDescription = null)
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("🚀 رفع الفيديو")
                                }
                            }
                        }
                    }
                }

                // Tips Card
                if (!isUploading && !uploadSuccess && uploadError == null) {
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            Text(
                                text = "💡 نصائح مهمة:",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.primary
                            )
                            Text(
                                text = "• ملفات MP4 فقط مدعومة\n" +
                                        "• حجم أقل من 100 ميجابايت\n" +
                                        "• سيظهر الفيديو لجميع الأعضاء\n" +
                                        "• المالك فقط يتحكم في التشغيل",
                                fontSize = 11.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                lineHeight = 14.sp
                            )
                        }
                    }
                }
            }
        }
    }
}

// Helper function to get video file info
private fun getVideoFileInfo(context: Context, uri: Uri): Pair<String, String> {
    return try {
        val cursor = context.contentResolver.query(uri, null, null, null, null)
        cursor?.use {
            val nameIndex = it.getColumnIndex(MediaStore.Video.Media.DISPLAY_NAME)
            val sizeIndex = it.getColumnIndex(MediaStore.Video.Media.SIZE)

            if (it.moveToFirst()) {
                val name = if (nameIndex >= 0) it.getString(nameIndex) else "فيديو.mp4"
                val size = if (sizeIndex >= 0) it.getLong(sizeIndex) else 0L
                val sizeStr = formatFileSize(size)
                Pair(name, sizeStr)
            } else {
                Pair("فيديو.mp4", "غير معروف")
            }
        } ?: Pair("فيديو.mp4", "غير معروف")
    } catch (e: Exception) {
        Pair("فيديو.mp4", "غير معروف")
    }
}

// Helper function to format file size
private fun formatFileSize(bytes: Long): String {
    return when {
        bytes < 1024 -> "$bytes B"
        bytes < 1024 * 1024 -> "${bytes / 1024} KB"
        bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)} MB"
        else -> "${bytes / (1024 * 1024 * 1024)} GB"
    }
}

// Main upload function
private fun uploadVideoToFirebase(
    uri: Uri,
    title: String,
    groupId: String,
    storage: FirebaseStorage,
    auth: FirebaseAuth,
    groupRepository: GroupRepository,
    onProgress: (Float) -> Unit,
    onSuccess: (String) -> Unit,
    onError: (String) -> Unit,
    onStart: () -> Unit
) {
    val currentUser = auth.currentUser
    if (currentUser == null) {
        onError("المستخدم غير مسجل الدخول")
        return
    }

    try {
        onStart()
        android.util.Log.d("VideoUpload", "Starting upload for user: ${currentUser.uid}")

        // إنشاء مرجع فريد للفيديو
        val videoId = UUID.randomUUID().toString()
        val videoRef = storage.reference
            .child("videos")
            .child(currentUser.uid)
            .child("$videoId.mp4")

        android.util.Log.d("VideoUpload", "Upload path: ${videoRef.path}")

        // بدء رفع الفيديو
        val uploadTask = videoRef.putFile(uri)

        // تتبع التقدم
        uploadTask.addOnProgressListener { taskSnapshot ->
            val progress = taskSnapshot.bytesTransferred.toFloat() / taskSnapshot.totalByteCount.toFloat()
            onProgress(progress)
            android.util.Log.d("VideoUpload", "Progress: ${(progress * 100).toInt()}%")
        }

        // عند النجاح
        uploadTask.addOnSuccessListener {
            android.util.Log.d("VideoUpload", "Upload successful, getting download URL...")
            videoRef.downloadUrl.addOnSuccessListener { downloadUri ->
                val downloadUrl = downloadUri.toString()
                android.util.Log.d("VideoUpload", "Download URL: $downloadUrl")

                // حفظ الفيديو في المجموعة
                val videoSession = VideoSession(
                    videoUrl = downloadUrl,
                    title = title,
                    isPlaying = false,
                    currentPosition = 0L,
                    lastUpdated = System.currentTimeMillis(),
                    syncCommand = "",
                    syncTimestamp = System.currentTimeMillis(),
                    ownerAction = false,
                    hasStarted = false
                )

                // حفظ في قاعدة البيانات
                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        android.util.Log.d("VideoUpload", "Saving video to group: $groupId")
                        val result = groupRepository.updateVideoSession(groupId, videoSession)
                        if (result.isSuccess) {
                            android.util.Log.d("VideoUpload", "✅ Video saved successfully")
                            onSuccess(downloadUrl)
                        } else {
                            val error = result.exceptionOrNull()?.message ?: "خطأ غير معروف"
                            android.util.Log.e("VideoUpload", "❌ Failed to save video: $error")
                            onError("فشل في حفظ الفيديو: $error")
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("VideoUpload", "❌ Exception saving video: ${e.message}")
                        onError("فشل في حفظ الفيديو: ${e.message}")
                    }
                }
            }.addOnFailureListener { exception ->
                android.util.Log.e("VideoUpload", "❌ Failed to get download URL: ${exception.message}")
                onError("فشل في الحصول على رابط التحميل: ${exception.message}")
            }
        }

        // عند الفشل
        uploadTask.addOnFailureListener { exception ->
            android.util.Log.e("VideoUpload", "❌ Upload failed: ${exception.message}")
            onError("فشل في رفع الفيديو: ${exception.message}")
        }

    } catch (e: Exception) {
        android.util.Log.e("VideoUpload", "❌ Unexpected error: ${e.message}")
        onError("خطأ غير متوقع: ${e.message}")
    }
}
