package com.newt.anime.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import com.newt.anime.data.models.GroupCreationOption
import com.newt.anime.data.models.SubscriptionTier
import com.newt.anime.ui.viewmodel.SubscriptionViewModel

@Composable
fun GroupCreationDialog(
    onDismiss: () -> Unit,
    onCreateGroup: (String, Int) -> Unit,
    onUpgradeRequired: (SubscriptionTier) -> Unit,
    subscriptionViewModel: SubscriptionViewModel = viewModel()
) {
    var groupName by remember { mutableStateOf("") }
    var selectedOption by remember { mutableStateOf<GroupCreationOption?>(null) }
    val groupCreationOptions by subscriptionViewModel.groupCreationOptions.collectAsState()
    val userSubscription by subscriptionViewModel.userSubscription.collectAsState()

    // تحميل الخيارات عند فتح الحوار
    LaunchedEffect(Unit) {
        subscriptionViewModel.refresh()
    }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "🎬 إنشاء مجموعة جديدة",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                    IconButton(onClick = onDismiss) {
                        Icon(
                            Icons.Default.Close,
                            contentDescription = "إغلاق",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // اسم المجموعة
                OutlinedTextField(
                    value = groupName,
                    onValueChange = { groupName = it },
                    label = { Text("اسم المجموعة") },
                    placeholder = { Text("أدخل اسم المجموعة...") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                // عنوان اختيار عدد الأعضاء
                Text(
                    text = "👥 اختر الحد الأقصى للأعضاء",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                // خيارات عدد الأعضاء - مبسطة وواضحة
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // خيار 4 أعضاء - مجاني
                    MemberLimitOptionCard(
                        maxMembers = 4,
                        tier = SubscriptionTier.FREE,
                        isAvailable = true,
                        isSelected = selectedOption?.maxMembers == 4,
                        onClick = {
                            selectedOption = GroupCreationOption(
                                maxMembers = 4,
                                tier = SubscriptionTier.FREE,
                                isAvailable = true
                            )
                        }
                    )

                    // خيار 10 أعضاء - بريميوم
                    val canUse10 = userSubscription?.tier == SubscriptionTier.PREMIUM ||
                                   userSubscription?.tier == SubscriptionTier.UNLIMITED
                    MemberLimitOptionCard(
                        maxMembers = 10,
                        tier = SubscriptionTier.PREMIUM,
                        isAvailable = canUse10,
                        isSelected = selectedOption?.maxMembers == 10,
                        onClick = {
                            if (canUse10) {
                                selectedOption = GroupCreationOption(
                                    maxMembers = 10,
                                    tier = SubscriptionTier.PREMIUM,
                                    isAvailable = true
                                )
                            } else {
                                onUpgradeRequired(SubscriptionTier.PREMIUM)
                            }
                        }
                    )

                    // خيار غير محدود - أقصى مستوى
                    val canUseUnlimited = userSubscription?.tier == SubscriptionTier.UNLIMITED
                    MemberLimitOptionCard(
                        maxMembers = Int.MAX_VALUE,
                        tier = SubscriptionTier.UNLIMITED,
                        isAvailable = canUseUnlimited,
                        isSelected = selectedOption?.maxMembers == Int.MAX_VALUE,
                        onClick = {
                            if (canUseUnlimited) {
                                selectedOption = GroupCreationOption(
                                    maxMembers = Int.MAX_VALUE,
                                    tier = SubscriptionTier.UNLIMITED,
                                    isAvailable = true
                                )
                            } else {
                                onUpgradeRequired(SubscriptionTier.UNLIMITED)
                            }
                        }
                    )
                }

                // معلومات الاشتراك الحالي
                userSubscription?.let { subscription ->
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp)
                        ) {
                            Text(
                                text = "📊 اشتراكك الحالي: ${subscription.tier.nameAr}",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.primary
                            )
                            Text(
                                text = "الحد الأقصى: ${if (subscription.getMaxMembers() == Int.MAX_VALUE) "غير محدود" else "${subscription.getMaxMembers()} أعضاء"}",
                                fontSize = 11.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }

                // أزرار العمل
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("إلغاء")
                    }

                    Button(
                        onClick = {
                            if (groupName.isNotBlank() && selectedOption != null) {
                                onCreateGroup(groupName, selectedOption!!.maxMembers)
                            }
                        },
                        enabled = groupName.isNotBlank() && selectedOption != null,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("🚀 إنشاء")
                    }
                }
            }
        }
    }
}

@Composable
fun MemberLimitOptionCard(
    maxMembers: Int,
    tier: SubscriptionTier,
    isAvailable: Boolean,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val borderColor = when {
        !isAvailable -> Color.Gray
        isSelected -> MaterialTheme.colorScheme.primary
        tier == SubscriptionTier.FREE -> Color.Gray
        tier == SubscriptionTier.PREMIUM -> Color(0xFFFFD700)
        tier == SubscriptionTier.UNLIMITED -> Color(0xFFFF6B35)
        else -> Color.Gray
    }

    val backgroundColor = when {
        !isAvailable -> MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
        isSelected -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
        tier == SubscriptionTier.PREMIUM -> Color(0xFFFFD700).copy(alpha = 0.1f)
        tier == SubscriptionTier.UNLIMITED -> Color(0xFFFF6B35).copy(alpha = 0.1f)
        else -> MaterialTheme.colorScheme.surface
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = borderColor,
                shape = RoundedCornerShape(12.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        if (isAvailable) Icons.Default.Person else Icons.Default.Lock,
                        contentDescription = null,
                        tint = borderColor,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = when {
                            maxMembers == Int.MAX_VALUE -> "غير محدود"
                            else -> "$maxMembers أعضاء"
                        },
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = if (isAvailable) MaterialTheme.colorScheme.onSurface else Color.Gray
                    )
                }

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = when {
                        !isAvailable && tier != SubscriptionTier.FREE -> "يتطلب ${tier.nameAr} - ${tier.getPrice()}"
                        tier == SubscriptionTier.FREE -> "مجاني"
                        else -> "${tier.nameAr} - ${tier.getPrice()}"
                    },
                    fontSize = 12.sp,
                    color = if (isAvailable) MaterialTheme.colorScheme.onSurfaceVariant else Color.Gray
                )

                // ميزات إضافية للخطط المدفوعة
                if (tier != SubscriptionTier.FREE) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = when (tier) {
                            SubscriptionTier.PREMIUM -> "• ${tier.uploadLimitGB.toInt()}GB رصيد رفع • مجموعات غير محدودة"
                            SubscriptionTier.UNLIMITED -> "• ${tier.uploadLimitGB.toInt()}GB رصيد رفع • أعضاء غير محدودين • دعم أولوية"
                            else -> ""
                        },
                        fontSize = 10.sp,
                        color = if (isAvailable) borderColor else Color.Gray,
                        lineHeight = 12.sp
                    )
                }
            }

            // أيقونة الحالة
            if (isSelected) {
                Icon(
                    Icons.Default.Star,
                    contentDescription = "مختار",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            } else if (!isAvailable) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        Icons.Default.Lock,
                        contentDescription = "مقفل",
                        tint = Color.Gray,
                        modifier = Modifier.size(20.dp)
                    )
                    Text(
                        text = "ترقية",
                        fontSize = 10.sp,
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

// حوار ترقية الاشتراك
@Composable
fun UpgradeRequiredDialog(
    requiredTier: SubscriptionTier,
    onUpgrade: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "🔒 ترقية مطلوبة",
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text("لإنشاء مجموعة بهذا العدد من الأعضاء، تحتاج إلى ترقية اشتراكك إلى:")
                
                Spacer(modifier = Modifier.height(12.dp))
                
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = when (requiredTier) {
                            SubscriptionTier.PREMIUM -> Color(0xFFFFD700).copy(alpha = 0.1f)
                            SubscriptionTier.UNLIMITED -> Color(0xFFFF6B35).copy(alpha = 0.1f)
                            else -> MaterialTheme.colorScheme.surfaceVariant
                        }
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(12.dp)
                    ) {
                        Text(
                            text = "💎 ${requiredTier.nameAr}",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Text(
                            text = requiredTier.getPrice() + if (requiredTier != SubscriptionTier.FREE) " شهرياً" else "",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "• ${if (requiredTier.maxMembers == Int.MAX_VALUE) "أعضاء غير محدودين" else "حتى ${requiredTier.maxMembers} أعضاء"}",
                            fontSize = 12.sp
                        )
                        requiredTier.features.take(2).forEach { feature ->
                            Text(
                                text = "• $feature",
                                fontSize = 12.sp
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            Button(onClick = onUpgrade) {
                Text("🚀 ترقية الآن")
            }
        },
        dismissButton = {
            OutlinedButton(onClick = onDismiss) {
                Text("لاحقاً")
            }
        }
    )
}
