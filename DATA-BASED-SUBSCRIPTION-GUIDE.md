# 📊 دليل نظام الاشتراكات القائم على استهلاك البيانات

## 📱 **anime-app-DATA-BASED-SUBSCRIPTION-debug.apk**

### **🔄 نظام اشتراكات محدث - قائم على استهلاك البيانات بدلاً من الوقت**

---

## 🎯 **المستويات الجديدة:**

### 1. **🆓 المستوى المجاني (FREE)**
- **السعر**: مجاني
- **الحد الأقصى**: 4 أعضاء لكل مجموعة
- **رصيد الرفع**: 300MB يومياً (يتجدد كل 24 ساعة)
- **الميزات**:
  - حتى 4 أعضاء
  - 300MB رفع يومياً
  - مشاهدة الفيديوهات
  - الدردشة الأساسية

### 2. **⭐ المستوى البريميوم (PREMIUM)**
- **السعر**: $4.99 (دفعة واحدة)
- **الحد الأقصى**: 10 أعضاء لكل مجموعة
- **رصيد الرفع**: 15GB (حتى انتهاء الرصيد)
- **الميزات**:
  - حتى 10 أعضاء
  - 15GB رصيد رفع
  - مجموعات غير محدودة
  - دردشة متقدمة
  - إحصائيات المشاهدة

### 3. **🚀 المستوى غير المحدود (UNLIMITED)**
- **السعر**: $9.99 (دفعة واحدة)
- **الحد الأقصى**: أعضاء غير محدودين
- **رصيد الرفع**: 30GB (حتى انتهاء الرصيد)
- **الميزات**:
  - أعضاء غير محدودين
  - 30GB رصيد رفع
  - مجموعات غير محدودة
  - جودة فيديو عالية
  - دعم أولوية 24/7
  - ميزات حصرية

---

## 📊 **عرض الرصيد في الشاشة الرئيسية:**

```
┌─────────────────────────────────────┐
│        مجموعات المشاهدة            │
├─────────────────────────────────────┤
│ 📊 مجاني                    [85%] │
│ 45MB متبقي اليوم                   │
├─────────────────────────────────────┤
│ [إنشاء مجموعة] [انضمام لمجموعة]   │
└─────────────────────────────────────┘
```

### **للمستويات المدفوعة:**
```
┌─────────────────────────────────────┐
│ 📊 بريميوم                  [60%] │
│ 6.2GB متبقي                        │
└─────────────────────────────────────┘
```

---

## 🔧 **كيفية عمل النظام الجديد:**

### **1. المستوى المجاني:**
- **300MB يومياً** - يتجدد كل 24 ساعة
- **عداد يومي** يظهر المتبقي من اليوم
- **إعادة تعيين تلقائية** في منتصف الليل
- **لا ينتهي** - دائماً متاح

### **2. المستويات المدفوعة:**
- **رصيد ثابت** (15GB أو 30GB)
- **استهلاك تدريجي** مع كل رفع فيديو
- **انتهاء الاشتراك** عند نفاد الرصيد
- **العودة للمجاني** تلقائياً عند انتهاء الرصيد

### **3. حوار إنشاء المجموعة المحسن:**

```
┌─────────────────────────────────────┐
│        🎬 إنشاء مجموعة جديدة        │
├─────────────────────────────────────┤
│ اسم المجموعة: [_______________]    │
│                                     │
│ 👥 اختر الحد الأقصى للأعضاء:       │
│                                     │
│ ┌─ 👤 4 أعضاء ─────────────────┐   │
│ │ مجاني ✅                      │   │
│ └───────────────────────────────┘   │
│                                     │
│ ┌─ 👥 10 أعضاء ────────────────┐   │
│ │ بريميوم - $4.99              │   │
│ │ • 15GB رصيد رفع 🔒           │   │
│ └───────────────────────────────┘   │
│                                     │
│ ┌─ 🌟 غير محدود ──────────────┐   │
│ │ غير محدود - $9.99            │   │
│ │ • 30GB رصيد رفع 🔒           │   │
│ └───────────────────────────────┘   │
│                                     │
│ [إلغاء]              [🚀 إنشاء]   │
└─────────────────────────────────────┘
```

---

## 💳 **شاشة الاشتراكات المحدثة:**

### **عرض الرصيد الحالي:**
```
┌─────────────────────────────────────┐
│ 📊 اشتراكك الحالي                  │
│ ⭐ بريميوم                         │
│                                     │
│ ┌─ 📊 رصيد الرفع ──────────────┐   │
│ │ 6.2GB متبقي                   │   │
│ │ ████████░░ 60% مستخدم         │   │
│ └───────────────────────────────┘   │
│                                     │
│ الحد الأقصى: 10 أعضاء              │
├─────────────────────────────────────┤
│ 🎯 شراء رصيد إضافي                │
│                                     │
│ ┌─ ⭐ بريميوم ─────────────────┐    │
│ │ $4.99                         │    │
│ │ رصيد 15GB                    │    │
│ │ [شراء رصيد]                  │    │
│ └───────────────────────────────┘    │
└─────────────────────────────────────┘
```

---

## 🔄 **آلية استهلاك الرصيد:**

### **عند رفع فيديو:**

#### **للمجاني:**
1. **فحص الحد اليومي**: هل تبقى مساحة من 300MB؟
2. **رفع الفيديو**: إذا كان الحجم مناسب
3. **تحديث العداد**: زيادة المستخدم اليوم
4. **إعادة تعيين**: في منتصف الليل تلقائياً

#### **للمدفوع:**
1. **فحص الرصيد**: هل يكفي الرصيد المتبقي؟
2. **رفع الفيديو**: إذا كان الرصيد كافي
3. **خصم الرصيد**: تقليل الرصيد بحجم الفيديو
4. **فحص الانتهاء**: إذا وصل الرصيد للصفر
5. **العودة للمجاني**: تلقائياً عند انتهاء الرصيد

---

## 📈 **مؤشرات الاستهلاك:**

### **في الشاشة الرئيسية:**
- **شريط دائري** يظهر نسبة الاستهلاك
- **ألوان تحذيرية**:
  - 🟢 أخضر (0-70%)
  - 🟡 برتقالي (70-90%)
  - 🔴 أحمر (90-100%)

### **في شاشة الاشتراكات:**
- **شريط خطي** مفصل
- **نص واضح** للرصيد المتبقي
- **نسبة مئوية** للاستهلاك

---

## ⚠️ **رسائل التحذير:**

### **عند اقتراب انتهاء الرصيد:**
```
┌─────────────────────────────────────┐
│        ⚠️ تحذير                    │
├─────────────────────────────────────┤
│ رصيدك أوشك على الانتهاء!          │
│ متبقي: 0.5GB فقط                  │
│                                     │
│ هل تريد شراء رصيد إضافي؟          │
│                                     │
│ [لاحقاً]              [شراء رصيد] │
└─────────────────────────────────────┘
```

### **عند انتهاء الرصيد:**
```
┌─────────────────────────────────────┐
│        🔴 انتهى الرصيد             │
├─────────────────────────────────────┤
│ تم استنفاد رصيد الرفع بالكامل      │
│ تم تحويلك للمستوى المجاني          │
│                                     │
│ المتاح الآن: 300MB يومياً          │
│                                     │
│ [موافق]              [شراء رصيد]  │
└─────────────────────────────────────┘
```

---

## 🛠️ **التحديثات التقنية:**

### **نماذج البيانات الجديدة:**

#### **SubscriptionTier (محدث):**
```kotlin
enum class SubscriptionTier(
    val maxMembers: Int,
    val priceUSD: Double,
    val uploadLimitGB: Double, // رصيد الرفع
    val dailyUploadMB: Double  // الحد اليومي للمجاني
) {
    FREE(4, 0.0, 0.0, 300.0),
    PREMIUM(10, 4.99, 15.0, 0.0),
    UNLIMITED(Int.MAX_VALUE, 9.99, 30.0, 0.0)
}
```

#### **UserSubscription (محدث):**
```kotlin
data class UserSubscription(
    val userId: String,
    val tier: SubscriptionTier,
    val remainingUploadGB: Double, // الرصيد المتبقي
    val dailyUploadUsedMB: Double, // المستخدم اليوم
    val lastDailyReset: Long       // آخر إعادة تعيين
)
```

### **دوال جديدة:**
- `canUploadVideo(sizeInMB)` - فحص إمكانية الرفع
- `consumeUploadQuota(sizeInMB)` - استهلاك الرصيد
- `resetDailyQuota()` - إعادة تعيين الحد اليومي
- `getRemainingQuotaText()` - نص الرصيد المتبقي
- `getUsagePercentage()` - نسبة الاستهلاك

---

## 🎮 **سيناريوهات الاستخدام:**

### **1. مستخدم مجاني يرفع فيديو:**
1. **فحص الحد اليومي**: 150MB متبقي من 300MB
2. **رفع فيديو 100MB**: نجح ✅
3. **تحديث العداد**: 50MB متبقي اليوم
4. **في اليوم التالي**: إعادة تعيين لـ 300MB

### **2. مستخدم بريميوم ينفد رصيده:**
1. **الرصيد الحالي**: 0.2GB متبقي من 15GB
2. **محاولة رفع فيديو 500MB**: فشل ❌
3. **رسالة خطأ**: "الرصيد غير كافي"
4. **خيارات**: شراء رصيد إضافي أو انتظار

### **3. انتهاء الرصيد تلقائياً:**
1. **رفع فيديو أخير**: استنفاد الرصيد
2. **تحويل تلقائي**: للمستوى المجاني
3. **إشعار للمستخدم**: "انتهى الرصيد"
4. **المتاح الآن**: 300MB يومياً

---

## 📁 **الملفات المحدثة:**

### **النماذج:**
- `Subscription.kt` (محدث) - نظام الرصيد الجديد

### **المستودعات:**
- `SubscriptionRepository.kt` (محدث) - دوال استهلاك الرصيد

### **ViewModels:**
- `SubscriptionViewModel.kt` (محدث) - منطق الرصيد

### **الشاشات:**
- `HomeScreen.kt` (محدث) - عرض الرصيد
- `SubscriptionScreen.kt` (محدث) - شريط التقدم
- `GroupCreationDialog.kt` (محدث) - خيارات محسنة

---

## 🎉 **المزايا الجديدة:**

✅ **نظام رصيد واضح** بدلاً من الاشتراك الشهري  
✅ **عرض الرصيد المتبقي** في الشاشة الرئيسية  
✅ **مؤشرات بصرية** لنسبة الاستهلاك  
✅ **تحويل تلقائي** للمجاني عند انتهاء الرصيد  
✅ **إعادة تعيين يومية** للمستوى المجاني  
✅ **حوار إنشاء محسن** مع خيارات واضحة  
✅ **رسائل تحذيرية** عند اقتراب انتهاء الرصيد  

**النظام الآن أكثر وضوحاً وعدالة للمستخدمين!** 🚀

## 📱 **ملف APK الجديد:**
**`anime-app-DATA-BASED-SUBSCRIPTION-debug.apk`** - جاهز للتثبيت والاختبار!
