package com.newt.anime.ui.components

import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import android.app.Activity
import android.content.pm.ActivityInfo
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.ui.PlayerView
import com.newt.anime.data.models.VideoSession
import kotlinx.coroutines.delay

@Composable
fun ExoVideoPlayer(
    videoSession: VideoSession,
    isOwner: Boolean,
    onPlayPause: (Boolean) -> Unit,
    onSyncAction: (String, Long) -> Unit = { _, _ -> }, // دالة التزامن الجديدة
    onFullscreenToggle: () -> Unit = {}, // دالة تبديل الشاشة الكاملة
    viewersCount: Int = 0,
    onViewersClick: () -> Unit = {},
    onSendMessage: (String) -> Unit = {},
    onSendEmoji: (String) -> Unit = {},
    chatMessages: List<com.newt.anime.data.models.ChatMessage> = emptyList(),
    emojiReactions: List<com.newt.anime.data.models.EmojiReaction> = emptyList(),
    currentUserId: String = "",
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val activity = context as Activity
    var isLoading by remember { mutableStateOf(true) }
    var hasError by remember { mutableStateOf(false) }
    var showControls by remember { mutableStateOf(false) }
    var isPlaying by remember { mutableStateOf(videoSession.isPlaying) }
    var isMuted by remember { mutableStateOf(false) }
    var exoPlayer by remember { mutableStateOf<ExoPlayer?>(null) }
    var lastSyncTimestamp by remember { mutableStateOf(0L) }
    var isFullscreen by remember { mutableStateOf(false) }
    var currentPosition by remember { mutableStateOf(0L) }
    var duration by remember { mutableStateOf(0L) }
    var isDragging by remember { mutableStateOf(false) }

    
    // إخفاء التحكم تلقائياً بعد 3 ثواني
    LaunchedEffect(showControls) {
        if (showControls) {
            delay(3000)
            showControls = false
        }
    }
    
    // ✅ تزامن محسن - منع التشغيل التلقائي نهائياً
    LaunchedEffect(videoSession.syncTimestamp, videoSession.syncCommand) {
        if (videoSession.syncTimestamp <= lastSyncTimestamp) return@LaunchedEffect
        lastSyncTimestamp = videoSession.syncTimestamp

        // فقط للمشاهدين
        if (!isOwner) {
            exoPlayer?.let { player ->
                android.util.Log.d("ExoPlayer", "🔍 VIEWER SYNC: command=${videoSession.syncCommand}, ownerPlaying=${videoSession.isPlaying}")

                when (videoSession.syncCommand) {
                    "force_play" -> {
                        player.seekTo(videoSession.currentPosition)
                        kotlinx.coroutines.delay(100)
                        player.play()
                        isPlaying = true
                        android.util.Log.d("ExoPlayer", "▶️ FORCE PLAY")
                    }
                    "force_pause" -> {
                        player.seekTo(videoSession.currentPosition)
                        kotlinx.coroutines.delay(50)
                        player.pause()
                        isPlaying = false
                        android.util.Log.d("ExoPlayer", "🛑 FORCE PAUSE")
                    }
                    "position_update" -> {
                        val diff = kotlin.math.abs(videoSession.currentPosition - player.currentPosition)
                        if (diff > 2000) {
                            player.seekTo(videoSession.currentPosition)
                        }
                    }
                }
            }
        }
    }

    // ✅ تحديث موضع المالك - منع الإرسال أثناء الإيقاف
    LaunchedEffect(exoPlayer, isOwner) {
        while (true) {
            exoPlayer?.let { player ->
                if (isOwner) {
                    val newPosition = player.currentPosition
                    val newDuration = player.duration.takeIf { it > 0 } ?: 1L

                    // تحديث الموضع المحلي دائماً
                    if (kotlin.math.abs(newPosition - currentPosition) > 300) {
                        currentPosition = newPosition
                        duration = newDuration

                        // ✅ إرسال position_update فقط إذا كان الفيديو يشتغل
                        if (player.isPlaying) {
                            onSyncAction("position_update", newPosition)
                            android.util.Log.d("ExoPlayer", "📍 OWNER PLAYING: Position update sent: $newPosition")
                        } else {
                            android.util.Log.d("ExoPlayer", "⏸️ OWNER PAUSED: No position update sent (keeping paused)")
                        }
                    }
                } else if (!isOwner) {
                    currentPosition = videoSession.currentPosition
                    duration = player.duration.takeIf { it > 0 } ?: 1L
                }
                delay(1000)
            }
        }
    }

    // إخفاء الأزرار تلقائياً بعد 3 ثواني مثل YouTube
    LaunchedEffect(showControls) {
        if (showControls) {
            delay(3000) // 3 ثواني
            showControls = false
        }
    }

    // تأثير الشاشة الكاملة - إخفاء/إظهار الأشرطة فقط
    LaunchedEffect(isFullscreen) {
        val windowInsetsController = WindowCompat.getInsetsController(activity.window, activity.window.decorView)

        if (isFullscreen) {
            // إخفاء شريط الحالة وشريط التنقل
            windowInsetsController.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())

            android.util.Log.d("ExoPlayer", "📺 Fullscreen mode activated - hiding system bars")
        } else {
            // إظهار شريط الحالة وشريط التنقل
            windowInsetsController.show(WindowInsetsCompat.Type.systemBars())

            android.util.Log.d("ExoPlayer", "📱 Normal mode activated - showing system bars")
        }
    }

    Box(
        modifier = if (isFullscreen) {
            Modifier
                .fillMaxSize()
                .background(Color.Black)
                .clickable { showControls = !showControls }
        } else {
            modifier
                .background(Color.Black)
                .clickable { showControls = !showControls }
        }
    ) {
        if (videoSession.videoUrl.isNotEmpty()) {
            // ExoPlayer
            AndroidView(
                factory = { ctx ->
                    PlayerView(ctx).apply {
                        useController = false // نستخدم تحكم مخصص
                        
                        // إعداد ExoPlayer بسيط بدون تحكم في الجودة
                        val player = ExoPlayer.Builder(ctx)
                            .build()
                        this.player = player
                        exoPlayer = player
                        
                        // إعداد الفيديو مع حماية
                        try {
                            if (videoSession.videoUrl.isNotEmpty() && videoSession.videoUrl != "") {
                                val mediaItem = MediaItem.fromUri(Uri.parse(videoSession.videoUrl))
                                player.setMediaItem(mediaItem)
                                player.prepare()
                                android.util.Log.d("ExoVideoPlayer", "✅ Video prepared: ${videoSession.title}")
                            } else {
                                android.util.Log.d("ExoVideoPlayer", "⚠️ No video URL - empty group")
                            }
                        } catch (e: Exception) {
                            android.util.Log.e("ExoVideoPlayer", "❌ Error preparing video: ${e.message}")
                        }
                        
                        // معالج الأحداث
                        player.addListener(object : com.google.android.exoplayer2.Player.Listener {
                            override fun onPlaybackStateChanged(playbackState: Int) {
                                when (playbackState) {
                                    ExoPlayer.STATE_READY -> {
                                        isLoading = false
                                        hasError = false
                                        android.util.Log.d("ExoPlayer", "Video ready")
                                    }
                                    ExoPlayer.STATE_BUFFERING -> {
                                        android.util.Log.d("ExoPlayer", "Video buffering")
                                    }
                                    ExoPlayer.STATE_ENDED -> {
                                        android.util.Log.d("ExoPlayer", "Video ended")
                                    }
                                }
                            }
                            
                            override fun onIsPlayingChanged(playing: Boolean) {
                                // تحديث الحالة بشكل طبيعي
                                isPlaying = playing
                                android.util.Log.d("ExoPlayer", "Playing state changed: $playing")
                            }
                        })
                        
                        // منع التشغيل التلقائي عند تحميل الفيديو فقط
                        player.pause()
                        isPlaying = false

                        // للمالك: تزامن الموضع فقط بدون تشغيل
                        if (isOwner) {
                            if (videoSession.currentPosition > 0) {
                                player.seekTo(videoSession.currentPosition)
                                android.util.Log.d("ExoPlayer", "👑 OWNER: Synced to saved position: ${videoSession.currentPosition}")
                            }
                            android.util.Log.d("ExoPlayer", "👑 OWNER: Video ready - waiting for manual play")
                        } else {
                            // للمشاهدين: تزامن موضع الفيديو أولاً
                            if (videoSession.currentPosition > 0) {
                                player.seekTo(videoSession.currentPosition)
                                android.util.Log.d("ExoPlayer", "👁️ VIEWER: Synced to position: ${videoSession.currentPosition}")
                            }

                            // تشغيل فقط إذا كان المالك قد بدأ التشغيل مرة واحدة على الأقل
                            if (videoSession.isPlaying && videoSession.hasStarted) {
                                player.play()
                                isPlaying = true
                                android.util.Log.d("ExoPlayer", "👁️ VIEWER: Video started - following owner")
                            } else {
                                android.util.Log.d("ExoPlayer", "👁️ VIEWER: Video ready but waiting for owner to start")
                            }
                        }
                        
                        // النقر لإظهار التحكم (للمالك فقط)
                        if (isOwner) {
                            setOnClickListener {
                                showControls = !showControls
                                android.util.Log.d("ExoPlayer", "Controls toggled: $showControls")
                            }
                        } else {
                            // للمشاهدين: النقر فقط لإظهار شريط التقدم
                            setOnClickListener {
                                showControls = !showControls
                                android.util.Log.d("ExoPlayer", "Progress bar toggled for viewer: $showControls")
                            }
                        }
                    }
                },
                modifier = Modifier.fillMaxSize()
            )
        }
        
        // مؤشر التحميل
        if (isLoading && videoSession.videoUrl.isNotEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.8f)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(
                        color = Color.Red,
                        strokeWidth = 4.dp,
                        modifier = Modifier.size(60.dp)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "جاري تحميل الفيديو...",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        
        // رسالة خطأ
        if (hasError) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.8f)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "❌",
                        fontSize = 48.sp,
                        color = Color.Red
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "خطأ في تحميل الفيديو",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        
        // أزرار التحكم في وسط الشاشة للمالك فقط
        if (videoSession.videoUrl.isNotEmpty() && !isLoading && !hasError && showControls && isOwner) {
            Row(
                modifier = Modifier
                    .align(Alignment.Center)
                    .background(
                        Color.Black.copy(alpha = 0.7f),
                        RoundedCornerShape(50.dp)
                    )
                    .padding(horizontal = 24.dp, vertical = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(24.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // تراجع 10 ثواني
                YouTubeButton(
                    icon = Icons.Default.KeyboardArrowLeft,
                    onClick = {
                        exoPlayer?.let { player ->
                            val currentPos = player.currentPosition
                            val newPosition = maxOf(0, currentPos - 10000) // 10 ثواني
                            player.seekTo(newPosition)
                            currentPosition = newPosition
                            onSyncAction("seek", newPosition)
                            android.util.Log.d("ExoPlayer", "⏪ OWNER: Seek backward 10s from $currentPos to: $newPosition")
                        }
                    }
                )

                // 🎮 زر تحكم جديد - صغير ودقيق
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .background(Color.Black.copy(alpha = 0.8f), CircleShape)
                        .clickable {
                            exoPlayer?.let { player ->
                                val pos = player.currentPosition
                                if (player.isPlaying) {
                                    player.pause()
                                    isPlaying = false
                                    onSyncAction("force_pause", pos)
                                    android.util.Log.d("ExoPlayer", "🛑 FORCE PAUSE: $pos")
                                } else {
                                    player.play()
                                    isPlaying = true
                                    onSyncAction("force_play", pos)
                                    android.util.Log.d("ExoPlayer", "▶️ FORCE PLAY: $pos")
                                }
                            }
                        },
                    contentAlignment = Alignment.Center
                ) {
                    if (isPlaying) {
                        // أيقونة إيقاف بسيطة
                        Row(horizontalArrangement = Arrangement.spacedBy(2.dp)) {
                            Box(Modifier.size(3.dp, 14.dp).background(Color.White))
                            Box(Modifier.size(3.dp, 14.dp).background(Color.White))
                        }
                    } else {
                        // أيقونة تشغيل بسيطة
                        Icon(
                            Icons.Default.PlayArrow,
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }

                // تقديم 10 ثواني
                YouTubeButton(
                    icon = Icons.Default.KeyboardArrowRight,
                    onClick = {
                        exoPlayer?.let { player ->
                            val currentPos = player.currentPosition
                            val newPosition = minOf(duration, currentPos + 10000) // 10 ثواني
                            player.seekTo(newPosition)
                            currentPosition = newPosition
                            onSyncAction("seek", newPosition)
                            android.util.Log.d("ExoPlayer", "⏩ OWNER: Seek forward 10s from $currentPos to: $newPosition")
                        }
                    }
                )
            }
        }

        // شريط التقدم في الأسفل - للجميع عند الضغط على الشاشة
        if (videoSession.videoUrl.isNotEmpty() && !isLoading && !hasError && showControls) {
            Column(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .background(
                        androidx.compose.ui.graphics.Brush.verticalGradient(
                            colors = listOf(
                                Color.Transparent,
                                Color.Black.copy(alpha = 0.8f)
                            )
                        )
                    )
                    .padding(16.dp)
            ) {
                // شريط التقدم
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = formatTime(currentPosition),
                        color = Color.White,
                        fontSize = 12.sp,
                        modifier = Modifier.padding(end = 8.dp)
                    )

                    if (isOwner) {
                        // شريط تحكم للمالك
                        Slider(
                            value = if (duration > 0) currentPosition.toFloat() / duration.toFloat() else 0f,
                            onValueChange = { value ->
                                isDragging = true
                                val newPosition = (value * duration).toLong()
                                currentPosition = newPosition
                            },
                            onValueChangeFinished = {
                                isDragging = false
                                exoPlayer?.let { player ->
                                    // التأكد من أن الموضع صحيح قبل الإرسال
                                    val targetPosition = currentPosition
                                    player.seekTo(targetPosition)
                                    onSyncAction("seek", targetPosition)
                                    android.util.Log.d("ExoPlayer", "🎮 OWNER: Slider seeked to: $targetPosition")
                                }
                            },
                            modifier = Modifier.weight(1f),
                            colors = SliderDefaults.colors(
                                thumbColor = Color.Red,
                                activeTrackColor = Color.Red,
                                inactiveTrackColor = Color.White.copy(alpha = 0.3f)
                            )
                        )
                    } else {
                        // شريط عرض فقط للمشاهدين
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(4.dp)
                                .background(
                                    Color.White.copy(alpha = 0.3f),
                                    RoundedCornerShape(2.dp)
                                )
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxHeight()
                                    .fillMaxWidth(
                                        if (duration > 0) currentPosition.toFloat() / duration.toFloat() else 0f
                                    )
                                    .background(
                                        Color.Red,
                                        RoundedCornerShape(2.dp)
                                    )
                            )
                        }
                    }

                    Text(
                        text = formatTime(duration),
                        color = Color.White,
                        fontSize = 12.sp,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }

                // فقط شريط التقدم - الأزرار في الوسط
            }
        }
        


        // عداد المشاهدين داخل الفيديو - في اليمين العلوي
        if (videoSession.videoUrl.isNotEmpty() && showControls) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp)
                    .background(
                        Color.Black.copy(alpha = 0.7f),
                        RoundedCornerShape(20.dp)
                    )
                    .clickable { onViewersClick() }
                    .padding(horizontal = 12.dp, vertical = 6.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "👁️",
                        fontSize = 14.sp,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "$viewersCount",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.White
                    )
                }
            }
        }



        // مؤشر الشاشة الكاملة للمالك
        if (isOwner && isFullscreen && videoSession.videoUrl.isNotEmpty()) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(16.dp)
                    .background(
                        Color.Black.copy(alpha = 0.7f),
                        RoundedCornerShape(20.dp)
                    )
                    .padding(horizontal = 12.dp, vertical = 6.dp)
            ) {
                Text(
                    text = "📺 شاشة كاملة",
                    color = Color.White,
                    fontSize = 12.sp
                )
            }
        }
        
        // رسالة عدم وجود فيديو
        if (videoSession.videoUrl.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "📺",
                        fontSize = 64.sp,
                        color = Color.White.copy(alpha = 0.5f)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "لا يوجد فيديو حالياً",
                        color = Color.White.copy(alpha = 0.7f),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                    if (isOwner) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "اضغط 'رفع فيديو' لبدء المشاهدة",
                            color = Color.White.copy(alpha = 0.5f),
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }

        // زر الشاشة الكاملة - للجميع (يخفى في الشاشة الكاملة)
        if (videoSession.videoUrl.isNotEmpty() && !isLoading && !hasError && !isFullscreen) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(16.dp)
                    .background(
                        Color.Black.copy(alpha = 0.7f),
                        CircleShape
                    )
                    .clickable { onFullscreenToggle() }
                    .padding(12.dp)
            ) {
                Text(
                    text = "↗",
                    color = Color.White,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }

        // طبقة عرض الرسائل والإيموجي
        if (videoSession.videoUrl.isNotEmpty() && !isLoading && !hasError) {
            MessageOverlay(
                chatMessages = chatMessages,
                emojiReactions = emojiReactions,
                currentUserId = currentUserId,
                modifier = Modifier.fillMaxSize()
            )
        }

        // طبقة الدردشة والإيموجي التفاعلية
        if (videoSession.videoUrl.isNotEmpty() && !isLoading && !hasError) {
            ChatOverlay(
                onSendMessage = onSendMessage,
                onSendEmoji = onSendEmoji,
                chatMessages = chatMessages,
                showControls = showControls,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
    
    // تنظيف ExoPlayer عند الخروج
    DisposableEffect(Unit) {
        onDispose {
            exoPlayer?.release()

            // إظهار شريط الحالة والتنقل عند الخروج
            try {
                val windowInsetsController = WindowCompat.getInsetsController(activity.window, activity.window.decorView)
                windowInsetsController.show(WindowInsetsCompat.Type.systemBars())
            } catch (e: Exception) {
                android.util.Log.e("ExoPlayer", "Error showing system bars: ${e.message}")
            }
        }
    }
}

@Composable
private fun YouTubeButton(
    icon: ImageVector,
    onClick: () -> Unit,
    isMain: Boolean = false,
    text: String = "",
    isPause: Boolean = false,
    modifier: Modifier = Modifier
) {
    val buttonSize = if (isMain) 64.dp else 56.dp
    val iconSize = if (isMain) 36.dp else 24.dp

    Box(
        modifier = modifier
            .size(buttonSize)
            .background(
                Color.Black.copy(alpha = 0.7f),
                CircleShape
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (isPause) {
                // أيقونة Pause مخصصة مثل YouTube (خطين عموديين)
                Row(
                    horizontalArrangement = Arrangement.spacedBy(3.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .width(3.dp)
                            .height(if (isMain) 20.dp else 16.dp)
                            .background(Color.White, RoundedCornerShape(1.dp))
                    )
                    Box(
                        modifier = Modifier
                            .width(3.dp)
                            .height(if (isMain) 20.dp else 16.dp)
                            .background(Color.White, RoundedCornerShape(1.dp))
                    )
                }
            } else {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(iconSize)
                )
            }
            if (text.isNotEmpty()) {
                Text(
                    text = text,
                    color = Color.White,
                    fontSize = 8.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

// دالة تنسيق الوقت مثل YouTube
private fun formatTime(timeMs: Long): String {
    val totalSeconds = timeMs / 1000
    val minutes = totalSeconds / 60
    val seconds = totalSeconds % 60
    return String.format("%d:%02d", minutes, seconds)
}
