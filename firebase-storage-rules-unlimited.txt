rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // قواعد رفع الفيديو بدون حد للحجم
    match /videos/{userId}/{videoId} {
      allow read: if request.auth != null; // قراءة للجميع
      allow write: if request.auth != null 
        && request.auth.uid == userId
        && request.resource.contentType.matches('video/.*'); // فيديو فقط، بدون حد للحجم
    }
    
    // مجلد الفيديوهات المشتركة
    match /shared-videos/{videoId} {
      allow read: if request.auth != null; // قراءة للجميع
      allow write: if request.auth != null
        && request.resource.contentType.matches('video/.*'); // فيديو فقط، بدون حد للحجم
    }
    
    // مجلد عام للفيديوهات (اختياري)
    match /public-videos/{videoId} {
      allow read: if true; // قراءة للجميع حتى بدون تسجيل
      allow write: if request.auth != null
        && request.resource.contentType.matches('video/.*'); // فيديو فقط، بدون حد للحجم
    }
  }
}
